import os
from contextlib import asynccontextmanager

import fastapi_cdn_host
import uvicorn
from dotenv import load_dotenv
from fastapi import FastAPI
from starlette.middleware.cors import CORSMiddleware

load_dotenv()
from src.api.router import router
from src.cache.init_cache import init_all_caches

@asynccontextmanager
async def lifespan(app_param: FastAPI):
    # 应用启动前的初始化操作
    init_all_caches()

    # 利用 yield 分隔启动和关闭逻辑
    yield

    # 应用关闭前的清理逻辑（如果有）
    # 例如清理缓存或断开数据库连接
    pass

app = FastAPI(lifespan=lifespan)
fastapi_cdn_host.patch_docs(app)

# 仅当 deploy_mode 环境变量的值为 'local' 时添加中间件
if os.getenv('deploy_mode') == 'local':
    app.add_middleware(
        CORSMiddleware,  # type: ignore
        allow_origins=['*'],
        allow_credentials=True,
        allow_methods=['*'],
        allow_headers=['*'],
    )

app.include_router(router)

def start():
    """
    启动服务
    """
    uvicorn.run(
        app,
        host="0.0.0.0",
        reload=os.getenv('deploy_reload') == 'local',
        port=int(os.getenv("API_PORT", 2189)),
    )

if __name__ == '__main__':
    start()