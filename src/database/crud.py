import re
from datetime import datetime
from typing import Optional, Tuple, List, Dict, Any
import uuid

from nanoid import generate
from sqlalchemy import select, func
from sqlalchemy import text

from src.common.logger import logger
from src.database.db_mysql import db_session
from src.database.models.entity import TSysLog, GPTEncodingCache, GPTEncodingCacheConfig, PromptTemplate, AiCallLog

SYS_LOG_TABLE_NAME = TSysLog.__tablename__

GPT_ENCODING_CACHE_CONFIG_TABLE = GPTEncodingCacheConfig.__tablename__


def insert_sys_log(trace_id, md5, request_ip, method, request_uri, operate_status, exception_detail, create_time, elapsed_time,
                   tenant_id, study_id):
    try:
        insert_stmt = text(
            f"""
        INSERT INTO {SYS_LOG_TABLE_NAME} 
        (id, pdf_md5, request_ip, method, request_uri, operate_status, exception_detail, create_time, elapsed_time, tenant_id, study_id) 
        VALUES 
        (:id, :pdf_md5, :request_ip, :method, :request_uri, :operate_status, :exception_detail, :create_time, :elapsed_time, :tenant_id, :study_id)
    """)
        parameters = {
            'id': trace_id,
            'pdf_md5': md5,
            'request_ip': request_ip,
            'method': method,
            'request_uri': request_uri,
            'operate_status': operate_status,
            'exception_detail': exception_detail,
            'create_time': create_time,
            'elapsed_time': elapsed_time,
            'tenant_id': tenant_id,
            'study_id': study_id
        }
        with db_session() as session:
            session.execute(insert_stmt, parameters)
            session.commit()
    except (Exception,) as e:
        logger.warning("{id} | {message}", id=trace_id, message=e)


def insert_encoding_cache(trace_id, data_list, tenant_id, protocol_number):
    try:
        if not data_list:
            return
            
        filtered_data = []
        for item in data_list:
            original_value = str(item.get('original_value', '')).strip()
            encoded_values = item.get('encoded_value', '')
            field_name = item.get('field_name', '')
            
            # 如果 encoded_value 是字符串，转换为列表
            if isinstance(encoded_values, str):
                encoded_values = [encoded_values]
            elif not isinstance(encoded_values, list):
                continue
                
            # 过滤空值并展开每个 encoded_value
            if original_value:
                for encoded_value in encoded_values:
                    encoded_value = str(encoded_value).strip()
                    # 添加格式验证
                    if encoded_value and is_valid_encoded_value(field_name, encoded_value):
                        filtered_data.append({
                            'field_name': field_name,
                            'original_value': original_value,
                            'encoded_value': encoded_value
                        })
        
        if not filtered_data:
            logger.warning(
                "{trace_id} | No valid data after filtering empty values", 
                trace_id=trace_id
            )
            return
            
        current_time = datetime.now()

        with db_session() as session:
            # 1. 查询已存在的数据
            existing_records = session.query(GPTEncodingCacheConfig).filter(
                GPTEncodingCacheConfig.tenant_id == tenant_id,
                GPTEncodingCacheConfig.study_id == protocol_number
            ).all()
            
            # 创建已存在记录的集合，用于去重
            existing_keys = {
                (
                    record.field_name,
                    record.original_value,
                    record.encoded_value,
                    record.tenant_id,
                    record.study_id
                ) 
                for record in existing_records
            }
            
            # 2. 处理 gpt_encoding_cache 表数据
            for index, item in enumerate(filtered_data, start=1):
                cache_record = GPTEncodingCache(
                    id=f"{trace_id}-{index}",
                    field_name=item['field_name'],
                    original_value=item['original_value'],
                    encoded_value=item['encoded_value'],
                    tenant_id=tenant_id,
                    study_id=protocol_number,
                    created_at=current_time,
                    updated_at=current_time
                )
                session.add(cache_record)

            # 3. 准备 gpt_encoding_cache_config 表数据并去重
            new_records = []
            for item in filtered_data:
                check_key = (
                    item['field_name'],
                    item['original_value'],
                    item['encoded_value'],
                    tenant_id,
                    protocol_number
                )
                
                # 如果记录不存在，则添加
                if check_key not in existing_keys:
                    existing_keys.add(check_key)  # 添加到已存在集合中，避免重复
                    config_record = GPTEncodingCacheConfig(
                        id=generate(size=20),
                        field_name=item['field_name'],
                        original_value=item['original_value'],
                        encoded_value=item['encoded_value'],
                        tenant_id=tenant_id,
                        study_id=protocol_number,
                        created_at=current_time,
                        updated_at=current_time
                    )
                    new_records.append(config_record)

            # 批量插入新记录
            if new_records:
                session.bulk_save_objects(new_records)
            session.commit()

        logger.info(
            "{trace_id} | Cache inserted successfully | New records: {new_count} | Total filtered: {filtered_count} | Original: {original_count}", 
            trace_id=trace_id,
            new_count=len(new_records),
            filtered_count=len(filtered_data),
            original_count=len(data_list)
        )
    except Exception as e:
        logger.warning("{id} | {message}", id=trace_id, message=str(e))


def query_encoded_data(data_list, tenant_id, study_id = None):
    try:
        encoded_mapping = {}
        logger.info(
            "开始查询编码数据 | 租户ID: {tenant_id} | 研究ID: {study_id} | 输入数据量: {input_count}",
            tenant_id=tenant_id,
            study_id=study_id,
            input_count=len(data_list)
        )
        
        for data in data_list:
            for field_name, original_values in data.items():
                if study_id is None:
                    stmt = text(f"""
                            SELECT original_value, encoded_value
                            FROM {GPT_ENCODING_CACHE_CONFIG_TABLE}
                            WHERE field_name = :field_name AND original_value IN :original_values and study_id = :study_id and tenant_id = :tenant_id
                        """)
                else:
                    stmt = text(f"""
                                SELECT original_value, encoded_value
                                FROM {GPT_ENCODING_CACHE_CONFIG_TABLE}
                                WHERE field_name = :field_name AND original_value IN :original_values and study_id = :study_id and tenant_id = :tenant_id
                            """)
                results, result_dict = [], {}
                keys = ("original_value", "encoded_value")
                with db_session() as session:
                    fetch_result = session.execute(stmt, {'field_name': field_name, 'original_values': original_values, 'study_id': study_id, 'tenant_id': tenant_id}).fetchall()
                
                # 记录查询结果
                logger.debug(
                    "字段查询结果 | 字段名: {field_name} | 查询到记录数: {result_count}",
                    field_name=field_name,
                    result_count=len(fetch_result)
                )
                
                for row in fetch_result:
                    results.append(dict(zip(keys, row)))
                for item in results:
                    key, value = item['original_value'], item['encoded_value']
                    # 修改：为每个key维护一个值列表
                    if key not in result_dict:
                        result_dict[key] = [value] if value else []
                    else:
                        # 如果key已存在，添加新的值到列表中
                        if value:  # 只添加非空值
                            result_dict[key].append(value)

                    # 记录日志时精确指明是添加到列表
                    logger.debug(
                        "添加编码映射 | 字段名: {field_name} | 原始值: {original_value} | 编码值: {encoded_value} | 当前列表长度: {list_length}",
                        field_name=field_name,
                        original_value=key,
                        encoded_value=value,
                        list_length=len(result_dict.get(key, []))
                    )

                # 过滤掉空列表
                result_dict = {key: values for key, values in result_dict.items() if values}
                encoded_mapping[field_name] = result_dict

        logger.info(
            "编码数据查询完成 | 租户ID: {tenant_id} | 研究ID: {study_id} | 成功映射字段数: {field_count}",
            tenant_id=tenant_id,
            study_id=study_id,
            field_count=len(encoded_mapping)
        )
        return encoded_mapping
    except (Exception,) as e:
        logger.warning(
            "编码数据查询失败 | 租户ID: {tenant_id} | 研究ID: {study_id} | 错误: {error}",
            tenant_id=tenant_id,
            study_id=study_id,
            error=str(e)
        )
        keys = set(key for item in data_list for key in item.keys())
        return {key: {} for key in keys}


def is_valid_encoded_value(field_name, encoded_value):
    """
    验证特定field_name的encoded_value是否符合格式要求
    格式要求：数字[.数字]*(中文或英文描述)
    例如：10021114(甲状腺功能减退症) 或 000680.02.001(Levothyroxine sodium)
    
    Args:
        field_name (str): 字段名称
        encoded_value (str): 需要验证的值
    
    Returns:
        bool: 是否符合格式要求
    """
    # 需要特殊验证的字段名列表
    special_fields = ['product_name','labtest','Ind_Table_ind_reptd','desc_reptd','rel_hist_table_pat_hist_rptd','CSDD_cause_reptd']
    
    # 如果不是特殊字段，直接返回True
    if field_name not in special_fields:
        return True

    pattern = r'^\d+(?:[.]\d+)*\([^)]+\)$'
    
    return bool(re.match(pattern, str(encoded_value)))


def get_prompt_templates_by_tenant(
        tenant_id: str,
        template_key: Optional[str] = None
) -> List[PromptTemplate]:
    """根据租户ID获取提示模板列表"""
    with db_session() as session:
        query = select(PromptTemplate).filter(PromptTemplate.company_code == tenant_id)

        if template_key:
            query = query.filter(PromptTemplate.template_key == template_key)
            
        # 打印带参数的SQL语句
        compiled = query.compile(compile_kwargs={"literal_binds": True})
        print("Generated SQL with params:", str(compiled))

        templates = session.scalars(query).all()
        return templates


def update_prompt_template_content(
        tenant_id: str,
        template_key: str,
        content: str,
        language: str
) -> Optional[PromptTemplate]:
    """更新提示模板内容"""
    with db_session() as session:
        template = session.query(PromptTemplate).filter(
            PromptTemplate.company_code == tenant_id,
            PromptTemplate.template_key == template_key,
            PromptTemplate.language == language
        ).first()

        if not template:
            return None

        template.content = content
        template.updated_at = datetime.now()

        session.commit()
        session.refresh(template)
        return template


def create_prompt_template(
        tenant_id: str,
        template_key: str,
        content: str,
        language: str,
        file_path: str
) -> Optional[PromptTemplate]:
    """创建新的提示模板"""
    try:
        with db_session() as session:
            new_template = PromptTemplate(
                company_code=tenant_id,
                language=language,
                template_key=template_key,
                file_path=file_path,
                content=content,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            session.add(new_template)
            session.commit()
            session.refresh(new_template)
            return new_template
    except Exception as e:
        logger.error(f"创建提示模板失败: {str(e)}")
        raise


async def get_study_data(study_num: str, tenant_id: str) -> str:
    """
    根据study_num和tenant_id从数据库中获取研究相关数据
    
    Args:
        study_num: 研究编号
        tenant_id: 租户ID
        
    Returns:
        str: 查询到的研究数据字符串
    """
    try:
        query = text("""
            SELECT study_data 
            FROM study_info 
            WHERE study_num = :study_num 
            AND tenant_id = :tenant_id
            LIMIT 1
        """)
        
        with db_session() as session:
            result = session.execute(
                query,
                {
                    'study_num': study_num,
                    'tenant_id': tenant_id
                }
            ).scalar()
            
            return result if result else ""
            
    except Exception as e:
        logger.warning(
            "获取研究数据失败 | study_num: {study_num} | tenant_id: {tenant_id} | error: {error}",
            study_num=study_num,
            tenant_id=tenant_id,
            error=str(e)
        )
        return ""


def insert_ai_call_log(trace_id=None, tenant_id=None, protocol_number=None, status=0, error_message=None,
                   duration=None, input_tokens=0, output_tokens=0, reasoning_tokens=0, model_name=None, tag=None, md5=None, language=None, 
                   user_name=None, user_id=None, langfuse_trace_id=None, langfuse_observation_id=None, endpoint_name=None):
    """
    插入AI调用日志
    Args:
        trace_id: 追踪ID
        tenant_id: 租户ID
        protocol_number: 协议编号
        status: 调用状态（0-成功，1-失败）
        error_message: 错误信息
        duration: 调用耗时（毫秒）
        input_tokens: 输入token数
        output_tokens: 输出token数
        reasoning_tokens: 推理token数（Claude扩展思考模式下的思考token）
        model_name: 模型名称
        tag: 操作分类
        md5: 文件MD5
        language: 语言
        user_name: 用户名
        user_id: 用户ID
        langfuse_trace_id: Langfuse跟踪ID
        langfuse_observation_id: Langfuse观察ID
        endpoint_name: 接入点名称
    Returns:
        None
    """
    try:
        # 确保token参数都是非负整数
        input_tokens = max(0, int(input_tokens)) if input_tokens is not None else 0
        output_tokens = max(0, int(output_tokens)) if output_tokens is not None else 0
        reasoning_tokens = max(0, int(reasoning_tokens)) if reasoning_tokens is not None else 0
        
        # 如果输入、输出和推理token都为0，则不记录日志
        if input_tokens == 0 and output_tokens == 0 and reasoning_tokens == 0:
            logger.debug(
                "跳过记录AI调用日志 | input_tokens=0, output_tokens=0, reasoning_tokens=0 | trace_id={trace_id}, model={model}",
                trace_id=trace_id,
                model=model_name
            )
            return
            
        with db_session() as session:
            ai_call_log = AiCallLog(
                id=str(uuid.uuid4()).replace('-', ''),
                trace_id=trace_id,
                tenant_id=tenant_id,
                protocol_number=protocol_number,
                status=status,
                error_message=error_message,
                duration=duration,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                reasoning_tokens=reasoning_tokens,
                model_name=model_name,
                tag=tag,
                md5=md5,
                language=language,
                user_name=user_name,
                user_id=user_id,
                langfuse_trace_id=langfuse_trace_id,
                langfuse_observation_id=langfuse_observation_id,
                endpoint_name=endpoint_name
            )
            session.add(ai_call_log)
            session.commit()
            
            # 记录成功插入日志
            total_tokens = input_tokens + output_tokens + reasoning_tokens
            logger.info(
                "AI调用日志记录成功 | trace_id={trace_id} | 模型={model} | 总tokens={total} | 输入={input} | 输出={output} | 推理={reasoning}",
                trace_id=trace_id,
                model=model_name,
                total=total_tokens,
                input=input_tokens,
                output=output_tokens,
                reasoning=reasoning_tokens
            )
            
    except Exception as e:
        logger.error(
            "插入AI调用日志失败 | trace_id={trace_id} | 模型={model} | 错误={error}",
            trace_id=trace_id,
            model=model_name,
            error=str(e)
        )


def query_ai_call_logs(
    tenant_id: str = None,
    trace_id: str = None,
    model_name: str = None,
    user_id: str = None,
    status: int = None,
    tag: str = None,
    start_time: datetime = None,
    end_time: datetime = None,
    limit: int = 100,
    offset: int = 0
) -> Tuple[List[Dict[str, Any]], int]:
    """
    查询AI调用日志记录
    
    Args:
        tenant_id: 租户ID
        trace_id: 跟踪ID
        model_name: 模型名称
        user_id: 用户ID
        status: 状态码
        tag: 标签
        start_time: 开始时间
        end_time: 结束时间
        limit: 限制返回记录数
        offset: 查询偏移量
        
    Returns:
        Tuple[List[Dict], int]: 查询结果列表和总记录数
    """
    try:
        with db_session() as session:
            # 构建基础查询
            query = select(AiCallLog)
            count_query = select(func.count()).select_from(AiCallLog)
            
            # 添加过滤条件
            if tenant_id:
                query = query.filter(AiCallLog.tenant_id == tenant_id)
                count_query = count_query.filter(AiCallLog.tenant_id == tenant_id)
                
            if trace_id:
                query = query.filter(AiCallLog.trace_id == trace_id)
                count_query = count_query.filter(AiCallLog.trace_id == trace_id)
                
            if model_name:
                query = query.filter(AiCallLog.model_name == model_name)
                count_query = count_query.filter(AiCallLog.model_name == model_name)
                
            if user_id:
                query = query.filter(AiCallLog.user_id == user_id)
                count_query = count_query.filter(AiCallLog.user_id == user_id)
                
            if status is not None:
                query = query.filter(AiCallLog.status == status)
                count_query = count_query.filter(AiCallLog.status == status)
                
            if tag:
                query = query.filter(AiCallLog.tag == tag)
                count_query = count_query.filter(AiCallLog.tag == tag)
                
            if start_time:
                query = query.filter(AiCallLog.created_at >= start_time)
                count_query = count_query.filter(AiCallLog.created_at >= start_time)
                
            if end_time:
                query = query.filter(AiCallLog.created_at <= end_time)
                count_query = count_query.filter(AiCallLog.created_at <= end_time)
            
            # 添加排序、分页
            query = query.order_by(AiCallLog.created_at.desc()).offset(offset).limit(limit)
            
            # 执行查询
            total_count = session.scalar(count_query)
            logs = session.scalars(query).all()
            
            # 转换结果为字典列表
            result = []
            for log in logs:
                log_dict = {
                    "id": log.id,
                    "tenant_id": log.tenant_id,
                    "trace_id": log.trace_id,
                    "md5": log.md5,
                    "language": log.language,
                    "protocol_number": log.protocol_number,
                    "status": log.status,
                    "error_message": log.error_message,
                    "duration": log.duration,
                    "created_at": log.created_at,
                    "updated_at": log.updated_at,
                    "input_tokens": log.input_tokens,
                    "output_tokens": log.output_tokens,
                    "reasoning_tokens": log.reasoning_tokens,
                    "model_name": log.model_name,
                    "user_name": log.user_name,
                    "user_id": log.user_id,
                    "tag": log.tag
                }
                result.append(log_dict)
            
            logger.info(
                "AI调用日志查询成功 | 租户ID: {tenant_id} | 结果数量: {count} | 总记录数: {total}",
                tenant_id=tenant_id,
                count=len(result),
                total=total_count
            )
            
            return result, total_count
            
    except Exception as e:
        logger.error(
            "AI调用日志查询失败 | 租户ID: {tenant_id} | 错误: {error}",
            tenant_id=tenant_id,
            error=str(e)
        )
        return [], 0
