from datetime import datetime

import nanoid
from sqlalchemy import String, DateTime, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ger, BigInteger, SmallInteger
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


class Base(DeclarativeBase):
    id: Mapped[str] = mapped_column(String(21), primary_key=True, default=lambda: nanoid.generate(size=20), nullable=False)
    __abstract__ = True


class TSysLog(Base):
    __tablename__ = "t_sys_log"


class GPTEncodingCache(Base):
    __tablename__ = "gpt_encoding_cache"
    field_name: Mapped[str] = mapped_column(String(255), default="", comment='field_name')
    original_value: Mapped[str] = mapped_column(String(255), default="", comment='original_value')
    encoded_value: Mapped[str] = mapped_column(String(255), default="", comment='encoded_value')
    created_at: Mapped[datetime] = mapped_column(default=datetime.now, comment='created_time')
    updated_at: Mapped[datetime] = mapped_column(default=datetime.now, comment='updated_time')
    tenant_id: Mapped[str] = mapped_column(String(255), default="", comment='tenant_id')
    study_id: Mapped[str] = mapped_column(String(255), default="", comment='study_id')


class GPTEncodingCacheConfig(Base):
    __tablename__ = "gpt_encoding_cache_config"
    
    field_name: Mapped[str] = mapped_column(String(255), nullable=False, comment='field_name')
    original_value: Mapped[str] = mapped_column(String(255), nullable=False, comment='original_value')
    encoded_value: Mapped[str] = mapped_column(String(255), nullable=False, comment='encoded_value')
    created_at: Mapped[datetime] = mapped_column(default=datetime.now, nullable=False, comment='created_time')
    updated_at: Mapped[datetime] = mapped_column(default=datetime.now, nullable=False, comment='updated_time')
    tenant_id: Mapped[str] = mapped_column(String(255), nullable=False, comment='tenant_id')
    study_id: Mapped[str] = mapped_column(String(255), nullable=True, comment='study_id')


class DictionaryMapping(Base):
    __tablename__ = 'dictionary_mapping'
    
    company_code: Mapped[str] = mapped_column(String(50), nullable=False)
    project_code: Mapped[str] = mapped_column(String(50), nullable=False) 
    module: Mapped[str] = mapped_column(String(50), nullable=False)
    placeholder: Mapped[str] = mapped_column(String(50), nullable=False)
    content: Mapped[str] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)
    tenant_id: Mapped[str] = mapped_column(String(50), nullable=True)

class PromptTemplate(Base):
    __tablename__ = 'prompt_templates'
    
    company_code: Mapped[str] = mapped_column(String(50), comment='公司代码')
    tenant_id: Mapped[str] = mapped_column(String(50), comment='公司代码')
    language: Mapped[str] = mapped_column(String(10), comment='语言代码，如cn/en')
    template_key: Mapped[str] = mapped_column(String(100), comment='模板键名')
    file_path: Mapped[str] = mapped_column(String(255), comment='原MD文件路径')
    content: Mapped[str] = mapped_column(Text, comment='模板内容')
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)

class StudyInfo(Base):
    __tablename__ = 'study_info'
    
    tenant_id: Mapped[str] = mapped_column(String(50), nullable=False, comment='租户ID')
    study_num: Mapped[str] = mapped_column(String(100), nullable=False, comment='研究编号')
    study_data: Mapped[str] = mapped_column(Text, comment='研究数据')
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now)


class SystemDict(Base):
    """租户对应的字典表"""
    __tablename__ = "t_system_dict"

    field_name: Mapped[str] = mapped_column(String(255), nullable=True, comment='字段名')
    dict_cn: Mapped[str] = mapped_column(String(255), nullable=True, comment='映射中文字典数据')
    dict_en: Mapped[str] = mapped_column(String(255), nullable=True, comment='映射英文字典数据')
    origin_field: Mapped[str] = mapped_column(String(255), nullable=True, comment='初始数据映射字段')
    mapping_field: Mapped[str] = mapped_column(String(255), nullable=True, comment='映射数据对应字段')
    is_delete: Mapped[bool] = mapped_column(Boolean, nullable=True, default=False, comment='是否删除 1是0否')
    create_time: Mapped[datetime] = mapped_column(DateTime, nullable=True, default=datetime.now, comment='创建时间')
    update_time: Mapped[datetime] = mapped_column(DateTime, nullable=True, default=datetime.now, comment='更新时间')
    create_by: Mapped[str] = mapped_column(String(20), nullable=True, default='', comment='创建人')
    update_by: Mapped[str] = mapped_column(String(20), nullable=True, default='', comment='更新人')
    tenant_id: Mapped[str] = mapped_column(String(100), nullable=True, comment='租户id')

class WhoDrug(Base):
    __tablename__ = 't_report_whodrug'

    drug_code: Mapped[str] = mapped_column(String(50), nullable=False)
    drug_name: Mapped[str] = mapped_column(String(50), nullable=False)

class ReportMeddra(Base):
    __tablename__ = 't_report_meddra'

    llt_code: Mapped[str] = mapped_column(String(50), nullable=False)
    llt_name: Mapped[str] = mapped_column(String(50), nullable=False)

class TenantFeatureConfig(Base):
    """租户功能配置表"""
    __tablename__ = "tenant_feature_config"
    
    tenant_id: Mapped[str] = mapped_column(String(50), nullable=False, comment='租户ID')
    feature_key: Mapped[str] = mapped_column(String(100), nullable=False, comment='功能特性键名')
    feature_value: Mapped[str] = mapped_column(Text, nullable=True, comment='功能特性值')
    description: Mapped[str] = mapped_column(String(500), nullable=True, comment='功能描述')
    enabled: Mapped[bool] = mapped_column(Boolean, nullable=True, default=True, comment='是否启用')
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now, comment='创建时间')
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.now, comment='更新时间')

class AiCallLog(Base):
    """AI调用日志表"""
    __tablename__ = "ai_call_log"
    
    id: Mapped[str] = mapped_column(String(32), primary_key=True, default=lambda: nanoid.generate(size=20), nullable=False, comment='主键ID')
    tenant_id: Mapped[str] = mapped_column(String(32), nullable=True, comment='租户ID')
    trace_id: Mapped[str] = mapped_column(String(40), nullable=True, comment='跟踪ID')
    md5: Mapped[str] = mapped_column(String(32), nullable=True, comment='MD5哈希值')
    language: Mapped[str] = mapped_column(String(10), nullable=True, comment='语言')
    protocol_number: Mapped[str] = mapped_column(String(50), nullable=True, comment='协议编号')
    status: Mapped[int] = mapped_column(SmallInteger, nullable=True, default=0, comment='状态')
    error_message: Mapped[str] = mapped_column(Text, nullable=True, comment='错误信息')
    duration: Mapped[int] = mapped_column(BigInteger, nullable=True, comment='持续时间(毫秒)')
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.now, comment='创建时间')
    updated_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.now, comment='更新时间')
    input_tokens: Mapped[int] = mapped_column(Integer, nullable=True, default=0, comment='输入令牌数')
    output_tokens: Mapped[int] = mapped_column(Integer, nullable=True, default=0, comment='输出令牌数')
    reasoning_tokens: Mapped[int] = mapped_column(Integer, nullable=True, default=0, comment='推理令牌数')
    model_name: Mapped[str] = mapped_column(String(50), nullable=True, comment='模型名称')
    user_name: Mapped[str] = mapped_column(String(50), nullable=True, comment='用户名')
    user_id: Mapped[str] = mapped_column(String(32), nullable=True, comment='用户ID')
    tag: Mapped[str] = mapped_column(String(20), nullable=True, comment='标签')
    langfuse_trace_id: Mapped[str] = mapped_column(String(100), nullable=True, comment='Langfuse跟踪ID')
    langfuse_observation_id: Mapped[str] = mapped_column(String(100), nullable=True, comment='Langfuse观察ID')
    endpoint_name: Mapped[str] = mapped_column(String(100), nullable=True, comment='接入点名称')
