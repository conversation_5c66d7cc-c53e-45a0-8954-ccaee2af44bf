import sys
from sqlalchemy import URL, create_engine
from sqlalchemy.orm import sessionmaker
from src.common.logger import logger
from src.config.database import get_mysql_url


def create_engine_and_session(url: str | URL):
    try:
        engine = create_engine(url, echo=False, future=True, pool_pre_ping=True)
    except Exception as e:
        logger.error('❌ 数据库链接失败 {}', e)
        sys.exit()
    else:
        return sessionmaker(bind=engine, autoflush=False, expire_on_commit=False)


# 创建测试环境数据库会话
db_session = create_engine_and_session(get_mysql_url('test'))

# 创建生产环境数据库会话
prod_db_session = create_engine_and_session(get_mysql_url('prod'))
