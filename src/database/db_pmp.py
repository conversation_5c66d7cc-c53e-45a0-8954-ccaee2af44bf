import sys
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from src.common.logger import logger
from src.config.database import get_pmp_url

def create_engine_and_session(url: str):
    """创建数据库引擎和会话工厂
    
    Args:
        url (str): 数据库连接URL字符串
        
    Returns:
        sessionmaker: 会话工厂
    """
    try:
        engine = create_engine(
            url,
            echo=False,  # 关闭SQL语句输出
            future=True,
            pool_pre_ping=True,
            connect_args={
                'ssl': {},  # 空字典表示不验证SSL
                'connect_timeout': 10
            }
        )
        # 测试连接
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
            conn.commit()
    except Exception as e:
        logger.error('❌ PMP数据库连接失败: {0}'.format(str(e)))
        sys.exit(1)
    else:
        return sessionmaker(bind=engine, autoflush=False, expire_on_commit=False)

# 创建PMP数据库会话
pmp_session = create_engine_and_session(get_pmp_url())