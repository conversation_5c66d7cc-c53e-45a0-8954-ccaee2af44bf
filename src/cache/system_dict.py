from typing import Dict, Optional, Tuple

from loguru import logger

from src.database.db_mysql import db_session
from src.database.models.entity import SystemDict


class SystemDictCache:
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(SystemDictCache, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            # 初始化缓存字典
            # 使用四层嵌套字典: tenant_id -> field_name -> language -> value -> target_value
            self._dict_cache: Dict[str, Dict[str, Dict[str, Dict[str, str]]]] = {}
            self._load_system_dict()
            SystemDictCache._initialized = True

    def _load_system_dict(self):
        """
        从数据库加载所有系统字典数据到内存
        """
        try:
            with db_session() as session:
                dict_items = session.query(SystemDict).filter(
                    SystemDict.is_delete == False
                ).all()

                total_items = 0
                for item in dict_items:
                    if not item.tenant_id or not item.field_name:
                        continue

                    # 初始化租户字典
                    if item.tenant_id not in self._dict_cache:
                        self._dict_cache[item.tenant_id] = {}

                    # 初始化字段字典
                    if item.field_name not in self._dict_cache[item.tenant_id]:
                        self._dict_cache[item.tenant_id][item.field_name] = {
                            'cn': {},  # 中文字典
                            'en': {}   # 英文字典
                        }

                    # 存储中英文映射（分别存储在cn和en层）
                    field_dict = self._dict_cache[item.tenant_id][item.field_name]
                    if item.dict_cn and item.dict_en:
                        # 中文字典：中文 -> 英文
                        field_dict['cn'][item.dict_cn] = item.dict_en
                        # 英文字典：英文 -> 中文
                        field_dict['en'][item.dict_en] = item.dict_cn
                        total_items += 2

            logger.info("成功加载系统字典数据到缓存，共{}个租户，{}个映射项",
                        len(self._dict_cache),
                        total_items)

            # 打印每个租户的字段统计
            for tenant_id, tenant_dict in self._dict_cache.items():
                logger.debug("租户[{}]包含{}个字段",
                             tenant_id,
                             len(tenant_dict))

        except Exception as e:
            logger.error("加载系统字典数据到缓存时发生错误: {}", str(e))

    def get_mapping(self, field_name: str, value: str, tenant_id: str, language: str = 'cn') -> Tuple[bool, Optional[str]]:
        """
        获取字典映射，会在中英文字典中都尝试查找映射
        :param field_name: 字段名
        :param value: 原始值
        :param tenant_id: 租户ID
        :param language: 目标语言，'cn'或'en'
        :return: (是否找到映射, 映射值)的元组，如果未找到映射则返回(False, None)
        """
        if not value or not field_name or not tenant_id:
            return False, None

        tenant_dict = self._dict_cache.get(tenant_id, {})
        field_dict = tenant_dict.get(field_name, {})
        
        # 在中文字典中查找
        cn_dict = field_dict.get('cn', {})
        if value in cn_dict:
            # 如果在中文字典中找到，根据目标语言返回相应的值
            return True, (cn_dict[value] if language == 'en' else value)
            
        # 在英文字典中查找
        en_dict = field_dict.get('en', {})
        if value in en_dict:
            # 如果在英文字典中找到，根据目标语言返回相应的值
            return True, (value if language == 'en' else en_dict[value])
            
        return False, None

    def refresh(self):
        """
        刷新缓存数据
        """
        self._dict_cache.clear()
        self._load_system_dict()
