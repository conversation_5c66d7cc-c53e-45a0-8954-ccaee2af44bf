from typing import Any
from fastapi import HTTPException


class HTTPError(HTTPException):
    def __init__(self, *, code: int, msg: Any = None, headers: dict[str, Any] | None = None):
        super().__init__(status_code=code, detail=msg, headers=headers)


class AuthorizationError(HTTPError):
    code = 403

    def __init__(self, *, msg: str = '无效的认证方式', headers: dict[str, Any] | None = None):
        super().__init__(code=self.code, msg=msg, headers=headers or {'WWW-Authenticate': 'Bearer'})


class TokenError(HTTPError):
    code = 403

    def __init__(self, *, msg: str = '无效Token', headers: dict[str, Any] | None = None):
        super().__init__(code=self.code, msg=msg, headers=headers)
