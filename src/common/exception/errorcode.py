from src.api.schema import UnifiedResponseModel


class BaseErrorCode:
    Code: int
    Msg: str

    @classmethod
    def return_resp(cls, msg: str = None, data: any = None) -> UnifiedResponseModel:
        return UnifiedResponseModel(status_code=cls.Code, status_message=msg or cls.Msg, data=data)


class GenerationFailureError(BaseErrorCode):
    Code: int = 400
    Msg: str = '生成失败，请稍后再试'


class FileFormatError(BaseErrorCode):
    Code: int = 422
    Msg: str = '文件格式解析错误，请确保上传正常格式文件'


class SystemServiceError(BaseErrorCode):
    Code: int = 500
    Msg: str = '系统错误，生成失败，请稍后重试'


class TaskExistError(BaseErrorCode):
    Code: int = 410
    Msg: str = '任务已存在'
