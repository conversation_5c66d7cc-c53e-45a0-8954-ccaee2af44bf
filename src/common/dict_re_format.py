import xml.etree.ElementTree as ET
import html
from datetime import datetime
import uuid

# 中文数据XML字符串（可替换为实际文件读取）
cn_xml = """<?xml version='1.0' encoding='UTF-8'?><MESSAGE><OPTIONS><OPTION id='100002'>mL</OPTION></OPTIONS></MESSAGE>"""

# 英文数据XML字符串（可替换为实际文件读取）
en_xml = """<?xml version='1.0' encoding='UTF-8'?><MESSAGE><OPTIONS><OPTION id='100002'>mL</OPTION></OPTIONS></MESSAGE>"""

current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
tenant_id = "kelun"
create_by = "user"
update_by = "user"
is_delete = 0
field_name = "TXT_cdr_dose_unit_id"

# 解析中文XML
cn_root = ET.fromstring(cn_xml)
cn_options = cn_root.find('OPTIONS')
cn_dict = {}
for option in cn_options.findall('OPTION'):
    cid = option.get('id')
    cn_text = html.unescape(option.text)
    cn_dict[cid] = cn_text

# 解析英文XML
en_root = ET.fromstring(en_xml)
en_options = en_root.find('OPTIONS')
en_dict = {}
for option in en_options.findall('OPTION'):
    eid = option.get('id')
    en_text = option.text
    en_dict[eid] = en_text

# 根据中文顺序输出多条INSERT语句
# 每条记录一条INSERT
for option in cn_options.findall('OPTION'):
    cid = option.get('id')
    cn_val = cn_dict.get(cid, '')
    en_val = en_dict.get(cid, '')
    record_id = str(uuid.uuid4())  # 每条记录一个新的uuid
    # 字符串中可能包含单引号，需要转义
    cn_val_escaped = cn_val.replace("'", "''")
    en_val_escaped = en_val.replace("'", "''")

    insert_sql = f"""
INSERT INTO `t_system_dict` (
    `id`, `field_name`, `dict_cn`, `dict_en`, `tenant_id`, `create_time`, `update_time`, `create_by`, `update_by`, `is_delete`
) VALUES (
    '{record_id}', '{field_name}', '{cn_val_escaped}', '{en_val_escaped}', '{tenant_id}',
    '{current_time}', '{current_time}', '{create_by}', '{update_by}', {is_delete}
);
""".strip()
    print(insert_sql)
