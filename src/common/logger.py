import os
import loguru
import platform
from pathlib import Path


class Logger:
    def __init__(self):

        system_name = platform.system()
        if system_name == "Linux":
            self.log_path = Path.cwd().joinpath("logs")
        elif system_name in ["Windows", "Darwin"]:
            self.log_path = Path.cwd().parent.joinpath("logs")
        if not os.path.exists(self.log_path):
            os.mkdir(self.log_path)

        self.logger = loguru.logger

        log_stdout_file = os.path.join(self.log_path, 'info.log')
        log_stderr_file = os.path.join(self.log_path, 'error.log')

        log_config = dict(rotation="00:00", retention='15 days', enqueue=True)

        self.logger.add(
            sink=log_stdout_file,
            level='DEBUG',
            filter=lambda record: record['level'].name in ['DEBUG', 'INFO'],
            **log_config,
            backtrace=False,
            diagnose=False,
        )

        self.logger.add(
            sink=log_stderr_file,
            level='WARNING',
            filter=lambda record: record['level'].name in ['WARNING', 'ERROR'],
            **log_config,
            backtrace=True,
            diagnose=True,
        )


logger = Logger().logger
