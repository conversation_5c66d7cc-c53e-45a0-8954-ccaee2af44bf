Metadata-Version: 2.4
Name: pv-model-service
Version: 0.1.0
Summary: Add your description here
Requires-Python: >=3.11
Description-Content-Type: text/markdown
Requires-Dist: aiofiles>=24.1.0
Requires-Dist: beautifulsoup4>=4.13.3
Requires-Dist: boto3>=1.37.21
Requires-Dist: botocore>=1.37.21
Requires-Dist: fastapi>=0.115.12
Requires-Dist: fastapi-cdn-host>=0.8.4
Requires-Dist: httpx>=0.28.1
Requires-Dist: jinja2>=3.1.6
Requires-Dist: langfuse>=2.60.7
Requires-Dist: loguru>=0.7.3
Requires-Dist: markdown>=3.7
Requires-Dist: nanoid>=2.0.0
Requires-Dist: numpy<2.0.0,>=1.24.0
Requires-Dist: openpyxl>=3.1.5
Requires-Dist: orjson>=3.10.16
Requires-Dist: pandas>=2.2.3
Requires-Dist: pathlib>=1.0.1
Requires-Dist: pillow>=11.1.0
Requires-Dist: pydantic>=2.10.6
Requires-Dist: pymupdf>=1.25.5
Requires-Dist: pymysql>=1.1.1
Requires-Dist: python-dotenv>=1.1.0
Requires-Dist: python-multipart>=0.0.20
Requires-Dist: sqlalchemy>=2.0.39
Requires-Dist: starlette>=0.46.1
Requires-Dist: tenacity>=9.0.0
Requires-Dist: tqdm>=4.67.1
Requires-Dist: urllib3>=2.3.0
Requires-Dist: uvicorn>=0.34.0

## python项目依赖更新

## 使用 uv 管理 Python 依赖

[uv](https://github.com/astral-sh/uv) 是一个非常快速的 Python 包安装器和解析器，旨在替代 `pip` 和 `pip-tools`。

### 常用命令

```bash
# 安装 uv (如果尚未安装)
# 参考官方文档: https://astral.sh/uv/install.sh
# 或者使用 pip:
pip install uv

# 根据pyproject.tom、uv.lock自动安装依赖包
uv sync
 
# 添加依赖
uv add package-name

# 更新全部依赖
uv lock --upgrade
uv sync

# 生成 requirements.txt
uv pip compile pyproject.toml -o requirements.txt
```

## 本地运行

### 运行模式

由于插件是跨域请求的后端，服务器上由nginx进行CORS的返回头的处理，本地运行需要添加命令参数

```shell
deploy_mode = local
```

### 热部署

Python支持热更新，需要通过以下命令参数进行开启

```shell
deploy_reload = local
```

