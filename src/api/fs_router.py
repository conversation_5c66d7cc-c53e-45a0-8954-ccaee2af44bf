import os
import json
import pathlib
import urllib.parse
import traceback
import mimetypes
from functools import wraps
from fastapi import APIRouter, HTTPException, Query, status, Response
from fastapi.responses import FileResponse, StreamingResponse
from typing import Optional, List, Dict, Any, Callable
from src.common.logger import logger
from src.utils import dirs

BASE_DIR = dirs.build_path("documents")

router = APIRouter(prefix="/fs", tags=["FileSystem"])

def is_safe_path(basedir: pathlib.Path, requested_path: pathlib.Path) -> bool:
    """检查请求的路径是否安全地位于基础目录内"""
    try:
        base_resolve = basedir.resolve()
        req_resolve = requested_path.resolve()
        return str(req_resolve).startswith(str(base_resolve))
    except Exception:
        return False

def handle_file_operation(path_param: str = "file_path"):
    """用于处理文件操作的装饰器，包含通用的安全检查和错误处理"""
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                file_path = kwargs.get(path_param)
                if not file_path:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="缺少文件路径参数。"
                    )

                file_path_decoded = urllib.parse.unquote(file_path)
                if ".." in file_path_decoded.split(os.path.sep):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="文件路径包含无效字符。"
                    )

                target_path = BASE_DIR.joinpath(file_path_decoded)
                full_path = target_path.resolve()

                if not is_safe_path(BASE_DIR, full_path):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="禁止访问！路径超出允许范围。"
                    )

                kwargs["full_path"] = full_path
                return await func(*args, **kwargs)

            except FileNotFoundError:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="文件未找到。"
                )
            except PermissionError:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限访问该文件。"
                )
            except HTTPException:
                raise
            except Exception as e:
                logger.info(traceback.format_exc())
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="操作过程中发生内部错误。"
                )
        return wrapper
    return decorator

async def _get_directory_listing(subdir: str = "", search: Optional[str] = None):
    """Helper function to get directory listing."""
    try:
        # Decode and sanitize subdir path
        subdir_decoded = urllib.parse.unquote(subdir)
        if ".." in subdir_decoded.split(os.path.sep):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="路径包含无效字符。"
            )

        current_dir = BASE_DIR.joinpath(subdir_decoded).resolve()
        if not is_safe_path(BASE_DIR, current_dir):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="禁止访问！路径超出允许范围。"
            )

        if not current_dir.is_dir():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="目录未找到。"
            )

        # Calculate display path
        current_path_display = (
            "/" + current_dir.relative_to(BASE_DIR.resolve()).as_posix()
            if current_dir != BASE_DIR.resolve() else "/"
        )

        # Calculate parent path
        parent_path = None
        if current_dir != BASE_DIR.resolve():
            parent_dir = current_dir.parent
            if is_safe_path(BASE_DIR, parent_dir):
                parent_rel_path = parent_dir.relative_to(BASE_DIR.resolve()).as_posix()
                parent_path = "" if parent_rel_path == "." else parent_rel_path

        # Build items list
        items = []
        for entry in current_dir.iterdir():
            if search and search.lower() not in entry.name.lower():
                continue
            item_rel_path = entry.relative_to(BASE_DIR.resolve()).as_posix()
            items.append({
                "name": entry.name,
                "is_dir": entry.is_dir(),
                "path": item_rel_path,
            })

        items.sort(key=lambda x: (not x["is_dir"], x["name"].lower()))

        return {
            "current_path": current_path_display,
            "parent_path": parent_path,
            "items": items,
            "error": None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.info(f"Error listing directory: {str(e)}")
        return {
            "current_path": "/",
            "parent_path": None,
            "items": [],
            "error": f"浏览目录时发生错误: {str(e)}"
        }

@router.get("/list")
async def list_files(
    subdir: str = Query(""),
    search: Optional[str] = None
):
    """Lists files and directories in the specified directory."""
    result = await _get_directory_listing(subdir=subdir, search=search)
    return Response(
        content=json.dumps(result),
        media_type='application/json',
    )

@router.get("/preview")
@handle_file_operation()
async def preview_file(file_path: str = Query(...), full_path: pathlib.Path = None):
    """Preview the specified file content without downloading."""
    if not full_path.is_file():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指定路径不是一个文件。"
        )

    # 获取文件的MIME类型
    content_type, _ = mimetypes.guess_type(str(full_path))
    if not content_type:
        content_type = 'application/octet-stream'

    # 对于文本类型文件，添加charset=utf-8
    if content_type.startswith('text/'):
        content_type += '; charset=utf-8'

    async def file_stream():
        try:
            with open(full_path, 'rb') as f:
                while chunk := f.read(8192):  # 8KB chunks
                    yield chunk
        except Exception as e:
            logger.info(f"Error streaming file: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="读取文件时发生错误。"
            )

    filename = urllib.parse.quote(full_path.name)
    return StreamingResponse(
        file_stream(),
        media_type=content_type,
        headers={
            'Content-Disposition': f'inline; filename="{filename}"',
        }
    )

@router.get("/download")
@handle_file_operation()
async def download_file(file_path: str = Query(...), full_path: pathlib.Path = None):
    """Downloads the specified file."""
    if not full_path.is_file():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指定路径不是一个文件。"
        )
    filename = urllib.parse.quote(full_path.name)
    return FileResponse(
        path=full_path,
        filename=filename,
        headers={
            'Content-Disposition': f'attachment; filename="{filename}"',
        }
    )