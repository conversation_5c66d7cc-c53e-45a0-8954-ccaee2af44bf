import asyncio
import os
import threading
import time
import traceback
import uuid
from datetime import datetime
from typing import Optional

from dotenv import load_dotenv
from fastapi import APIRouter, File, UploadFile, Body, HTTPException, Request
from fastapi.responses import StreamingResponse
from langfuse.decorators import observe, langfuse_context
from nanoid import generate

from api import fs_router  # 导入文件系统路由
from api.schema import UnifiedResponseModel, UploadResponse, ExtractRequest, ExtractStreamData, \
    RecognizeRequest, RecognizeStreamData, NarrativeRequest, NarrativeStreamData, \
    StructuredRequest, StructuredStreamData, TransformerRequest, TransformerStreamData, PageData, \
    PromptTemplateGroupResponse, PromptTemplateUpdate, PromptTemplateResponse, ExcelStreamData
from src.common import response
from src.common.context import TraceContext
from src.common.enums import AiCallTag
from src.common.logger import logger
from src.database import crud
from src.services.chat.model_factory import ModelFactory
from src.services.extraction import Extraction
from src.services.feedback.excel_service import ExcelService
from src.services.model_config import ModelConfigService
from src.services.narrative import Narrative
from src.services.prompt import PromptService
from src.services.recognize import Recognize
from src.services.structured import Structured
from src.services.transformer import Transformer
from src.services.upload import Upload
from src.utils.util import get_language

router = APIRouter(prefix='/api/pv', tags=['PV'])


@router.post("/file/upload", name="上传", response_model=UnifiedResponseModel[UploadResponse])
async def upload_doc(request: Request,
                     file: UploadFile = File(...),
                     tenantId: str = Body(default="avb"),
                     tenantEnv: str = Body(default="UAT"),
                     studyNum: str = Body(default=""),
                     language: str = Body(default='en')):
    start_time = datetime.now()
    trace_id = generate(size=20)
    filename_md5, operate_status, exception_detail = None, "1", None
    try:
        upload_instance = Upload(file, language, trace_id)
        filename_md5, base64_images = await upload_instance.file_upload()
        return response.resp_200(data=UploadResponse(md5=filename_md5, base64_images=base64_images))
    except (Exception,) as exc:
        operate_status = "0"
        exception_detail = str(exc)
        logger.exception("{id} | {message}", id=trace_id, message=str(exc))
        traceback.print_exc()
        return HTTPException(status_code=500, detail=str(exc))
    finally:
        time_difference = (datetime.now() - start_time).total_seconds()
        crud.insert_sys_log(trace_id, filename_md5, request.client.host, request.method, request.url, operate_status, exception_detail, start_time, time_difference, tenantId, studyNum)


@observe
@router.post("/recognize/stream/chat", name="图片识别")
async def recognize_chat(request: Request, body: RecognizeRequest = Body(...)):
    start_time = datetime.now()
    trace_id = generate(size=20)

    @observe
    async def event_stream(md5):
        operate_status, exception_detail = "1", None
        try:
            # 更新 langfuse 上下文
            langfuse_context.update_current_trace(
                session_id=md5,
                user_id=body.userId,
                version=trace_id,
                tags=["recognize", body.tenantId, *([body.studyNum] if body.studyNum else [])]
            )
            
            # 只获取 langfuse 的 trace_id
            langfuse_trace_id = langfuse_context.get_current_trace_id()
            
            # 更新 TraceContext 中的 langfuse trace_id
            TraceContext.init_trace(
                trace_id=trace_id,
                tenant_id=body.tenantId,
                study_num=body.studyNum,
                module_name="recognize",
                tag=AiCallTag.RECOGNIZE.value,
                md5=md5,
                user_id=body.userId,
                user_name=getattr(body, 'userName', None),
                langfuse_trace_id=langfuse_trace_id,
                language=get_language(body.md5)
            )
            
            recognize_instance = Recognize(md5, body.tenantId, trace_id)

            # 简化页面数据处理
            all_contents = [
                PageData(no=idx, data=item.content)
                for idx, item in enumerate(body.data or [])
            ]

            async for message in recognize_instance.image_recognize(all_contents):
                yield message
        except Exception as e:
            operate_status = "0"
            exception_detail = str(e)
            logger.exception(f"{md5} | {trace_id} | {str(e)}")
            yield str(RecognizeStreamData(data={'type': 'error', 'message': str(e)}))
        finally:
            yield str(RecognizeStreamData(data={'type': 'end', 'message': ''}))
            logger.info(f"{md5} | {trace_id} | recognize end !!!")
            time_difference = (datetime.now() - start_time).total_seconds()
            crud.insert_sys_log(
                trace_id, md5, request.client.host, request.method, request.url,
                operate_status, exception_detail, start_time, time_difference,
                body.tenantId, body.studyNum
            )
            # 清理TraceContext
            TraceContext.clear()

    try:
        return StreamingResponse(event_stream(body.md5), media_type="text/event-stream")
    except Exception as exc:
        logger.error(f"{body.md5} | {trace_id} | {str(exc)}")
        traceback.print_exc()
        TraceContext.clear()  # 确保异常情况下也清理上下文
        raise HTTPException(status_code=500, detail=str(exc))  # 修正异常处理

@observe()
@router.post("/extract/stream/chat", name="结构化提取")
async def extract_chat(request: Request, body: ExtractRequest = Body(...)):
    start_time = datetime.now()
    trace_id = generate(size=20)

    # 创建线程安全的事件和队列
    extraction_complete = threading.Event()
    message_queue = asyncio.Queue()
    end_message_event = threading.Event()
    critical_error_event = threading.Event()

    # 保存当前事件循环的引用
    main_loop = asyncio.get_running_loop()

    # 使用闭包捕获主事件循环
    def safe_queue_put(message):
        try:
            if isinstance(message, str) and '"type":"end"' in message:
                end_message_event.set()

            if main_loop and not main_loop.is_closed():
                future = asyncio.run_coroutine_threadsafe(
                    message_queue.put(message),
                    main_loop
                )
                future.result(timeout=2)
                return True
            else:
                logger.warning("{md5} | {id} | Main event loop not available or closed",
                               md5=body.md5, id=trace_id)
                return False
        except (RuntimeError, asyncio.InvalidStateError) as e:
            logger.warning("{md5} | {id} | Event loop error: {error}",
                           md5=body.md5, id=trace_id, error=str(e))
            return False
        except Exception as e:
            logger.warning("{md5} | {id} | Queue put error: {error}",
                           md5=body.md5, id=trace_id, error=str(e))
            return False

    # 提取线程函数
    @observe
    def extraction_thread():
        operate_status, exception_detail = "1", None
        thread_saw_end_message = False

        try:
            langfuse_context.update_current_trace(
                session_id=body.md5,
                user_id=body.userId,
                version=trace_id,
                tags=["extract", body.tenantId, *([body.studyNum] if body.studyNum else [])]
            )

            langfuse_trace_id = langfuse_context.get_current_trace_id()

            TraceContext.init_trace(
                trace_id=trace_id,
                tenant_id=body.tenantId,
                study_num=body.studyNum,
                module_name="extract",
                tag=AiCallTag.EXTRACT.value,
                md5=body.md5,
                user_id=body.userId,
                user_name=getattr(body, 'userName', None),
                langfuse_trace_id=langfuse_trace_id,
                language=get_language(body.md5)
            )

            extraction_instance = Extraction(body.md5, body.tenantId, trace_id)

            thread_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(thread_loop)

            async def process_extraction():
                # <<< 修改：将 nonlocal 声明移到函数顶部
                nonlocal operate_status, exception_detail, thread_saw_end_message

                try:
                    extraction_gen = extraction_instance.extract_structured_data(body.data)
                    async for message in extraction_gen:
                        if isinstance(message, str) and '"type":"end"' in message:
                            thread_saw_end_message = True
                            logger.debug("{md5} | {id} | End message seen in extraction thread",
                                         md5=body.md5, id=trace_id)

                        try:
                            await message_queue.put(message)
                        except Exception as e:
                            logger.warning("{md5} | {id} | Direct queue put failed: {error}, trying safe put",
                                           md5=body.md5, id=trace_id, error=str(e))
                            if not safe_queue_put(message):
                                logger.error("{md5} | {id} | Failed to add message to queue",
                                             md5=body.md5, id=trace_id)
                except ValueError as ve:
                    error_msg = str(ve)
                    logger.error("{md5} | {id} | 发生致命错误，将中断执行流程: {message}",
                                 md5=body.md5, id=trace_id, message=error_msg)
                    critical_error_event.set()
                    error_message = str(ExtractStreamData(data={'type': 'error', 'message': error_msg}))
                    safe_queue_put(error_message)
                    operate_status = "0"
                    exception_detail = f"致命错误: {error_msg}"

                except Exception as e:
                    error_msg = str(e)
                    logger.exception("{md5} | {id} | 发生非致命错误: {message}",
                                     md5=body.md5, id=trace_id, message=error_msg)
                    error_message = str(ExtractStreamData(data={'type': 'error', 'message': error_msg}))
                    try:
                        await message_queue.put(error_message)
                    except Exception:
                        safe_queue_put(error_message)
                    operate_status = "0"
                    exception_detail = error_msg

            try:
                thread_loop.run_until_complete(process_extraction())
            finally:
                if not thread_saw_end_message and not critical_error_event.is_set():
                    try:
                        end_message = str(ExtractStreamData(data={'type': 'end', 'message': ''}))
                        if not thread_loop.is_closed():
                            logger.info("{md5} | {id} | Sending end message from thread loop",
                                        md5=body.md5, id=trace_id)
                            thread_loop.run_until_complete(message_queue.put(end_message))
                            end_message_event.set()
                        else:
                            logger.info("{md5} | {id} | Sending end message via safe put",
                                        md5=body.md5, id=trace_id)
                            if safe_queue_put(end_message):
                                end_message_event.set()
                    except Exception as e:
                        logger.warning("{md5} | {id} | Failed to send end message from thread: {error}",
                                       md5=body.md5, id=trace_id, error=str(e))
                else:
                    logger.debug("{md5} | {id} | Thread saw end message during extraction or a critical error occurred",
                                 md5=body.md5, id=trace_id)
                try:
                    if not thread_loop.is_closed():
                        thread_loop.close()
                except Exception as e:
                    logger.warning("{md5} | {id} | Error closing thread loop: {error}",
                                   md5=body.md5, id=trace_id, error=str(e))

        except Exception as e:
            operate_status = "0"
            exception_detail = str(e)
            logger.exception("{md5} | {id} | Thread error: {message}",
                             md5=body.md5, id=trace_id, message=str(e))
            critical_error_event.set()
            if not end_message_event.is_set():
                safe_queue_put(str(ExtractStreamData(data={'type': 'error', 'message': str(e)})))
                if safe_queue_put(str(ExtractStreamData(data={'type': 'end', 'message': ''}))):
                    end_message_event.set()
        finally:
            try:
                logger.info("{md5} | {id} | {message}", md5=body.md5, id=trace_id, message="extraction end !!!")
                time_difference = (datetime.now() - start_time).total_seconds()
                crud.insert_sys_log(trace_id, body.md5, request.client.host, request.method,
                                    request.url, operate_status, exception_detail,
                                    start_time, time_difference, body.tenantId, body.studyNum)
            except Exception as e:
                logger.error("{md5} | {id} | Cleanup error: {message}",
                             md5=body.md5, id=trace_id, message=str(e))
            extraction_complete.set()
            if not end_message_event.is_set():
                logger.warning("{md5} | {id} | Extraction thread completed but end message flag not set",
                               md5=body.md5, id=trace_id)
                end_message_event.set()
            TraceContext.clear()

    async def stream_with_heartbeat():
        extraction_thread_obj = threading.Thread(target=extraction_thread)
        extraction_thread_obj.daemon = True
        extraction_thread_obj.start()

        last_heartbeat = time.time()
        heartbeat_interval = 30
        main_saw_end_message = False

        try:
            while not (
                    (extraction_complete.is_set() and message_queue.empty()) or
                    critical_error_event.is_set()
            ):
                current_time = time.time()
                if current_time - last_heartbeat >= heartbeat_interval:
                    logger.debug("{md5} | {id} | Sending heartbeat", md5=body.md5, id=trace_id)
                    yield str(ExtractStreamData(data={'type': 'heartbeat', 'message': ''}))
                    last_heartbeat = current_time

                try:
                    message = await asyncio.wait_for(message_queue.get(), timeout=min(5, heartbeat_interval/2))
                    if isinstance(message, str) and '"type":"end"' in message:
                        main_saw_end_message = True
                        logger.debug("{md5} | {id} | End message seen in main stream",
                                     md5=body.md5, id=trace_id)
                    yield message
                except asyncio.TimeoutError:
                    if critical_error_event.is_set():
                        logger.warning("{md5} | {id} | 检测到致命错误信号，准备终止流", md5=body.md5, id=trace_id)
                        break
                    continue

            while not message_queue.empty():
                yield await message_queue.get()

            if critical_error_event.is_set():
                logger.info("{md5} | {id} | 已因致命错误终止流响应", md5=body.md5, id=trace_id)
            elif extraction_complete.is_set() and not main_saw_end_message:
                logger.warning("{md5} | {id} | Extraction complete but no end message seen, sending one",
                               md5=body.md5, id=trace_id)
                yield str(ExtractStreamData(data={'type': 'end', 'message': ''}))

        finally:
            if extraction_thread_obj.is_alive():
                critical_error_event.set()
                extraction_complete.set()
                extraction_thread_obj.join(timeout=0.1)
            TraceContext.clear()

    try:
        return StreamingResponse(stream_with_heartbeat(), media_type="text/event-stream")
    except (Exception,) as exc:
        logger.error("{md5} | {id} | {message}", md5=body.md5, id=trace_id, message=str(exc))
        traceback.print_exc()
        TraceContext.clear()
        return HTTPException(status_code=500, detail=str(exc))


@router.post("/narrative/stream/chat", name="事件描述")
async def narrative_chat(request: Request, body: NarrativeRequest = Body(...)):
    start_time = datetime.now()
    trace_id = generate(size=20)

    @observe()
    async def event_stream(md5):
        operate_status, exception_detail = "1", None
        try:
            # 更新 langfuse 上下文
            langfuse_context.update_current_trace(
                session_id=md5,
                user_id=body.userId,
                version=trace_id,
                tags=["narrative", body.tenantId, *([body.studyNum] if body.studyNum else [])]
            )
            
            # 只获取 langfuse 的 trace_id
            langfuse_trace_id = langfuse_context.get_current_trace_id()
            
            # 更新 TraceContext 中的 langfuse trace_id
            TraceContext.init_trace(
                trace_id=trace_id,
                tenant_id=body.tenantId,
                study_num=body.studyNum,
                module_name="narrative",
                tag=AiCallTag.NARRATIVE.value,
                md5=md5,
                user_id=body.userId,
                user_name=getattr(body, 'userName', None),
                langfuse_trace_id=langfuse_trace_id,
                language=get_language(body.md5)
            )
            
            narrative_instance = Narrative(md5, body.tenantId, trace_id, body.studyNum)
            async for message in narrative_instance.generate_narrative_data():
                yield message
        except Exception as e:
            operate_status = "0"
            exception_detail = str(e)
            logger.exception("{md5} | {id} | {message}", md5=body.md5, id=trace_id, message=str(e))
            yield str(NarrativeStreamData(data={'type': 'error', 'message': str(e)}))
        finally:
            yield str(NarrativeStreamData(data={'type': 'end', 'message': ''}))
            logger.info("{md5} | {id} | {message}", md5=body.md5, id=trace_id, message="narrative end !!!")
            time_difference = (datetime.now() - start_time).total_seconds()
            crud.insert_sys_log(trace_id, md5, request.client.host, request.method, request.url, operate_status, exception_detail, start_time, time_difference, body.tenantId, body.studyNum)
            # 清理TraceContext
            TraceContext.clear()
            
    try:
        return StreamingResponse(event_stream(body.md5), media_type="text/event-stream")
    except (Exception,) as exc:
        logger.error("{md5} | {id} | {message}", md5=body.md5, id=trace_id, message=str(exc))
        traceback.print_exc()
        TraceContext.clear()  # 确保异常情况下也清理上下文
        return HTTPException(status_code=500, detail=str(exc))


@router.post("/structured/stream/chat", name="结构化生成")
async def structured_chat(request: Request, body: StructuredRequest = Body(...)):
    start_time = datetime.now()
    trace_id = generate(size=20)

    @observe()
    async def event_stream(md5):
        operate_status, exception_detail = "1", None
        try:
            # 更新 langfuse 上下文
            langfuse_context.update_current_trace(
                session_id=md5,
                user_id=body.userId,
                version=trace_id,
                tags=["structured", body.tenantId, *([body.studyNum] if body.studyNum else [])]
            )
            
            # 只获取 langfuse 的 trace_id
            langfuse_trace_id = langfuse_context.get_current_trace_id()
            
            # 更新 TraceContext 中的 langfuse trace_id
            TraceContext.init_trace(
                trace_id=trace_id,
                tenant_id=body.tenantId,
                study_num=body.studyNum,
                module_name="structured",
                tag=AiCallTag.STRUCTURE.value,
                md5=md5,
                user_id=body.userId,
                user_name=getattr(body, 'userName', None),
                langfuse_trace_id=langfuse_trace_id,
                language=get_language(body.md5)
            )
            
            structured_instance = Structured(md5, body.tenantId, trace_id)
            async for message in structured_instance.generate_structured_data():
                yield message
        except Exception as e:
            operate_status = "0"
            exception_detail = str(e)
            logger.exception("{md5} | {id} | {message}", md5=body.md5, id=trace_id, message=str(e))
            yield str(StructuredStreamData(data={'type': 'error', 'message': str(e)}))
        finally:
            yield str(StructuredStreamData(data={'type': 'end', 'message': ''}))
            logger.info("{md5} | {id} | {message}", md5=body.md5, id=trace_id, message="structured end !!!")
            time_difference = (datetime.now() - start_time).total_seconds()
            crud.insert_sys_log(trace_id, md5, request.client.host, request.method, request.url, operate_status, exception_detail, start_time, time_difference, body.tenantId, body.studyNum)
            # 清理TraceContext
            TraceContext.clear()
            
    try:
        return StreamingResponse(event_stream(body.md5), media_type="text/event-stream")
    except (Exception,) as exc:
        logger.error("{md5} | {id} | {message}", md5=body.md5, id=trace_id, message=str(exc))
        traceback.print_exc()
        TraceContext.clear()  # 确保异常情况下也清理上下文
        return HTTPException(status_code=500, detail=str(exc))


@router.post("/transformer/stream/chat", name="二次转换")
async def standardize_chat(request: Request, body: TransformerRequest = Body(...)):
    start_time = datetime.now()
    trace_id = generate(size=20)

    @observe()
    async def event_stream(md5):
        operate_status, exception_detail = "1", None
        try:
            # 更新 langfuse 上下文
            langfuse_context.update_current_trace(
                session_id=md5,
                user_id=body.userId,
                version=trace_id,
                tags=["transformer", body.tenantId, *([body.studyNum] if body.studyNum else [])]
            )
            
            # 只获取 langfuse 的 trace_id
            langfuse_trace_id = langfuse_context.get_current_trace_id()
            
            # 更新 TraceContext 中的 langfuse trace_id
            TraceContext.init_trace(
                trace_id=trace_id,
                tenant_id=body.tenantId,
                study_num=body.studyNum,
                module_name="transformer",
                tag=AiCallTag.TRANSFORMER.value,
                md5=md5,
                user_id=body.userId,
                user_name=getattr(body, 'userName', None),
                langfuse_trace_id=langfuse_trace_id,
                language=get_language(body.md5)
            )
            
            transformer_instance = Transformer(md5, body.tenantId, trace_id)
            async for message in transformer_instance.generate_transformer_data():
                yield message
        except Exception as e:
            operate_status = "0"
            exception_detail = str(e)
            logger.exception("{md5} | {id} | {message}", md5=body.md5, id=trace_id, message=str(e))
            yield str(TransformerStreamData(data={'type': 'error', 'message': str(e)}))
        finally:
            yield str(TransformerStreamData(data={'type': 'end', 'message': ''}))
            logger.info("{md5} | {id} | {message}", md5=body.md5, id=trace_id, message="transformer end !!!")
            time_difference = (datetime.now() - start_time).total_seconds()
            crud.insert_sys_log(trace_id, md5, request.client.host, request.method, request.url, operate_status, exception_detail, start_time, time_difference, body.tenantId, body.studyNum)
            # 清理TraceContext
            TraceContext.clear()
            
    try:
        return StreamingResponse(event_stream(body.md5), media_type="text/event-stream")
    except (Exception,) as exc:
        logger.error("{md5} | {id} | {message}", md5=body.md5, id=trace_id, message=str(exc))
        traceback.print_exc()
        TraceContext.clear()  # 确保异常情况下也清理上下文
        return HTTPException(status_code=500, detail=str(exc))


@router.get("/prompt/{tenant_id}", response_model=UnifiedResponseModel[PromptTemplateGroupResponse])
async def list_prompt_templates(
        request: Request,
        tenant_id: str,
        template_key: Optional[str] = None
):
    """根据租户ID获取提示模板列表，按语言分组"""
    try:
        prompt_service = PromptService()
        result = prompt_service.list_prompt_templates(tenant_id, template_key)
        return response.resp_200(data=result)
    except Exception as exc:
        raise HTTPException(status_code=500, detail=str(exc))


@router.put("/prompt/{tenant_id}/{template_key}", response_model=UnifiedResponseModel[PromptTemplateResponse])
async def update_prompt_template(
        request: Request,
        tenant_id: str,
        template_key: str,
        template: PromptTemplateUpdate
):
    """更新提示模板内容，如果不存在则创建新模板"""
    try:
        prompt_service = PromptService()
        result = prompt_service.update_prompt_template(
            tenant_id=tenant_id,
            template_key=template_key,
            content=template.content,
            language=template.language
        )
        return response.resp_200(data=result)
    except HTTPException as http_exc:
        raise http_exc
    except Exception as exc:
        raise HTTPException(status_code=500, detail=str(exc))


@router.post("/excel/upload", name="上传Excel或Zip文件", response_model=UnifiedResponseModel[str])
async def upload_excel(request: Request,
                      file: UploadFile = File(...),
                      userId: str = Body(default="")):
    # 在开始流式响应之前先读取文件内容并保存到磁盘
    file_content = await file.read()
    file_name = file.filename
    
    # 加载环境变量获取上传路径
    load_dotenv()
    upload_file_path = os.getenv('UPLOAD_FILE_PATH', '/home/<USER>/uploadFile/pmp')
    os.makedirs(upload_file_path, exist_ok=True)
    
    file_uuid = str(uuid.uuid4()).replace('-', '')
    file_alias_name = f"{file_uuid}-{file_name}"
    file_path = os.path.join(upload_file_path, file_alias_name)
    
    # 直接保存文件到磁盘
    with open(file_path, "wb") as f:
        f.write(file_content)
    
    logger.info(f"文件已保存到: {file_path}")
    
    async def event_stream():
        try:
            # 实例化服务
            excel_service = ExcelService(userId)
            # 处理已保存的文件
            async for result in excel_service.process_saved_file(file_path, file_name, file_uuid):
                yield str(ExcelStreamData(data=result))
        except Exception as e:
            logger.exception("{userId} | {message}", userId=userId, message=str(e))
            yield str(ExcelStreamData(data={'type': 'error', 'message': str(e)}))
            # 错误处理可以在此添加，如删除文件等
        finally:
            # 确保发送结束消息
            yield str(ExcelStreamData(data={'type': 'end', 'message': ''}))
            
    try:
        # 返回流式响应
        return StreamingResponse(event_stream(), media_type="text/event-stream")
    except Exception as exc:
        logger.exception("{userId} | {message}", userId=userId, message=str(exc))
        traceback.print_exc()
        return HTTPException(status_code=500, detail=str(exc))


# 包含文件系统路由
router.include_router(fs_router.router)


@router.get("/model-cache/clear", name="清除模型缓存")
async def clear_model_cache(wait: bool = True, max_wait_seconds: int = 30):
    """
    清除模型配置和模型实例的缓存。
    当修改了模型配置或租户模型映射后，调用此接口使变更立即生效。
    
    注意：仅在没有活跃的模型调用时才会清除缓存，否则会返回错误。
    
    Args:
        wait: 当有活跃请求时是否等待请求完成后再清理缓存，默认为True
        max_wait_seconds: 最大等待时间（秒），默认为30秒
    
    Returns:
        清除缓存结果
    """
    try:
        start_time = time.time()
        wait_time = 0
        
        # 获取当前活跃请求数
        active_requests = TraceContext.get_active_requests_count()
        
        # 如果有活跃请求且设置了等待
        if active_requests > 0 and wait:
            logger.info("检测到 {count} 个活跃请求，等待它们完成后再清除模型缓存", count=active_requests)
            
            # 等待活跃请求完成，每秒检查一次，最多等待max_wait_seconds秒
            while TraceContext.get_active_requests_count() > 0 and wait_time < max_wait_seconds:
                await asyncio.sleep(1)  # 异步等待1秒
                wait_time = time.time() - start_time
                active_requests = TraceContext.get_active_requests_count()
                
            # 检查是否仍有活跃请求
            if active_requests > 0:
                logger.warning("等待 {wait_time:.1f} 秒后仍有 {count} 个活跃请求，放弃等待", 
                               wait_time=wait_time, count=active_requests)
                return response.resp_200(data={
                    "message": f"模型缓存清除失败：等待 {wait_time:.1f} 秒后仍有 {active_requests} 个活跃请求",
                    "status": "failed",
                    "active_requests": active_requests,
                    "wait_time": f"{wait_time:.1f}"
                })
        elif active_requests > 0 and not wait:
            logger.warning("尝试清除模型缓存失败：当前有 {count} 个活跃请求正在使用模型", count=active_requests)
            return response.resp_200(data={
                "message": f"模型缓存清除失败：当前有 {active_requests} 个活跃请求正在使用模型",
                "status": "failed",
                "active_requests": active_requests
            })
        
        # 清除ModelConfigService中的缓存
        ModelConfigService.clear_cache()

        # 清除ModelFactory中的模型实例缓存
        ModelFactory.clear_cache()

        if wait_time > 0:
            logger.info("等待 {wait_time:.1f} 秒后成功清除模型缓存", wait_time=wait_time)
            message = f"等待 {wait_time:.1f} 秒后成功清除模型缓存"
        else:
            logger.info("已通过API接口清除模型缓存")
            message = "模型缓存已成功清除"

        return response.resp_200(data={
            "message": message,
            "status": "success",
            "wait_time": f"{wait_time:.1f}" if wait_time > 0 else "0.0"
        })
    except Exception as e:
        logger.error("清除模型缓存失败: {error}", error=str(e))
        raise HTTPException(status_code=500, detail=f"清除模型缓存失败: {str(e)}")
