import json
import os
import uuid
import zipfile
import io
from datetime import datetime
from fastapi import UploadFile
from src.common.logger import logger
from dotenv import load_dotenv
from src.database.db_pmp import pmp_session
from src.database.models.pmp_models import TFileRecords, TReport, TReportMigration, TAutoInputTask
from src.services.feedback.manaus import excel_to_json, convert_excel_json_to_target
from src.utils.gzip_util import GzipUtil
import shutil

class ExcelService:
    def __init__(self, user_id: str):
        # 加载环境变量
        load_dotenv()
        # 文件上传配置
        self.upload_file_path = os.getenv('UPLOAD_FILE_PATH', '/home/<USER>/uploadFile/pmp')
        # 确保上传目录存在
        os.makedirs(self.upload_file_path, exist_ok=True)

        self.user_id = user_id

    async def save_report_data(self, json_data: dict, target_data: dict) -> str:
        """保存报告数据到数据库"""
        try:
            # 从json_data中获取solution_num
            solution_num = json_data.get('药品不良反应报告表', [{}])[0].get('反馈码')

            # 压缩target_data
            target_data_json = json.dumps(target_data, ensure_ascii=False, indent=2)
            compressed_target_data = GzipUtil.compress_string(target_data_json)

            with pmp_session() as session:
                # 查询是否存在相同solution_num且未删除的TReport记录
                existing_report = session.query(TReport).filter_by(
                    solution_num=solution_num,
                    is_delete=0
                ).first()

                if existing_report:
                    # 使用已存在的report_id
                    report_id = existing_report.id

                    # 更新TReport的update_time和update_by
                    existing_report.update_time = datetime.now()
                    existing_report.update_by = self.user_id

                    # 处理TReportMigration记录
                    existing_migration = session.query(TReportMigration).filter_by(
                        report_id=report_id,
                        type=1,
                        is_delete=0
                    ).first()

                    if existing_migration:
                        # 更新现有迁移数据
                        existing_migration.migration = compressed_target_data
                        existing_migration.update_time = datetime.now()
                        existing_migration.update_by = self.user_id
                    else:
                        # 创建新的迁移记录
                        new_migration = TReportMigration(
                            id=str(uuid.uuid4()).replace('-', ''),
                            report_id=report_id,
                            migration=compressed_target_data,
                            type=1,
                            create_time=datetime.now(),
                            update_time=datetime.now(),
                            create_by=self.user_id,
                            update_by=self.user_id,
                            is_delete=0,
                            tenant_id='ALS-all'
                        )
                        session.add(new_migration)

                    # 处理TAutoInputTask记录
                    existing_task = session.query(TAutoInputTask).filter_by(
                        report_id=report_id,
                        is_delete=False
                    ).first()

                    if not existing_task:
                        # 创建新的自动输入任务
                        auto_input_task = TAutoInputTask(
                            id=str(uuid.uuid4()).replace('-', ''),
                            report_id=report_id,
                            solution_num=solution_num,
                            build_status=0,
                            argus_num=None,
                            build_fail_count=0,
                            input_status_cn=0,
                            input_status_en=0,
                            input_fail_count=0,
                            client_id=None,
                            client_heart_time=None,
                            is_delete=False,
                            create_time=datetime.now(),
                            update_time=datetime.now(),
                            create_by=self.user_id,
                            update_by=self.user_id,
                            version=0,
                            tenant_id='ALS-all',
                            task_type=3
                        )
                        session.add(auto_input_task)

                    session.commit()
                    return report_id
                else:
                    # 创建新的报告记录
                    report_id = str(uuid.uuid4()).replace('-', '')
                    report = TReport(
                        id=report_id,
                        solution_num=solution_num,
                        report_detail=json_data,
                        create_time=datetime.now(),
                        update_time=datetime.now(),
                        create_by=self.user_id,
                        update_by=self.user_id,
                        report_origin_id=None,
                        report_version=None,
                        report_type='首次',
                        language=None,
                        migrate_xml_flag=-1,
                        migrate_attachment_flag=-1,
                        migrate_cioms_cn_flag=-1,
                        migrate_cioms_en_flag=-1,
                        migrate_e2b_cn_flag=-1,
                        migrate_e2b_en_flag=-1,
                        migrate_aer_flag=-1,
                        migrate_detail_audit_trail_flag=-1,
                        migrate_view_e2b_flag=-1,
                        is_delete=0,
                        tenant_id='ALS-all',
                        study_num=None,
                        wwid=None
                    )
                    session.add(report)

                    # 创建迁移记录
                    report_migration = TReportMigration(
                        id=str(uuid.uuid4()).replace('-', ''),
                        report_id=report_id,
                        migration=compressed_target_data,
                        type=1,
                        create_time=datetime.now(),
                        update_time=datetime.now(),
                        create_by=self.user_id,
                        update_by=self.user_id,
                        is_delete=0,
                        tenant_id='ALS-all'
                    )
                    session.add(report_migration)

                    # 创建自动输入任务（如果不存在）
                    existing_task = session.query(TAutoInputTask).filter_by(
                        report_id=report_id,
                        is_delete=False
                    ).first()

                    if not existing_task:
                        auto_input_task = TAutoInputTask(
                            id=str(uuid.uuid4()).replace('-', ''),
                            report_id=report_id,
                            solution_num=solution_num,
                            build_status=0,
                            argus_num=None,
                            build_fail_count=0,
                            input_status_cn=0,
                            input_status_en=0,
                            input_fail_count=0,
                            client_id=None,
                            client_heart_time=None,
                            is_delete=False,
                            create_time=datetime.now(),
                            update_time=datetime.now(),
                            create_by=self.user_id,
                            update_by=self.user_id,
                            version=0,
                            tenant_id='ALS-all',
                            task_type = 3
                        )
                        session.add(auto_input_task)

                    session.commit()
                    return report_id

        except Exception as e:
            logger.error(f"保存报告数据失败: {str(e)}")
            raise

    async def save_file_record(self, file_uuid: str, file_name: str, file_alias_name: str, file_path: str):
        """保存文件记录到数据库"""
        try:
            file_record = TFileRecords(
                id=file_uuid,
                file_name=file_name,
                file_alias_name=file_alias_name,
                file_path=file_path,
                tenant_id="ALS-all",
                create_time=datetime.now(),
                update_time=datetime.now(),
                create_by=self.user_id,
                update_by=self.user_id,
                is_delete=0
            )
            
            with pmp_session() as session:
                session.add(file_record)
                session.commit()
            return {"type": "message", "status": "success", "file_name": file_name, "file_uuid": file_uuid}
        except Exception as e:
            logger.error(f"保存文件记录失败: {str(e)}")
            return {"type": "error", "message": f"保存文件记录失败: {str(e)}", "file_name": file_name}

    def validate_target_data(self, target_data):
        """验证 target_data 中的相关字段是否满足要求"""

        def validate_field(value):
            return value and value != "未知编码"

        # 验证 deadReasonInfoTable
        if "deadReasonInfoTable" in target_data["reportSubjectInfo"]:
            for item in target_data["reportSubjectInfo"]["deadReasonInfoTable"]:
                if not validate_field(item.get("_cause_reptd_lltname", "")):
                    return False

        # 验证 rel_hist_add
        if "rel_hist_add" in target_data["reportSubjectInfo"]:
            for item in target_data["reportSubjectInfo"]["rel_hist_add"]:
                if not validate_field(item.get("_pat_hist_rptd_lltname", "")):
                    return False

        # 验证 drugInfo.试验用药
        if "试验用药" in target_data["drugInfo"]:
            for drug in target_data["drugInfo"]["试验用药"]:
                if "btnAddInd" in drug:
                    for ind in drug["btnAddInd"]:
                        if not validate_field(ind.get("ind_reptd_lltname", "")):
                            return False

        # 验证 reportSaeDetailInfo
        if "reportSaeDetailInfo" in target_data:
            for item in target_data["reportSaeDetailInfo"]:
                if not validate_field(item.get("desc_lltname", "")):
                    return False

        return True

    async def process_excel_file(self, file_content: bytes, file_path: str = None, file_uuid: str = None, image_file_uuid=None,feedback_code_result_map=None):
        """处理Excel文件内容"""
        try:
            # 使用BytesIO创建一个内存文件对象，避免文件IO问题
            excel_bytes_io = io.BytesIO(file_content)
            json_data = excel_to_json(excel_bytes_io)
            if json_data:
                target_data = convert_excel_json_to_target(json_data,feedback_code_result_map=feedback_code_result_map, file_uuid = file_uuid, image_file_uuid = image_file_uuid)
                validate_target_data = self.validate_target_data(target_data)
                report_id = await self.save_report_data(json_data, target_data)
                return {
                    "type": "message",
                    "status": "success", 
                    "file_path": file_path,
                    "file_name": os.path.basename(file_path) if file_path else "excel_file",
                    "file_uuid": file_uuid,
                    "report_id": report_id,
                    "json_data": json_data,
                    "target_data": target_data
                }
            return {"type": "error", "message": "无法解析Excel文件内容", "file_path": file_path}
        except Exception as e:
            logger.error(f"处理Excel文件失败: {str(e)}")
            if file_path:
                logger.error(f"文件路径: {file_path}")
            return {"type": "error", "message": str(e), "file_path": file_path}

    async def process_zip_file(self, zip_path: str, extract_dir: str):
        """处理ZIP文件"""
        try:
            # 确保提取目录存在
            os.makedirs(extract_dir, exist_ok=True)
            
            # 解压文件 - 使用zipfile模块的undocumented feature设置编码
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # 设置解码参数 - 这是一个未记录的属性，但能解决中文编码问题
                # 适用于Windows创建的ZIP中文文件名
                zip_ref._extract_member = lambda member, targetpath, pwd: \
                    zipfile.ZipFile._extract_member(zip_ref, member, targetpath, pwd)
                
                # 自动尝试不同编码方式
                original_extract_member = zip_ref._extract_member

                def extract_member_with_encoding(zip_ref_obj, original_extract_member, member, targetpath, pwd):
                    # 尝试使用不同编码处理文件名
                    try:
                        if not isinstance(member, zipfile.ZipInfo):
                            member = zip_ref_obj.getinfo(member)

                        # 尝试使用UTF-8和GBK双重解码
                        try:
                            # 先尝试UTF-8编码（适用于大多数Linux/Unix系统）
                            member.filename = member.filename.encode('cp437').decode('utf-8')
                        except UnicodeDecodeError:
                            # 如果失败，尝试GBK编码（适用于中文Windows系统）
                            member.filename = member.filename.encode('cp437').decode('gbk', errors='replace')
                    except:
                        # 出错时不改变原始文件名
                        pass

                    # 调用原始的提取方法
                    return original_extract_member(member, targetpath, pwd)

                # 替换提取方法
                zip_ref._extract_member = lambda member, targetpath, pwd: extract_member_with_encoding(zip_ref,
                                                                                                       original_extract_member,
                                                                                                       member,
                                                                                                       targetpath,
                                                                                                       pwd)
                # 执行解压
                zip_ref.extractall(extract_dir)

                # 处理嵌套的ZIP文件
                for file in zip_ref.namelist():
                    if file.lower().endswith('.zip'):
                        nested_zip_path = os.path.join(extract_dir, file)
                        nested_extract_dir = os.path.splitext(nested_zip_path)[0]
                        os.makedirs(nested_extract_dir, exist_ok=True)
                        try:
                            with zipfile.ZipFile(nested_zip_path, 'r') as nested_zip_ref:
                                nested_zip_original_extract_member = nested_zip_ref._extract_member
                                nested_zip_ref._extract_member = lambda member, targetpath, pwd: extract_member_with_encoding(nested_zip_ref, nested_zip_original_extract_member, member, targetpath, pwd)
                                nested_zip_ref.extractall(nested_extract_dir)
                            # 解压后删除嵌套的ZIP文件
                            os.remove(nested_zip_path)
                        except Exception as e:
                            logger.error(f"解压嵌套ZIP文件 {nested_zip_path} 失败: {str(e)}")
                
            logger.info(f"ZIP文件已解压到: {extract_dir}")

            json_files = []
            image_files = []
            excel_files = []

            # 遍历解压后的文件，分类收集 JSON、图片和 Excel 文件
            for root, _, files in os.walk(extract_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    if file.lower().endswith('.json'):
                        json_files.append(file_path)
                    elif file.lower().endswith(('.jpg', '.png')):
                        image_files.append(file_path)
                    elif file.lower().endswith(('.xlsx', '.xls')):
                        excel_files.append(file_path)

            # 检查图片文件数量
            if len(image_files) > 1:
                yield {"type": "error", "message": "ZIP文件中图片数量不能超过1个", "file_path": zip_path}
                # 处理完成后删除临时解压目录
                try:
                    shutil.rmtree(extract_dir)
                    logger.info(f"临时解压目录已删除: {extract_dir}")
                except Exception as e:
                    yield {"type": "error", "message": f"删除临时解压目录失败: {str(e)}"}
                return

            def process_json_files(files):
                # 合并所有JSON文件中的记录
                merged_data = []
                for file in files:
                    with open(file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        merged_data.extend(data)  # 支持单文件列表或多文件合并

                # 按FEEDBACK_CODE分组并排序
                feedback_map = {}
                for item in merged_data:
                    key = item.get("FEEDBACK_CODE")
                    if key not in feedback_map:
                        feedback_map[key] = []
                    feedback_map[key].append(item)

                # 对每个组按日期升序排序
                for key, entries in feedback_map.items():
                    entries.sort(key=lambda x: datetime.strptime(x["RECEIVED"], "%Y-%m-%d"))

                return feedback_map

            # 处理 JSON 文件
            feedback_code_result_map = process_json_files(json_files)  # 假设 process_json_files 是类方法
            # 这里可以添加处理 JSON 文件结果的逻辑，例如记录日志等

            image_file_uuid = None
            # 将文件复制到新位置
            for image_file in image_files:
                try:
                    # 生成图片文件的 UUID 和别名
                    image_file_uuid = str(uuid.uuid4()).replace('-', '')
                    image_file_alias_name = f"{image_file_uuid}-{os.path.basename(image_file)}"
                    # 构建图片的新文件路径
                    new_image_path = os.path.join(self.upload_file_path, image_file_alias_name)

                    # 复制图片文件到新位置
                    with open(image_file, 'rb') as src_img, open(new_image_path, 'wb') as dst_img:
                        dst_img.write(src_img.read())

                    # 保存图片文件记录
                    image_file_record_result = await self.save_file_record(
                        image_file_uuid,
                        os.path.basename(image_file),
                        image_file_alias_name,
                        new_image_path
                    )
                except Exception as e:
                    yield {"type": "error", "message": f"复制图片文件 {image_file} 时出错: {str(e)}"}

            # 处理解压后的每个Excel文件
            for excel_path in excel_files:
                try:
                    # 读取Excel文件内容
                    excel_content = None
                    excel_file = os.path.basename(excel_path)
                    with open(excel_path, 'rb') as ef:
                        excel_content = ef.read()

                    if not excel_content:
                        yield {"type": "error", "message": f"无法读取Excel文件内容: {excel_file}"}
                        continue

                    # 生成Excel文件的UUID和别名
                    excel_uuid = str(uuid.uuid4()).replace('-', '')
                    excel_alias_name = f"{excel_uuid}-{excel_file}"

                    # 构建新的文件路径（在upload_file_path目录下）
                    new_excel_path = os.path.join(self.upload_file_path, excel_alias_name)

                    # 将文件复制到新位置
                    with open(new_excel_path, 'wb') as nef:
                        nef.write(excel_content)

                    # 保存Excel文件记录
                    file_record_result = await self.save_file_record(
                        excel_uuid,
                        excel_file,
                        excel_alias_name,
                        new_excel_path
                    )


                    # 处理Excel文件并返回完整结果
                    process_result = await self.process_excel_file(excel_content, new_excel_path, excel_uuid,image_file_uuid,feedback_code_result_map)

                    # 合并文件记录和处理结果
                    if file_record_result["type"] == "message" and process_result["type"] == "message":
                        yield {
                            "type": "message",
                            "status": "success",
                            "file_name": excel_file,
                            "file_uuid": excel_uuid,
                            "file_path": new_excel_path,
                            "report_id": process_result.get("report_id"),
                            "json_data": process_result.get("json_data"),
                            "target_data": process_result.get("target_data")
                        }
                    else:
                        yield {
                            "type": "error",
                            "message": f"处理文件 {excel_file} 失败: {process_result.get('message', '未知错误')}",
                            "file_name": excel_file
                        }

                except Exception as e:
                    yield {"type": "error", "message": f"处理Excel文件 {excel_file} 时出错: {str(e)}"}
                    continue

                            
            # 处理完成后删除临时解压目录
            try:
                shutil.rmtree(extract_dir)
                logger.info(f"临时解压目录已删除: {extract_dir}")
            except Exception as e:
                yield {"type": "error", "message": f"删除临时解压目录失败: {str(e)}"}
                
        except Exception as e:
            yield {"type": "error", "message": f"处理ZIP文件失败: {str(e)}"}

    async def process_uploaded_file(self, file_content: bytes, file_name: str):
        """处理上传的文件内容 - 接收已读取的文件内容和文件名而不是UploadFile对象"""
        file_path = None
        try:
            # 生成文件UUID和路径
            file_uuid = str(uuid.uuid4()).replace('-', '')
            original_filename = file_name
            file_alias_name = f"{file_uuid}-{original_filename}"
            file_path = os.path.join(self.upload_file_path, file_alias_name)
            
            # 直接保存文件到磁盘
            with open(file_path, "wb") as f:
                f.write(file_content)
            
            # 保存文件记录
            file_record_result = await self.save_file_record(
                file_uuid,
                original_filename,
                file_alias_name,
                file_path
            )
            
            # 根据文件类型处理
            if original_filename.lower().endswith('.zip'):
                extract_dir = os.path.join(self.upload_file_path, file_uuid)
                os.makedirs(extract_dir, exist_ok=True)
                async for result in self.process_zip_file(file_path, extract_dir):
                    yield result
            elif original_filename.lower().endswith(('.xlsx', '.xls')):
                # 处理Excel文件
                process_result = await self.process_excel_file(file_content, file_path, file_uuid)
                if process_result["type"] == "message" and file_record_result["type"] == "message":
                    yield {
                        "type": "message",
                        "status": "success",
                        "file_name": original_filename,
                        "file_uuid": file_uuid,
                        "file_path": file_path,
                        "report_id": process_result.get("report_id"),
                        "json_data": process_result.get("json_data"),
                        "target_data": process_result.get("target_data")
                    }
                else:
                    yield {
                        "type": "error",
                        "message": f"处理文件 {original_filename} 失败: {process_result.get('message', '未知错误')}",
                        "file_name": original_filename
                    }
            else:
                yield {"type": "error", "message": "不支持的文件格式，请上传.zip、.xlsx或.xls文件"}
                
        except Exception as e:
            logger.error(f"处理上传文件失败: {str(e)}")
            # 如果处理过程中出错，尝试删除已保存的文件
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except Exception as cleanup_error:
                    logger.error(f"清理文件失败: {str(cleanup_error)}")
            yield {"type": "error", "message": str(e)}

    async def upload_and_process_file(self, file: UploadFile):
        """上传并处理文件（兼容旧接口）"""
        try:
            # 读取文件内容到内存中
            file_content = await file.read()
            file_name = file.filename
            
            # 调用新的处理方法
            async for result in self.process_uploaded_file(file_content, file_name):
                yield result
        except Exception as e:
            logger.error(f"上传处理文件失败: {str(e)}")
            yield {"type": "error", "message": str(e)}

    async def process_saved_file(self, file_path: str, file_name: str, file_uuid: str):
        """处理已保存到磁盘的文件"""
        try:
            # 构建文件别名
            file_alias_name = f"{file_uuid}-{file_name}"
            
            # 保存文件记录到数据库
            file_record_result = await self.save_file_record(
                file_uuid,
                file_name,
                file_alias_name,
                file_path
            )
            
            # 根据文件类型处理
            if file_name.lower().endswith('.zip'):
                # 创建提取目录
                extract_dir = os.path.join(self.upload_file_path, file_uuid)
                os.makedirs(extract_dir, exist_ok=True)
                
                # 处理ZIP文件
                async for result in self.process_zip_file(file_path, extract_dir):
                    yield result
            elif file_name.lower().endswith(('.xlsx', '.xls')):
                # 读取文件内容用于处理
                with open(file_path, 'rb') as f:
                    file_content = f.read()
                
                # 处理Excel文件
                process_result = await self.process_excel_file(file_content, file_path, file_uuid)
                
                # 返回处理结果
                if process_result["type"] == "message" and file_record_result["type"] == "message":
                    yield {
                        "type": "message",
                        "status": "success",
                        "file_name": file_name,
                        "file_uuid": file_uuid,
                        "file_path": file_path,
                        "report_id": process_result.get("report_id"),
                        "json_data": process_result.get("json_data"),
                        "target_data": process_result.get("target_data")
                    }
                else:
                    yield {
                        "type": "error",
                        "message": f"处理文件 {file_name} 失败: {process_result.get('message', '未知错误')}",
                        "file_name": file_name
                    }
            else:
                yield {"type": "error", "message": "不支持的文件格式，请上传.zip、.xlsx或.xls文件"}
                
        except Exception as e:
            logger.error(f"处理已保存文件失败: {str(e)}")
            yield {"type": "error", "message": str(e)} 