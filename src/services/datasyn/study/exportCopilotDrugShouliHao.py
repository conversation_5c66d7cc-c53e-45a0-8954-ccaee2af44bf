import pymysql
import pandas as pd
import re
from openpyxl import load_workbook
from openpyxl.styles import Pattern<PERSON>ill
from typing import List


def generate_clinical_drug_report(tenant_ids: List[str]):
    """
    生成临床研究药物报告
    :param tenant_ids: 租户ID列表，例如 ['inxmed', 'tenant2']
    """
    # 连接 MySQL 数据库
    connection = pymysql.connect(
        host='**********',
        user='root',
        password='Mysql#r2368',
        # database='pv_copilot_report_process',
        database='pv_copilot_report_process_test',
        charset='utf8mb4'
    )


    try:
        with connection.cursor() as cursor:
            # 查询指定租户的 study_info 数据
            study_info_sql = """
            SELECT 
                tenant_id,
                study_num,
                REPLACE(REPLACE(REPLACE(REPLACE(study_data, '" |   |
| ', ''), '"', ''),'“',''),'”','')
            FROM study_info_test
            WHERE tenant_id IN (%s)
            """ % ','.join(['%s'] * len(tenant_ids))

            cursor.execute(study_info_sql, tenant_ids)
            study_info_records = cursor.fetchall()

            # 按租户分组处理
            tenant_data = {}
            for record in study_info_records:
                tenant_id = record[0]
                if tenant_id not in tenant_data:
                    tenant_data[tenant_id] = []
                tenant_data[tenant_id].append(record)

            # 处理每个租户的数据
            for tenant_id, studies in tenant_data.items():
                process_tenant_data(connection, tenant_id, studies)

    finally:
        connection.close()


def process_tenant_data(connection, tenant_id: str, studies: list):
    """
    处理单个租户的数据
    """
    all_results = []
    fill_red = PatternFill(start_color="FF0000", end_color="FF0000", fill_type="solid")

    with connection.cursor() as cursor:
        for study_info in studies:
            study_num = study_info[1]
            study_data = study_info[2]

            # 提取药物信息
            description, acceptance_number, drug_list = extract_drug_info(study_data)

            # 查询dictionary_mapping表
            dict_mapping_sql = """
            SELECT content 
            FROM dictionary_mapping_test 
            WHERE project_code = %s 
                AND company_code = %s 
                AND placeholder = '{pat_exposure}'
            """
            cursor.execute(dict_mapping_sql, (study_num, tenant_id))
            dict_mapping_result = cursor.fetchone()

            process_study_data(
                description = description,
                all_results=all_results,
                tenant_id=tenant_id,
                study_num=study_num,
                study_data=study_data,
                acceptance_number=acceptance_number,
                drug_list=drug_list,
                dict_mapping_result=dict_mapping_result
            )

    # 生成Excel文件
    if all_results:
        generate_excel_file(tenant_id, all_results, fill_red)


def extract_drug_info(study_data: str) -> tuple:
    """
    提取药物编号、受理号和药物列表
    """
    description = ""

    drug_list = []
    acceptance_number = None

    if '（研究药物受理号包括：' in study_data:
        start = study_data.find('（研究药物受理号包括：') + len('（研究药物受理号包括：')
        end = study_data.find('）', start)
        acceptance_number = study_data[start:end]
        description = study_data.split('（研究药物受理号包括：')[0]
    else:
        match = re.search(r'(.*?（方案编号：.*?）)(.*)', study_data, re.DOTALL)
        if match:
            description = match.group(1)
            pattern = r"（方案编号：(\S+)）"
            description = re.sub(pattern, "", description)

            acceptance_info = match.group(2)
            drugs = acceptance_info.split('\r\n')
            for drug in drugs:
                if '的受理号包括：' in drug:
                    name, numbers = drug.split('的受理号包括：')
                    drug_list.append((name.strip(), numbers.replace('。', '').strip()))
        elif  '的受理号包括：' in study_data:
            if '\r\n' in study_data:
                drugs = study_data.split('\r\n')
            elif '\n' in study_data:
                drugs = study_data.split('\n')

            for drug in drugs:
                if '的受理号包括：' in drug:
                    name, numbers = drug.split('的受理号包括：')
                    drug_list.append((name.strip(), numbers.replace('。', '').strip()))
                else:
                    description = drug
        else:
            # 正则表达式匹配括号中的方案编号
            pattern = r"\(方案编号：(\S+)\)"
            # 替换操作，将匹配到的部分替换为空
            description = re.sub(pattern, "", study_data)
            pattern1 = r"\（方案编号：(\S+)\）"
            # 替换操作，将匹配到的部分替换为空
            description = re.sub(pattern1, "", description)

    return description, acceptance_number, drug_list


def process_study_data(description: str, all_results: list, tenant_id: str, study_num: str,
                       study_data: str, acceptance_number: str,drug_list: list,
                       dict_mapping_result: tuple):
    """
    处理单个研究方案的数据
    """
    # base_info = {
    #     "租户编号": tenant_id,
    #     "方案编号": study_num,
    #     "研究药物名称（中文）": "",
    #     "研究药物编号": "",
    #     "研究药物名称（英文）": "",
    #     "受理号": acceptance_number or "",
    #     "方案描述(中文)": description,
    #     "方案描述(英文)": '',
    # }
    base_info = [tenant_id, study_num,"","", acceptance_number or "" ,description,""]

    if study_num.strip() == 'KL166-I-01-CTP':
        print(111)
    if dict_mapping_result:
        content = dict_mapping_result[0]
        rows = content.strip().split('\n')
        drug_acceptance_mapping = {drug[0]: drug[1] for drug in drug_list}  # 提取药物名称与受理号的映射关系

        first = True
        for row in rows:
            columns = row.split('|')
            if len(columns) > 2:
                drug_name, drug_code = columns[1].strip(), columns[2].strip()

                # 尝试从药物-受理号映射中匹配药物名称
                mapped_acceptance_number = drug_acceptance_mapping.get(drug_name, '')
                # 当 drug_list 是空列表 且 acceptance_number 不为 None 或 空串 时执行
                if not drug_list and acceptance_number:
                    if first:
                        mapped_acceptance_number = acceptance_number
                        first = False

                description_en = ''
                all_results.append([
                    tenant_id, study_num, drug_name, drug_code, mapped_acceptance_number, description,description_en
                ])
    else:
        all_results.append(base_info)



def generate_excel_file(tenant_id: str, all_results: list, fill_red):
    """
    生成 Excel 文件并保存
    """
    columns = ['租户编号', '方案编号', '研究药物名称（中文）', '研究药物编号', '受理号', '方案描述(中文)','方案描述(英文)']
    df = pd.DataFrame(all_results, columns=columns)
    file_name = f'{tenant_id}_临床研究药物报告.xlsx'
    df.to_excel(file_name, index=False)

    # 加载生成的Excel文件并修改格式
    workbook = load_workbook(file_name)
    sheet = workbook.active

    # 添加红色背景
    for row in sheet.iter_rows(min_row=2, max_row=sheet.max_row, min_col=1, max_col=sheet.max_column):
        if row[2].value is None or row[3].value is None:
            for cell in row:
                cell.fill = fill_red

    workbook.save(file_name)
    print(f"文件已保存为 {file_name}")





# 使用示例（支持单个或多个租户）
if __name__ == "__main__":
    # 示例1：处理单个租户
    generate_clinical_drug_report(['kelun'])
    # generate_clinical_drug_report(['LaNova','inxmed','miracogen','hspharm','tykmedicines'])

    # 示例2：处理多个租户
    # generate_clinical_drug_report(['inxmed', 'tenant2', 'tenant3'])