import os
from collections import defaultdict
from datetime import datetime

import pandas as pd
from sqlalchemy import text
from nanoid import generate

from src.common.logger import logger
from src.database.db_mysql import db_session, prod_db_session

# sheet页名称到field_name的映射
SHEET_TO_FIELD_MAP = {
    '给药途径': 'TXT_cdr_admin_route_id',
    '剂量单位': 'TXT_cdr_dose_unit_id',
    '转归': 'TXT_evt_outcome_id',
    '剂型': 'TXT_formulation_id',
    '频率': 'TXT_freq_id',
    '实验室检查单位': 'TXT_labunit_0',
    '民族': 'TXT_pat_ethnic_group_id',
    '评价结果': 'assessmentResult',
    '评价来源': 'assessmentSource',
    '给药措施': 'TXT_act_taken_id',
    '病史类型': 'TXT_Rel_Hist_Table_pat_hist_type'
}

# 需要排除的租户
EXCLUDED_TENANT = 'kelun'


def validate_sheet_name(sheet_name):
    """验证sheet名称是否在映射中存在"""
    if sheet_name not in SHEET_TO_FIELD_MAP:
        error_msg = f"未找到sheet '{sheet_name}' 对应的field_name映射"
        logger.error(error_msg)
        raise ValueError(error_msg)
    
    field_name = SHEET_TO_FIELD_MAP[sheet_name]
    logger.info(f"Sheet '{sheet_name}' 映射到 field_name: {field_name}")
    return field_name


def clear_tenant_data(session, tenant_id):
    """清除指定租户的所有字典数据"""
    # 如果是被排除的租户，跳过清理
    if tenant_id.lower() == EXCLUDED_TENANT:
        logger.info(f"跳过租户 {tenant_id} 的数据清理（排除的租户）")
        return

    try:
        delete_sql = text("""
        DELETE FROM t_system_dict 
        WHERE tenant_id = :tenant_id
        """)
        result = session.execute(delete_sql, {'tenant_id': tenant_id})
        logger.info(f"已清除租户 {tenant_id} 的所有字典数据，影响行数：{result.rowcount}")
        session.commit()
    except Exception as e:
        logger.error(f"清除租户 {tenant_id} 的字典数据失败: {str(e)}")
        session.rollback()
        raise e


def print_statistics(stats):
    """打印统计信息"""
    logger.info("\n" + "=" * 50)
    logger.info("数据导入统计信息：")
    logger.info("=" * 50)

    logger.info("\n租户统计：")
    for tenant_id, tenant_stats in stats['tenants'].items():
        logger.info(f"\n租户: {tenant_id}")
        logger.info(f"  - 待删除记录数: {tenant_stats['delete_count']}")
        logger.info(f"  - 待插入记录数: {tenant_stats['insert_count']}")
        logger.info("  字段统计：")
        for field_name, count in tenant_stats['fields'].items():
            logger.info(f"    - {field_name}: {count}条")

    logger.info("\n总计：")
    logger.info(f"- 涉及租户数: {len(stats['tenants'])}")
    logger.info(f"- 总待删除记录数: {stats['total_delete_count']}")
    logger.info(f"- 总待插入记录数: {stats['total_insert_count']}")
    logger.info("=" * 50 + "\n")


def get_tenant_record_count(session, tenant_id):
    """获取指定租户的现有记录数"""
    try:
        count_sql = text("""
        SELECT COUNT(*) as count 
        FROM t_system_dict 
        WHERE tenant_id = :tenant_id
        """)
        result = session.execute(count_sql, {'tenant_id': tenant_id}).scalar()
        return result
    except Exception as e:
        logger.error(f"查询租户 {tenant_id} 的记录数失败: {str(e)}")
        return 0


def process_excel_data(excel_file_path, session=None):
    """
    处理Excel文件中的数据并保存到数据库
    
    参数:
    excel_file_path: Excel文件路径
    session: 数据库会话对象，如果提供则在此会话中执行操作，否则创建新会话
    
    返回:
    bool: 是否成功处理数据
    """
    try:
        print(f"开始处理Excel文件: {excel_file_path}")
            
        # 读取Excel文件中的所有sheet
        excel = pd.ExcelFile(excel_file_path)
        sheet_names = excel.sheet_names
        
        # 初始化统计信息字典
        stats = {}
        
        # 记录需要删除的记录
        to_delete_records = {}
        
        # 批量处理的数据集合
        batch_inserts = []
        batch_updates = []
        
        # 检查是否提供了外部session
        session_provided = session is not None
        # 如果没有提供session，则创建一个新的session
        if not session_provided:
            session = db_session()
            
        try:
            # 获取所有涉及到的租户ID集合
            all_tenants = set()
            for sheet_name in sheet_names:
                if sheet_name not in SHEET_TO_FIELD_MAP:
                    continue
                    
                # 读取当前sheet的数据
                df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
                if df.empty or '租户编号' not in df.columns:
                    continue
                    
                # 收集所有有效的租户ID
                tenants = set(df['租户编号'].dropna().astype(str).str.strip().unique())
                all_tenants.update(tenants)
            
            # 排除特定租户
            all_tenants = {t for t in all_tenants if t.lower() != EXCLUDED_TENANT}
            print(f"检测到的租户: {', '.join(all_tenants)}")
            
            # 预先获取所有字段的数据库记录
            print("预加载租户数据库记录以提高处理效率...")
            db_records_cache = {}
            for tenant_id in all_tenants:
                # 获取该租户的所有字典记录
                records = session.execute(
                    text("""
                        SELECT id, field_name, dict_cn, dict_en 
                        FROM t_system_dict 
                        WHERE tenant_id = :tenant_id
                            AND is_delete = 0
                    """),
                    {"tenant_id": tenant_id}
                ).fetchall()
                
                # 按字段名组织记录
                tenant_records = defaultdict(list)
                for record in records:
                    tenant_records[record[1]].append({
                        'id': record[0],
                        'field_name': record[1],
                        'dict_cn': record[2] if record[2] is not None else "",
                        'dict_en': record[3] if record[3] is not None else ""
                    })
                
                db_records_cache[tenant_id] = tenant_records
                print(f"  租户 {tenant_id}: 加载了 {len(records)} 条记录")
            
            for sheet_name in sheet_names:
                # 读取当前sheet的数据
                df = pd.read_excel(excel_file_path, sheet_name=sheet_name)
                
                # 跳过空sheet
                if df.empty:
                    print(f"跳过空sheet: {sheet_name}")
                    continue
                
                # 跳过不在映射中的sheet
                if sheet_name not in SHEET_TO_FIELD_MAP:
                    print(f"跳过未映射的sheet: {sheet_name}")
                    continue
                
                # 判断是否包含必要的列
                required_columns = ['标准名称（中文）', '标准名称（英文）', '租户编号']
                if not all(col in df.columns for col in required_columns):
                    missing_cols = [col for col in required_columns if col not in df.columns]
                    print(f"警告: sheet '{sheet_name}' 缺少必要的列: {missing_cols}")
                    continue
                
                # 处理当前sheet的数据
                print(f"处理 sheet: {sheet_name}")
                total_rows = len(df)
                insert_count = 0
                update_count = 0
                skip_count = 0
                
                # 获取对应的field_name
                try:
                    field_name = validate_sheet_name(sheet_name)
                except ValueError as e:
                    print(f"  跳过sheet '{sheet_name}': {str(e)}")
                    continue
                
                # 获取当前sheet相关的租户ID
                sheet_tenants = set(df['租户编号'].dropna().astype(str).str.strip().unique())
                sheet_tenants = {t for t in sheet_tenants if t.lower() != EXCLUDED_TENANT}
                
                # 如果该sheet没有有效租户，跳过
                if not sheet_tenants:
                    print(f"  跳过sheet '{sheet_name}': 未找到有效的租户ID")
                    continue
                
                # 批量处理所有租户的记录
                for tenant_id in sheet_tenants:
                    print(f"  处理租户 {tenant_id} 的数据...")
                    
                    # 获取该租户和字段的数据库记录
                    db_records = db_records_cache.get(tenant_id, {}).get(field_name, [])
                    
                    # 构建数据库记录的映射以加速查找
                    db_dict = {}  # (dict_cn, dict_en) -> record_id
                    db_cn_dict = {}  # dict_cn -> [record_ids]
                    db_en_dict = {}  # dict_en -> [record_ids]
                    
                    for record in db_records:
                        key = (record['dict_cn'], record['dict_en'])
                        db_dict[key] = record['id']
                        
                        if record['dict_cn']:
                            if record['dict_cn'] not in db_cn_dict:
                                db_cn_dict[record['dict_cn']] = []
                            db_cn_dict[record['dict_cn']].append(record['id'])
                        
                        if record['dict_en']:
                            if record['dict_en'] not in db_en_dict:
                                db_en_dict[record['dict_en']] = []
                            db_en_dict[record['dict_en']].append(record['id'])
                    
                    # 获取Excel中该租户的所有记录
                    tenant_df = df[df['租户编号'].astype(str).str.strip() == tenant_id]
                    
                    # 记录Excel中出现的记录ID
                    found_record_ids = set()
                    
                    # 批量处理该租户的所有Excel记录
                    excel_records = []
                    for _, row in tenant_df.iterrows():
                        dict_cn = str(row['标准名称（中文）']).strip() if not pd.isna(row['标准名称（中文）']) else ""
                        dict_en = str(row['标准名称（英文）']).strip() if not pd.isna(row['标准名称（英文）']) else ""
                        
                        # 跳过无效数据
                        if not (dict_cn or dict_en):
                            skip_count += 1
                            continue
                        
                        excel_records.append({
                            'dict_cn': dict_cn,
                            'dict_en': dict_en
                        })
                    
                    # 处理每条Excel记录
                    for record in excel_records:
                        dict_cn = record['dict_cn']
                        dict_en = record['dict_en']
                        
                        # 检查是否完全匹配
                        key = (dict_cn, dict_en)
                        if key in db_dict:
                            # 记录已存在，标记为找到
                            found_record_ids.add(db_dict[key])
                            skip_count += 1
                            continue
                        
                        # 检查是否需要更新（中文或英文匹配）
                        to_update_id = None
                        
                        # 检查中文匹配
                        if dict_cn and dict_cn in db_cn_dict:
                            for record_id in db_cn_dict[dict_cn]:
                                if record_id not in found_record_ids:
                                    to_update_id = record_id
                                    break
                        
                        # 如果中文没找到，检查英文匹配
                        if not to_update_id and dict_en and dict_en in db_en_dict:
                            for record_id in db_en_dict[dict_en]:
                                if record_id not in found_record_ids:
                                    to_update_id = record_id
                                    break
                        
                        if to_update_id:
                            # 需要更新记录
                            batch_updates.append({
                                'id': to_update_id,
                                'dict_cn': dict_cn,
                                'dict_en': dict_en
                            })
                            found_record_ids.add(to_update_id)
                            update_count += 1
                        else:
                            # 插入新记录
                            batch_inserts.append({
                                'id': generate(size=21),
                                'field_name': field_name,
                                'tenant_id': tenant_id,
                                'dict_cn': dict_cn,
                                'dict_en': dict_en
                            })
                            insert_count += 1
                    
                    # 找出数据库中存在但Excel中不存在的记录
                    to_delete_ids = [record['id'] for record in db_records if record['id'] not in found_record_ids]
                    
                    if to_delete_ids:
                        to_delete_records_detail = []
                        for record in db_records:
                            if record['id'] not in found_record_ids:
                                to_delete_records_detail.append({
                                    'id': record['id'],
                                    'dict_cn': record['dict_cn'],
                                    'dict_en': record['dict_en']
                                })
                        
                        key = f"{tenant_id}_{field_name}"
                        to_delete_records[key] = {
                            'tenant_id': tenant_id,
                            'field_name': field_name,
                            'record_ids': to_delete_ids,
                            'count': len(to_delete_ids),
                            'details': to_delete_records_detail
                        }
                        print(f"  找到 {len(to_delete_ids)} 条数据库中存在但Excel中不存在的记录 (租户: {tenant_id}, 字段: {field_name})")
                
                # 保存当前sheet的统计信息
                stats[sheet_name] = {
                    'total': total_rows,
                    'insert': insert_count,
                    'update': update_count,
                    'skip': skip_count
                }
                
                print(f"处理完毕 sheet '{sheet_name}': 总行数={total_rows}, 插入={insert_count}, 更新={update_count}, 跳过={skip_count}")
            
            # 汇总处理数据
            print("\n=== 数据处理汇总 ===")
            total_insert = 0
            total_update = 0
            total_skip = 0
            for sheet_name, sheet_stats in stats.items():
                print(f"表格 {sheet_name}: 插入={sheet_stats['insert']}, 更新={sheet_stats['update']}, 跳过={sheet_stats['skip']}")
                total_insert += sheet_stats['insert']
                total_update += sheet_stats['update']
                total_skip += sheet_stats['skip']
            
            print(f"\n总计: 插入={total_insert}, 更新={total_update}, 跳过={total_skip}")
            print(f"批量插入记录数: {len(batch_inserts)}, 批量更新记录数: {len(batch_updates)}")
            
            # 显示需要删除的记录统计
            total_to_delete = sum(info['count'] for info in to_delete_records.values())
            if total_to_delete > 0:
                print("\n=== 需要删除的记录 ===")
                for key, info in to_delete_records.items():
                    print(f"\n租户: {info['tenant_id']}, 字段: {info['field_name']}, 记录数: {info['count']}")
                    print("具体记录列表:")
                    print(f"{'ID':<25} | {'中文名称':<30} | {'英文名称':<30}")
                    print("-" * 90)
                    for item in info['details'][:10]:  # 仅显示前10条，避免输出过多
                        dict_cn = item['dict_cn'] if item['dict_cn'] is not None else ""
                        dict_en = item['dict_en'] if item['dict_en'] is not None else ""
                        print(f"{item['id']:<25} | {dict_cn:<30} | {dict_en:<30}")
                    if len(info['details']) > 10:
                        print(f"... 还有 {len(info['details']) - 10} 条记录未显示")
                print(f"\n总计需要删除: {total_to_delete} 条记录")
                
                # 手动确认是否提交事务
                # 无论是否提供外部session都进行确认
                confirm = input("\n请确认是否提交上述更改到数据库? (y/n): ")
                if confirm.lower() == 'y':
                    # 执行批量插入
                    if batch_inserts:
                        print(f"\n正在批量插入 {len(batch_inserts)} 条记录...")
                        # 分批插入，每次最多500条
                        batch_size = 500
                        for i in range(0, len(batch_inserts), batch_size):
                            batch = batch_inserts[i:i+batch_size]
                            # 构建批量插入的参数列表
                            values_list = []
                            params = {}
                            for idx, record in enumerate(batch):
                                param_prefix = f"p{idx}_"
                                values_list.append(f"(:{param_prefix}id, :{param_prefix}field_name, :{param_prefix}tenant_id, :{param_prefix}dict_cn, :{param_prefix}dict_en, 0, NOW(), NOW(), 'user', 'user')")
                                params[f"{param_prefix}id"] = record['id']
                                params[f"{param_prefix}field_name"] = record['field_name']
                                params[f"{param_prefix}tenant_id"] = record['tenant_id']
                                params[f"{param_prefix}dict_cn"] = record['dict_cn']
                                params[f"{param_prefix}dict_en"] = record['dict_en']
                            
                            # 执行批量插入
                            sql = f"""
                                INSERT INTO t_system_dict 
                                (id, field_name, tenant_id, dict_cn, dict_en, is_delete, create_time, update_time, create_by, update_by) 
                                VALUES {','.join(values_list)}
                            """
                            session.execute(text(sql), params)
                            print(f"  已插入 {len(batch)} 条记录")
                    
                    # 执行批量更新
                    if batch_updates:
                        print(f"\n正在批量更新 {len(batch_updates)} 条记录...")
                        # 分批更新，每次最多500条
                        batch_size = 500
                        for i in range(0, len(batch_updates), batch_size):
                            batch = batch_updates[i:i+batch_size]
                            for record in batch:
                                # 单条更新，但在一个批次中执行
                                session.execute(
                                    text("""
                                        UPDATE t_system_dict
                                        SET dict_cn = :dict_cn, dict_en = :dict_en, update_time = NOW(), update_by = 'user'
                                        WHERE id = :id
                                    """),
                                    record
                                )
                            print(f"  已更新 {len(batch)} 条记录")
                    
                    # 执行删除操作
                    if total_to_delete > 0:
                        deleted_count = 0
                        print("\n=== 执行删除操作 ===")
                        for key, info in to_delete_records.items():
                            if not info['record_ids']:
                                continue
                            
                            # 分批执行，避免参数过多
                            batch_size = 1000
                            for i in range(0, len(info['record_ids']), batch_size):
                                batch_ids = info['record_ids'][i:i+batch_size]
                                # 执行软删除
                                try:
                                    # 更新is_delete字段为1
                                    result = session.execute(
                                        text("""
                                            UPDATE t_system_dict
                                            SET is_delete = 1, update_time = NOW(), update_by = 'user'
                                            WHERE id IN :record_ids
                                        """),
                                        {"record_ids": tuple(batch_ids)}
                                    )
                                    deleted_count += result.rowcount
                                    print(f"已标记删除租户 {info['tenant_id']} 字段 {info['field_name']} 的 {result.rowcount} 条记录")
                                except Exception as e:
                                    print(f"删除记录失败: {str(e)}")
                                    raise e
                            
                        print(f"\n总计已标记删除: {deleted_count} 条记录")
                    
                    # 只有在确认后才提交事务
                    # 如果使用的是外部session，则不在这里提交，而是通知调用方已确认
                    if not session_provided:
                        session.commit()
                        print("✅ 事务提交成功")
                    return True
                else:
                    # 如果拒绝确认，回滚事务并返回False
                    if not session_provided:
                        session.rollback()
                    print("❌ 已拒绝更改，数据将不会保存")
                    return False
            else:
                # 如果有插入或更新但没有删除的记录
                if batch_inserts or batch_updates:
                    confirm = input("\n未发现需要删除的记录，但有新增或更新。请确认是否提交上述更改到数据库? (y/n): ")
                    if confirm.lower() == 'y':
                        # 执行批量插入
                        if batch_inserts:
                            print(f"\n正在批量插入 {len(batch_inserts)} 条记录...")
                            # 分批插入，每次最多500条
                            batch_size = 500
                            for i in range(0, len(batch_inserts), batch_size):
                                batch = batch_inserts[i:i+batch_size]
                                # 构建批量插入的参数列表
                                values_list = []
                                params = {}
                                for idx, record in enumerate(batch):
                                    param_prefix = f"p{idx}_"
                                    values_list.append(f"(:{param_prefix}id, :{param_prefix}field_name, :{param_prefix}tenant_id, :{param_prefix}dict_cn, :{param_prefix}dict_en, 0, NOW(), NOW(), 'user', 'user')")
                                    params[f"{param_prefix}id"] = record['id']
                                    params[f"{param_prefix}field_name"] = record['field_name']
                                    params[f"{param_prefix}tenant_id"] = record['tenant_id']
                                    params[f"{param_prefix}dict_cn"] = record['dict_cn']
                                    params[f"{param_prefix}dict_en"] = record['dict_en']
                                
                                # 执行批量插入
                                sql = f"""
                                    INSERT INTO t_system_dict 
                                    (id, field_name, tenant_id, dict_cn, dict_en, is_delete, create_time, update_time, create_by, update_by) 
                                    VALUES {','.join(values_list)}
                                """
                                session.execute(text(sql), params)
                                print(f"  已插入 {len(batch)} 条记录")
                        
                        # 执行批量更新
                        if batch_updates:
                            print(f"\n正在批量更新 {len(batch_updates)} 条记录...")
                            # 分批更新，每次最多500条
                            batch_size = 500
                            for i in range(0, len(batch_updates), batch_size):
                                batch = batch_updates[i:i+batch_size]
                                for record in batch:
                                    # 单条更新，但在一个批次中执行
                                    session.execute(
                                        text("""
                                            UPDATE t_system_dict
                                            SET dict_cn = :dict_cn, dict_en = :dict_en, update_time = NOW(), update_by = 'user'
                                            WHERE id = :id
                                        """),
                                        record
                                    )
                                print(f"  已更新 {len(batch)} 条记录")
                        
                        # 提交事务
                        if not session_provided:
                            session.commit()
                            print("✅ 事务提交成功")
                        return True
                    else:
                        # 如果拒绝确认，回滚事务并返回False
                        if not session_provided:
                            session.rollback()
                        print("❌ 已拒绝更改，数据将不会保存")
                        return False
                else:
                    # 没有任何变更
                    print("\n没有任何需要保存的变更")
                    if not session_provided:
                        session.commit()
                    return True
        except Exception as e:
            # 只有在没有提供外部session的情况下才回滚事务
            if not session_provided:
                session.rollback()
            print(f"处理Excel数据时出错: {str(e)}")
            raise
            
        finally:
            # 只有在没有提供外部session的情况下才关闭session
            if not session_provided:
                session.close()
                
    except Exception as e:
        print(f"打开Excel文件出错: {str(e)}")
        return False


def main():
    """主函数"""
    # 获取脚本所在目录的绝对路径
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # 构建相对于脚本位置的Excel文件路径
    excel_path = os.path.join(script_dir, 'excelfile', 'CODE LIST RPT_20250110.xls')
    # 规范化路径
    excel_path = os.path.normpath(excel_path)

    try:
        # 检查文件是否存在
        if not os.path.exists(excel_path):
            logger.error(f"Excel文件不存在: {excel_path}")
            return

        # 让用户选择运行环境
        while True:
            env = input("请选择运行环境 (test/prod): ").lower()
            if env in ['test', 'prod']:
                break
            print("无效的环境选择，请输入 'test' 或 'prod'")

        # 二次确认生产环境操作
        if env == 'prod':
            confirm = input("⚠️ 警告：您选择了生产环境，请再次确认 (yes/no): ")
            if confirm.lower() != 'yes':
                logger.info("用户取消了生产环境操作")
                return

        logger.info(f"当前选择的运行环境: {env}")
        logger.info(f"使用Excel文件: {excel_path}")
        if process_excel_data(excel_path):  # 检查返回值
            logger.info("所有数据导入成功！")
    except Exception as e:
        logger.error(f"数据导入失败：{str(e)}")


if __name__ == "__main__":
    main()
