import re
from io import StringIO

import pandas as pd
import os
import logging
from datetime import datetime
from sqlalchemy.orm import Session
from src.database.db_mysql import db_session
from src.database.models.entity import DictionaryMapping
from src.services.datasyn.config import TXT_formulation_id_config
# 导入SKIP_PLACEHOLDERS
from src.services.datasyn.config.config import SKIP_PLACEHOLDERS


# 清理单元格内容中的不规范数据
def clean_cell_value(value, tenant_code=None):
    """
    清理单元格值中的不规范数据，例如移除额外的空格和斜杠包围的编码
    
    Args:
        value: 单元格原始值
        tenant_code: 租户代码，如'kelun'、'avb'等
        
    Returns:
        str: 清理后的值
    """
    if not isinstance(value, str):
        return value
    
    # 只有kelun租户才需要清理，其他租户直接返回原值
    if tenant_code is None or tenant_code.lower() != 'kelun':
        return value
        
    # 去除多余的空格
    cleaned_value = value.strip()
    
    # 使用更灵活的正则表达式，匹配形如 "BENADRYL /00000402/" 的格式
    # 特征: 主要内容后有空格，接着是一个斜杠，然后是纯数字，再一个斜杠
    pattern = r'(.+?)\s+/(\d+)/'
    
    match = re.search(pattern, cleaned_value)
    if match:
        # 检查第一个捕获组是否是正常的术语
        main_part = match.group(1).strip()
        
        # 检查第二个捕获组是数字编码
        code_part = match.group(2)
        
        cleaned_value = main_part
    
    return cleaned_value


def assemble_read_company_excel_file(company_code,language, study_ids, source_file_path, sheet_names, columns_of_interest_dict,
                                     sheet_target_file_dict, cache):
    # 过滤特定的项目的列，项目名称
    # avb = "AVB"
    # Study_ID = 'Study ID'
    # if avb==company_code:
    #     Study_ID = '方案编号'
    Study_ID = '方案编号'

    # 缓存解析excel文件内容，用于填写placeholder
    fileDataDict = {}
    
    # 确定是否是kelun租户
    is_kelun = company_code and company_code.lower() == 'kelun'
    cleaned_data_counter = None
    
    # 用于非kelun公司存储1003临床研究药物表格的pat_exposure数据
    excel_pat_exposure_data = {}
    
    if is_kelun:
        # 添加数据清理统计计数器
        cleaned_data_counter = {
            'total_cells': 0,  # 处理过的单元格总数
            'cleaned_cells': 0,  # 实际被清理的单元格数
            'sheets': {}  # 每个sheet的清理统计
        }

    for sheet_name in sheet_names:
        # 只有kelun租户才初始化当前sheet的统计数据
        if is_kelun:
            cleaned_data_counter['sheets'][sheet_name] = {
                'total': 0,
                'cleaned': 0
            }
        
        sheet_placeholder_list = sheet_target_file_dict[sheet_name]
        sheet_placeholder = sheet_placeholder_list[0]
        try:
            # 读取指定Sheet中的数据
            df = pd.read_excel(source_file_path, sheet_name=sheet_name)
            print(f"Excel文件读取成功: {sheet_name}")
        except Exception as e:
            print(f"读取Excel文件时出错：{e}")
            continue

        # 获取当前Sheet对应的列
        columns_of_interest = columns_of_interest_dict[sheet_name]

        # 检查列是否存在于数据中
        missing_columns = [col for col in columns_of_interest if col not in df.columns]
        if missing_columns:
            print(f"以下列在Excel中找不到：{missing_columns}")
            continue

        # 将所有列的数据类型转换为字符串
        df = df.astype(str)
        
        filtered_df = df[
            df[Study_ID].notna() & 
            (df[Study_ID].str.strip() != '') & 
            (df[Study_ID] != 'nan') &
            (df[Study_ID].str.lower() != 'blank')  # 添加对"blank"的检查
        ]
        print(f"{sheet_name} 总数据行数: {len(filtered_df)}")

        # 特殊处理非kelun公司的1003临床研究药物表格，提取pat_exposure数据
        if not is_kelun and sheet_name == '1003临床研究药物':
            print(f"处理{company_code}的研究药物表格，提取pat_exposure数据...")
            # 打印所有列名，帮助调试
            print(f"Excel中1003临床研究药物表格的所有列: {df.columns.tolist()}")
            
            # 动态确定列名
            drug_name_col = None
            drug_id_col = None
            study_id_col = None
            # 尝试匹配研究药物名称列
            name_candidates = ['研究药物名称（中文）', '研究药物名称(中文)', '药物名称（中文）', '药物名称']
            for col in name_candidates:
                if col in df.columns:
                    drug_name_col = col
                    print(f"找到研究药物名称列: {col}")
                    break
            # 尝试匹配研究药物编号列    
            id_candidates = ['研究药物编号', '药物编号', '研究药物代码']
            for col in id_candidates:
                if col in df.columns:
                    drug_id_col = col
                    print(f"找到研究药物编号列: {col}")
                    break
            # 方案编号列
            study_id_candidates = ['方案编号', '研究方案编号']
            for col in study_id_candidates:
                if col in df.columns:
                    study_id_col = col
                    print(f"找到方案编号列: {col}")
                    break
            # 查看是否找到了必要的列
            if drug_name_col and drug_id_col and study_id_col:
                print(f"成功找到必要的列: 方案编号={study_id_col}, 药物名称={drug_name_col}, 药物编号={drug_id_col}")
                # 提取pat_exposure数据，三列无表头
                for study_id, group in filtered_df.groupby(study_id_col):
                    pat_exposure_lines = []
                    for _, row in group.iterrows():
                        study_id_val = row[study_id_col]
                        drug_name = row[drug_name_col]
                        drug_id = row[drug_id_col]
                        # 清理数据
                        study_id_val = re.sub(r'\\.0$', '', str(study_id_val))
                        drug_name = re.sub(r'\\.0$', '', str(drug_name))
                        drug_id = re.sub(r'\\.0$', '', str(drug_id))
                        # 检查有效性
                        if study_id_val != 'nan' and drug_name != 'nan' and drug_id != 'nan' and study_id_val.strip() and drug_name.strip() and drug_id.strip():
                            pat_exposure_lines.append(f"|{study_id_val.strip()}|{drug_name.strip()}|{drug_id.strip()}|")
                    # 如果有有效数据，保存到字典，三列无表头
                    if pat_exposure_lines:
                        excel_pat_exposure_data[study_id] = '\n'.join(pat_exposure_lines)
                        print(f"成功提取方案{study_id}的pat_exposure数据: {len(pat_exposure_lines)}行")
            else:
                missing = []
                if not drug_name_col:
                    missing.append("研究药物名称列")
                if not drug_id_col:
                    missing.append("研究药物编号列")
                if not study_id_col:
                    missing.append("方案编号列")
                print(f"研究药物表格缺少必要的列: {missing}，可用列: {df.columns.tolist()}")

        # 按 Study ID 分组
        grouped = filtered_df.groupby(Study_ID)

        for study_id, group in grouped:
            # 选择特定的列
            df_selected = group[columns_of_interest]

            # 根据三列的组合值去重
            df_unique = df_selected.drop_duplicates()

            # 生成合并后的内容
            lines = []
            # 判断是否是pat_exposure字段
            is_pat_exposure = (sheet_placeholder == '{pat_exposure}')
            # pat_exposure不加表头
            for index, row in df_unique.iterrows():
                col1 = row[columns_of_interest[0]]
                if len(columns_of_interest)>2:
                    col2 = row[columns_of_interest[2]]
                col3 = row[columns_of_interest[1]]

                # 检查是否有 NaN 或空字符串
                if len(columns_of_interest)>2:
                    if pd.isna(col1) or str(col1).strip() == '' or pd.isna(col2) or str(col2).strip() == '' or pd.isna(
                        col3) or str(col3).strip() == '':
                        continue
                elif pd.isna(col1) or str(col1).strip() == '' or pd.isna(
                        col3) or str(col3).strip() == '':
                    continue

                # 去掉小数点
                col1 = re.sub(r'\.0$', '', str(col1))
                if len(columns_of_interest) > 2:
                    col2 = re.sub(r'\.0$', '', str(col2))
                col3 = re.sub(r'\.0$', '', str(col3))

                if len(columns_of_interest) > 2 and (col2=='nan'  or col3=='nan' ):
                    continue
                elif col1=='nan'  or col3=='nan':
                    continue
                
                # 清理数据中的不规范格式
                orig_col1 = col1
                orig_col3 = col3
                orig_col2 = None if len(columns_of_interest) <= 2 else col2
                
                col1 = clean_cell_value(col1, company_code)
                if len(columns_of_interest) > 2:
                    col2 = clean_cell_value(col2, company_code)
                col3 = clean_cell_value(col3, company_code)
                
                # 只有kelun租户才更新计数器和打印清理信息
                if is_kelun:
                    # 更新计数器
                    cleaned_data_counter['total_cells'] += 2 if len(columns_of_interest) <= 2 else 3
                    cleaned_data_counter['sheets'][sheet_name]['total'] += 2 if len(columns_of_interest) <= 2 else 3
                    
                    # 记录清理前后的变化
                    if orig_col1 != col1:
                        print(f"数据清理: {sheet_name} - 第一列值从 '{orig_col1}' 变更为 '{col1}'")
                        cleaned_data_counter['cleaned_cells'] += 1
                        cleaned_data_counter['sheets'][sheet_name]['cleaned'] += 1
                    if len(columns_of_interest) > 2 and orig_col2 != col2:
                        print(f"数据清理: {sheet_name} - 第二列值从 '{orig_col2}' 变更为 '{col2}'")
                        cleaned_data_counter['cleaned_cells'] += 1
                        cleaned_data_counter['sheets'][sheet_name]['cleaned'] += 1
                    if orig_col3 != col3:
                        print(f"数据清理: {sheet_name} - 第三列值从 '{orig_col3}' 变更为 '{col3}'")
                        cleaned_data_counter['cleaned_cells'] += 1
                        cleaned_data_counter['sheets'][sheet_name]['cleaned'] += 1

                # 拼接内容
                if is_pat_exposure:
                    # pat_exposure三列无表头
                    line = f"|{col1}|{col3}|{col2}|\n" if len(columns_of_interest) > 2 else f"|{col1}|{col3}|\n"
                elif len(columns_of_interest) > 2:
                    # 三列字段，|编码|编码(文本)|
                    line = f"|{col1}|{col2}({col3})|\n"
                else:
                    # 两列字段，|标准术语英文名|标准术语中文名|
                    line = f"|{col3}|{col1}|\n"
                lines.append(line)

            # 合并所有行并去掉末尾的换行符
            merged_lines = ''.join(lines).rstrip('\n')

            # 检查是否有 临床试验id 这个键
            if study_id not in fileDataDict:
                # 如果没有，创建一个空字典并放入特定的值
                fileDataDict[study_id] = {
                    sheet_placeholder: merged_lines,
                }
            else:
                # 如果有，取出字典并检查是否有 sheet_placeholder 这个键
                if sheet_placeholder not in fileDataDict[study_id]:
                    # 如果没有，添加进去
                    fileDataDict[study_id][sheet_placeholder] = merged_lines

            # 处理pat_exposure
            pat_exposure_placeholder = '{pat_exposure}'
            
            # 根据公司类型不同处理pat_exposure
            if not is_kelun:
                # 如果是非kelun公司，使用从Excel直接提取的pat_exposure数据（三列无表头）
                if study_id in excel_pat_exposure_data:
                    fileDataDict[study_id][pat_exposure_placeholder] = excel_pat_exposure_data[study_id]
                    print(f"为方案{study_id}添加从Excel提取的pat_exposure数据（三列无表头）")
            # 只有kelun公司且pat_exposure在SKIP_PLACEHOLDERS中时才跳过
            elif company_code == 'kelun' and pat_exposure_placeholder in SKIP_PLACEHOLDERS:
                print(f"kelun公司跳过{pat_exposure_placeholder}处理")
            else:
                # 如果是kelun公司且未配置跳过，使用缓存数据
                pat_exposure = assemble_pat_exposure(cache, study_id)
                fileDataDict[study_id][pat_exposure_placeholder] = pat_exposure

    # 只有kelun租户才打印数据清理统计信息
    if is_kelun and cleaned_data_counter['total_cells'] > 0:
        clean_ratio = cleaned_data_counter['cleaned_cells'] / cleaned_data_counter['total_cells'] * 100
        print("\n数据清理统计信息:")
        print(f"总处理单元格: {cleaned_data_counter['total_cells']}")
        print(f"实际清理单元格: {cleaned_data_counter['cleaned_cells']} ({clean_ratio:.2f}%)")
        
        # 打印每个sheet的统计
        print("\n按Sheet统计清理数据:")
        for sheet, stats in cleaned_data_counter['sheets'].items():
            if stats['total'] > 0:
                sheet_ratio = stats['cleaned'] / stats['total'] * 100
                print(f"  {sheet}: {stats['cleaned']}/{stats['total']} ({sheet_ratio:.2f}%)")
            else:
                print(f"  {sheet}: 0/0 (0.00%)")
    
    print(f"\n{company_code}组装数据结束===============================================================")
    cal_dic_num(fileDataDict)
    return fileDataDict


def create_md_file_by_template(company_code, fileDataDict, placeholder_of_file_dict):
    print(f"{company_code}开始写入数据===============================================================")
    # 遍历内层字典
    for study_id, placeholder_key_value_dict in fileDataDict.items():
        print(f"study_id : {study_id}")
        # 创建项目编号目录
        project_code = str(study_id)
        # 遍历 fileDataDict
        for file_name, file_info_dict in placeholder_of_file_dict.items():
            tmpl_file_path = file_info_dict['tmplFilePath']
            file_path_dir = file_info_dict['filePath'][0]
            file_path_name = file_info_dict['filePath'][1]
            placeholder_name_list = file_info_dict['placeholder']
            directory = os.path.join(os.getcwd(),file_path_dir.format(project_code=project_code, company_code=company_code))
            if not os.path.exists(directory):
                os.makedirs(directory)

            # 从缓存中获取模板内容并替换占位符

            template_content = read_templ(company_code=company_code, tmpl_file_path_list=tmpl_file_path)
            file_content =  template_content
            for placeholder in placeholder_name_list:
                if placeholder in placeholder_key_value_dict:
                    print(f"写入: {file_name},占位符{placeholder}")
                    file_content = file_content.replace(placeholder, placeholder_key_value_dict[placeholder])

            # 获取目标文件路径
            file_path = os.path.join(directory, file_path_name)
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(file_content)

            print(f"数据已成功写入{file_path}文件")


def read_templ(company_code, tmpl_file_path_list):
    # 获取当前脚本文件所在的目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # 替换 template_path_list 中的占位符
    formatted_path_parts = [part.format(company_code=company_code) if '{company_code}' in part else part for part in
                            tmpl_file_path_list]
    # 获取模板文件的绝对路径（基于脚本文件所在目录）
    template_path = os.path.abspath(os.path.join(script_dir, *formatted_path_parts))

    # 检查模板文件是否存在
    if not os.path.exists(template_path):
        print(f"模板文件不存在: {template_path}")
        return

    with open(template_path, 'r', encoding='utf-8') as template_file:
        content = template_file.read()
        return content


def init_cache(pat_exposure_list):
    # 缓存数据到字典
    cache = {}
    for entry in pat_exposure_list:
        protocal_number = entry["protocal_number"]
        product_info = {"product_name": entry["product_name"], "drug_id": entry["drug_id"]}
        if protocal_number in cache:
            cache[protocal_number].append(product_info)
        else:
            cache[protocal_number] = [product_info]
    return cache

def assemble_pat_exposure(dic_cache, protocal_number):
    # 获取缓存中的数据
    if protocal_number not in dic_cache:
        logging.warning(f"未找到试验编号 {protocal_number} 的药物缓存数据")
        return ""

    filtered_data = dic_cache[protocal_number]

    # 格式化为所需的字符串
    pat_exposure = "\n".join([f"|{entry['product_name']}|{entry['drug_id']}|" for entry in filtered_data])

    return pat_exposure

#计算待替换的字典中的个数
def cal_dic_num(result):
    for key, study_dic in result.items():
        if isinstance(study_dic, dict):
            # 计算每个键对应的值的个数
            for placeholder, value in study_dic.items():
                count = len(value.split('\n'))
                print(f"键 '{placeholder}' 对应的值的个数是: {count}")
        else:
            print(f"键 '{key}' 的值不是字典。")


#数据清洗，比如科伦的一些单位 不对应
def data_clean(fileDataDict, filed_name, standard_data_str):
    print(f"开始清洗{filed_name}==========")
    standard_data = pd.read_csv(StringIO(standard_data_str), sep='|', skipinitialspace=True)
    # 去除空白列
    standard_data = standard_data.loc[:, ~standard_data.columns.str.contains('^Unnamed')]

    # 去除列名和数据中的空白字符
    standard_data.columns = standard_data.columns.str.strip()
    # 打印列名以检查
    print("Standard Data Columns:", standard_data.columns)

    for study_id, placeholder_idc in fileDataDict.items():

        # 将字符串按行拆分
        if filed_name not in placeholder_idc:
            continue
        raw_data_lines = placeholder_idc[filed_name].strip().split('\n')

        raw_data_list = []
        for line in raw_data_lines:
            # 去除前后空格和竖线，然后分割
            parts = [part.strip() for part in line.strip().strip('|').split('|')]
            if len(parts) >= 2:  # 确保至少有两列数据
                raw_data_list.append(parts)

        raw_data = pd.DataFrame(raw_data_list, columns=['标准术语英文名', '标准术语中文名'])

        # 创建日志记录器
        logging.basicConfig(filename='data_cleaning.txt', level=logging.INFO,
                            format='%(asctime)s - %(levelname)s - %(message)s')

        # 调试信息：检查列名
        print("standard_data columns:", standard_data.columns)
        print("raw_data columns:", raw_data.columns)

        # 确保列名正确
        assert '标准术语中文名' in standard_data.columns, "standard_data 中缺少列 '标准术语中文名'"
        assert '标准术语英文名' in standard_data.columns, "standard_data 中缺少列 '标准术语英文名'"
        assert '标准术语中文名' in raw_data.columns, "raw_data 中缺少列 '标准术语中文名'"
        assert '标准术语英文名' in raw_data.columns, "raw_data 中缺少列 '标准术语英文名'"

        # 将标准数据转换成两个字典
        # 删除重复项并保留最后一个
        standard_data_cn = standard_data.drop_duplicates(subset=['标准术语中文名'], keep='last')
        standard_data_en = standard_data.drop_duplicates(subset=['标准术语英文名'], keep='last')


        # 构造字典，确保包含中文名
        standard_data_cn_dict = {}
        for _, row in standard_data_cn.iterrows():
            standard_data_cn_dict[row['标准术语中文名'].strip()] = {
                '标准术语中文名': row['标准术语中文名'].strip(),
                '标准术语英文名': row['标准术语英文名'].strip()
            }
        # 构造字典，确保包含中文名
        standard_data_en_dict = {}
        for _, row in standard_data_en.iterrows():
            standard_data_en_dict[row['标准术语英文名'].strip()] = {
                '标准术语中文名': row['标准术语中文名'].strip(),
                '标准术语英文名': row['标准术语英文名'].strip()
            }

        # 原始数据的中文去重和英文去重
        unique_cn = raw_data['标准术语中文名'].drop_duplicates().tolist()
        unique_en = raw_data['标准术语英文名'].drop_duplicates().tolist()

        # 初始化返回列表
        cleaned_data = []

        # 根据中文去重列表获取标准数据
        for cn in unique_cn:
            if cn in standard_data_cn_dict:
                cleaned_data.append(standard_data_cn_dict[cn])
            else:
                logging.info(f"{filed_name} 中文名 '{cn}' 无法在标准数据中找到对应记录")
                print(f"{filed_name}中文名 '{cn}' 无法在标准数据中找到对应记录")

        # 根据英文去重列表获取标准数据
        for en in unique_en:
            if en in standard_data_en_dict:
                cleaned_data.append(standard_data_en_dict[en])
            else:
                logging.info(f"{filed_name} 英文名 '{en}' 无法在标准数据中找到对应记录")
                print(f"{filed_name} 英文名 '{en}' 无法在标准数据中找到对应记录")

        # 转换为 DataFrame 并去重
        cleaned_data_df = pd.DataFrame(cleaned_data).drop_duplicates()
        print(f"项目{study_id}，字段{filed_name}，标准库数据量{standard_data.size}，清洗前数据量{raw_data.size},清洗后数据量{cleaned_data_df.size}")

        # 将去重后的数据保存为字符串并按竖线分隔格式输出
        output = StringIO()
        for index, row in cleaned_data_df.iterrows():
            output.write(f"|{row['标准术语英文名']}|{row['标准术语中文名']}|\n")
        # 获取字符串内容
        cleaned_data_string = output.getvalue().rstrip('\n')

        fileDataDict[study_id][filed_name] = cleaned_data_string
    print(f"清洗结束{filed_name}==========")



