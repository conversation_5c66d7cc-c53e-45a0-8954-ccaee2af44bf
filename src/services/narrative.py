import asyncio
import json
import re
from collections import OrderedDict
from copy import deepcopy

from langfuse.decorators import observe, langfuse_context

from src.api.schema import NarrativeStreamData
from src.common.logger import logger
from src.common.context import TraceContext
from src.conf.config import Config
from src.conf.prompt import get_prompts, read_prompt
from src.database.crud import get_study_data
from src.services.chat.model_factory import ModelFactory, BusinessType
from src.services.chat.reason_llm_client import do_reason
from src.utils.json_helper import JSONHelper
from src.utils.util import get_language, split_text, extract_from_model


@observe
async def request_aws_claude(trace_id, system_prompt, user_prompt, _key):
    # 在函数体内更新当前观察的名称和元数据
    langfuse_context.update_current_observation(
        name = f"event_stream ({_key})",
        metadata = {"full_input": {"trace_id": trace_id, "system_prompt": system_prompt, "user_prompt": user_prompt}}
    )
    
    # 使用模型工厂获取叙述业务模型客户端
    tenant_id = TraceContext.get_tenant_id()
    narrative_client = ModelFactory.get_client(BusinessType.NARRATIVE, tenant_id)
    
    # 日志记录客户端类型，便于调试
    logger.info(f"{trace_id} | 使用模型客户端类型: {narrative_client.__class__.__name__}")
    
    # 使用客户端的build_narrative_payload方法构建payload
    # 这样每个客户端可以根据自己的特性构建不同的payload
    payload = narrative_client.build_narrative_payload(system_prompt, user_prompt)
    
    # 调用模型
    response = await asyncio.to_thread(narrative_client.recognize_chat, trace_id, payload, None, _key)
    return extract_from_model(response)

@observe
def sync_do_reason(trace_id, user_prompt, save_path, _key="理解处理"):
    """同步版本的do_reason，内部使用新的事件循环运行异步版本"""
    # 从上下文获取租户ID
    tenant_id = TraceContext.get_tenant_id()
    
    # 在函数体内更新当前观察的名称
    langfuse_context.update_current_observation(
        name = f"sync_do_reason ({_key})"
    )
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(do_reason(trace_id, user_prompt, save_path, _key))
    finally:
        loop.close()


def extract_abbreviations(data):
    abbreviations = []
    if isinstance(data, dict):
        for key, value in data.items():
            if key == "英文缩写":
                abbreviations.extend(value)
            else:
                abbreviations.extend(extract_abbreviations(value))
    elif isinstance(data, list):
        for item in data:
            abbreviations.extend(extract_abbreviations(item))
    return abbreviations


def unique_abbreviation(data):
    abbreviations = extract_abbreviations(data)
    abbreviations_dict = {}
    for item in abbreviations:
        abbreviations_dict.update(item)
    return abbreviations_dict


def remove_abbreviations(obj):
    if isinstance(obj, dict):
        for key, value in list(obj.items()):
            if key == "英文缩写":
                obj.pop(key)
            else:
                remove_abbreviations(value)
    elif isinstance(obj, list):
        for item in obj:
            remove_abbreviations(item)


def split_dict(data):
    keys = list(data.keys())

    # 查找 '合并用药' 的索引
    if '合并用药' in keys:
        index_hebingyongyao = keys.index('合并用药')
    else:
        index_hebingyongyao = 0  # 如果不存在，则从头开始

    # 查找 '治疗用药' 的索引
    if '治疗用药' in keys:
        index_zhiliao = keys.index('治疗用药')
    else:
        index_zhiliao = len(keys)  # 如果不存在，则到最后

    # 分割字典
    part1_keys = keys[:index_hebingyongyao]
    part2_keys = keys[index_hebingyongyao:index_zhiliao+1]
    part3_keys = keys[index_zhiliao+1:]

    part1 = {k: data[k] for k in part1_keys}
    part2 = {k: data[k] for k in part2_keys}
    part3 = {k: data[k] for k in part3_keys}

    # 生成结果字符串
    part1_result = '\n\n'.join(str(value) for value in part1.values())
    part2_result = '\n\n'.join(str(value) for value in part2.values())
    part3_result = '\n\n'.join(str(value) for value in part3.values())

    return part1_result, part2_result, part3_result


def replace_first_occurrence(text, abbr_dict, is_lower=False):
    replace_dict = {}
    if is_lower:
        abbr_dict = {key.lower(): value for key, value in abbr_dict.items()}
    for abbr, full in abbr_dict.items():
        if abbr.lower() not in replace_dict:
            pattern = re.compile(r'\b' + abbr + r'\b', re.IGNORECASE)
            match = pattern.search(text)
            if match:
                text = pattern.sub(f'{full}（{abbr}）', text, 1)
                replace_dict[abbr.lower()] = True
    return text, replace_dict


def process_text(text):
    symbols_map = {
        '.': '。'
    }
    # 构建正则表达式，将所有可能的替换字符包含进去
    regex_pattern = '|'.join(re.escape(key) for key in symbols_map.keys())
    paragraphs = text.split('\n')
    processed_paragraphs = []
    for para in paragraphs:
        # 替换英文标点符号为中文标点符号，但数字中的标点除外
        para = re.sub(f'(?<!\\d)({regex_pattern})(?!\\d)', lambda x: symbols_map[x.group(0)], para)
        # 移除段落内多余的空格，但保留段落间的空格
        para = re.sub(r'\s+', '', para).strip()
        # 删除连续相同的符号，保留一个
        para = re.sub(r'([。,、，！；])\1+', r'\1', para)
        processed_paragraphs.append(para)
    processed_text = (processed_paragraphs[0] + ('\n'.join(processed_paragraphs[1:]).replace('患者', '受试者'))) \
        .replace("!", "！") \
        .replace("?", "？") \
        .replace(";", "；") \
        .replace(":", "：") \
        .replace(",", "，") \
        .replace("(", "（") \
        .replace(")", "）") \
        .replace("：，", "：") \
        .replace("，）", "）")
    # 使用正则表达式替换特定部分的<、>、<=、>=
    processed_text = re.sub(r'正常值范围：[<>]=?(\d+)',
                            lambda m: '正常值范围：' + (
                                '小于等于' if '<=' in m.group(0) else
                                '大于等于' if '>=' in m.group(0) else
                                '小于' if '<' in m.group(0)
                                else '大于') + m.group(1), processed_text
                            )
    return processed_text

def process_symbol(input_string):
    cnt = 0
    result = ''
    for ch in input_string:
        if ch == '"':
            if cnt % 2 == 0:
                result += '"'  # 替换为中文开引号
            else:
                result += '"'  # 替换为中文闭引号
            cnt += 1
        else:
            result += ch

    return result


class Narrative:

    def __init__(self, md5, tenant_id, trace_id, study_num):
        self.tenant_id = tenant_id
        self.trace_id = trace_id
        self.md5 = md5
        self.language = get_language(self.md5)
        self.config = Config(self.md5)
        self.recognize_content_path = self.config.recognize.get('content_path')
        self.extract_result_path = self.config.extraction.get('extract_result_path')
        self.narrative_path = self.config.narrative.get('narrative_path')
        self.datetime_path = self.config.narrative.get('datetime_path')
        self.study_num = study_num

    @staticmethod
    def format_check_results(check_data, date_pattern):
        def sorted_key(data):
            # 提取年份、月份和日期部分，允许日期部分包含问号
            match = re.match(date_pattern, data['检查日期'])
            if not match:
                return None  # 如果没有匹配，返回None
            year, month, day = match.groups()
            # 如果日期部分包含问号，将其排在当月最前面
            if day == '??':
                return int(year), int(month), float('-inf')  # 使用负无穷大表示最前面
            else:
                return int(year), int(month), int(day)  # 按照实际日期排序

        sorted_data = sorted(check_data, key=sorted_key)
        merged_text = []
        for item in sorted_data:
            for line in item['正文']:
                merged_text.append(line)
        result_dict = {}
        for line in merged_text:
            dt, result = line[0:11].strip(), line[12:].rstrip('；').strip()
            if dt not in result_dict:
                result_dict[dt] = []
            result_dict[dt].append(result)
        formatted_data = {}
        for dt, results in result_dict.items():
            formatted_data[dt] = f"{dt}，{'；'.join(results)}"
        return formatted_data

    async def generate_narrative_data(self):
        prompts_dict = get_prompts(self.language, self.tenant_id)
        with open(self.extract_result_path, 'r', encoding='utf-8') as file:
            extract_result = json.load(file)
        abbr_dict = unique_abbreviation(extract_result)
        for key in ['AE', 'SAE', 'ECI', 'AESI', 'CTCAE']:
            abbr_dict.pop(key, None)
        logger.info("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=abbr_dict)
        remove_abbreviations(extract_result)
        narrative_meta_dict_str = read_prompt(prompts_dict['narrative_meta_dict'], self.language)
        if not narrative_meta_dict_str:
            logger.error(
                "{md5} | {id} | 租户{tenant_id} narrative模块提示词数据未初始化",
                md5=self.md5,
                id=self.trace_id,
                tenant_id=self.tenant_id
            )
            raise ValueError(f"租户{self.tenant_id} narrative模块提示词数据未初始化")
        narrative_meta_dict = json.loads(narrative_meta_dict_str)

        narrative_meta_dirs = read_prompt(prompts_dict['narrative_meta_dirs'], self.language)
        if not narrative_meta_dirs:
            logger.error(
                "{md5} | {id} | 租户{tenant_id} narrative模块提示词数据未初始化",
                md5=self.md5,
                id=self.trace_id,
                tenant_id=self.tenant_id
            )
            raise ValueError(f"租户{self.tenant_id} narrative模块提示词数据未初始化")

        narrative_results = {}
        async def _process(_key, _val):
            module = _val.get("module")
            # 先读取模板内容
            sys_prompt = read_prompt(str(narrative_meta_dirs.joinpath(str(_val["prompt"]))), self.language)

            # 检查模板中是否包含 ${study_id_and_study_num}
            if "${study_id_and_study_num}" in sys_prompt:
                # 从数据库获取数据
                study_data = await get_study_data(self.study_num, self.tenant_id)
                sys_prompt = sys_prompt.replace("${study_id_and_study_num}", self.study_num + " " + study_data)

            final_result = ''
            if isinstance(module, str):
                if module == "all":
                    if _key == '基础疾病':
                        sys_user_prompt = sys_prompt.replace("{PDF报告识别的结果}", str(extract_result))
                    else:
                        extract_result_copy = deepcopy(extract_result)
                        removed_key = 'SAE/ECI/AESI的描述'
                        if removed_key in extract_result_copy:
                            extract_result_copy.pop(removed_key)
                        sys_user_prompt = sys_prompt.replace("{PDF报告识别的结果}", str(extract_result_copy))
                    response = await request_aws_claude(self.trace_id, sys_user_prompt, sys_user_prompt, _key)
                    final_result = JSONHelper.narrative(response).get('正文', "")
                else:
                    final_result = f"SAE/ECI/AESI的描述：{extract_result[module]['SAE/ECI/AESI的描述']}"
            else:
                # narrative切换到推理模型的租户处理逻辑
                extract_results = [item for sublist in module for item in extract_result[sublist]]

                # 如果extract_results为空则设置为空数组
                if not extract_results:
                    extract_results = []

                sys_user_prompt = sys_prompt.replace("{PDF报告识别的结果}", str(extract_results))
                # 将 Path 转为字符串后再做替换
                new_narrative_path = str(self.narrative_path).replace(
                    "narrative.md",
                    f"narrative-{module}.md"
                )
                # 记录详细的模型调用信息
                logger.info("{md5} | {id} | 调用推理模型生成叙述: {key}", md5=self.md5, id=self.trace_id, key=_key)
                response = await asyncio.to_thread(sync_do_reason, self.trace_id, sys_user_prompt, new_narrative_path, _key)
                module_narrative_result = []
                # 通过代码转义双引号避免模型返回没有转义的" 导致json结构异常
                content = JSONHelper.narrative(response).get('正文', "")
                if isinstance(content, (str, dict)):
                    module_narrative_result.append(content)
                else:
                    module_narrative_result.extend(content)
                final_result = module_narrative_result
                if isinstance(final_result, (list, tuple)):
                    final_result = final_result[0] if final_result else ''
            logger.info("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=_key + ": " + str(final_result))
            narrative_results[_key] = str(final_result)
        tasks = [asyncio.create_task(_process(key, val)) for key, val in narrative_meta_dict.items()]
        await asyncio.gather(*tasks)
        ordered_narrative_results = OrderedDict(
            (key, narrative_results[key]) for key in narrative_meta_dict if key in narrative_results)
        if self.language == 'cn':
            base_info = ordered_narrative_results.pop('患者基本信息', '').replace('\n', '\\n')
            # 处理第一段的转义字符
            if '\\n' in base_info:
                base_info = base_info.replace('\\n', '\n')
            try:
                part1, part2, part3 = split_dict(ordered_narrative_results)
                part1_narrative_result, part1_abbr_dict = replace_first_occurrence(part1, abbr_dict)
                filtered_abbr_dict = {k: v for k, v in abbr_dict.items() if k.lower() not in part1_abbr_dict}
                part2_narrative_result, part2_abbr_dict = replace_first_occurrence(part2, filtered_abbr_dict, is_lower=True)
                filtered_abbr_dict = {k: v for k, v in abbr_dict.items() if
                                      k.lower() not in {**part1_abbr_dict, **part2_abbr_dict}}
                part3_narrative_result, _ = replace_first_occurrence(part3, filtered_abbr_dict)
                combined_narrative_results = process_symbol(base_info) + '\n\n\n' + '\n\n'.join(
                    [part1_narrative_result, part2_narrative_result, part3_narrative_result])
                final_narrative = process_text(combined_narrative_results)
            except (Exception,) as e:
                logger.warning(e)
                combined_string = '\n\n'.join(ordered_narrative_results.values())
                final_narrative = process_text(combined_string)
        else:
            if 'not provided' in ordered_narrative_results['既往病史'] and 'not provided' in ordered_narrative_results['合并疾病']:
                del ordered_narrative_results['既往病史']
                ordered_narrative_results['合并疾病'] = 'Medical history and concomitant medications were not provided.'
            final_narrative = '\n\n'.join(ordered_narrative_results.values())
        yield str(NarrativeStreamData(data={'type': 'message', 'message': final_narrative}))
        with open(self.narrative_path, 'w', encoding='utf-8') as file:
            file.write(final_narrative)
        logger.info("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=f"finish narrative")
