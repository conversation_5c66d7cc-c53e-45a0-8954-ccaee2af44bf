import json
import os
from typing import Optional, Dict, Any, List, Tuple

from sqlalchemy import text

from src.common.logger import logger
from src.database.db_mysql import db_session
from src.services.tenant_config import TenantFeatureService


class ModelConfigService:
    """模型配置服务，用于管理模型配置的获取和缓存"""
    
    # 模型配置缓存
    _model_config_cache = {}
    
    @staticmethod
    def get_model_config(model_id: str) -> Optional[Dict[str, Any]]:
        """
        获取模型配置
        
        Args:
            model_id: 模型配置ID
            
        Returns:
            模型配置字典，如果不存在则返回None
        """
        # 先从缓存获取
        if model_id in ModelConfigService._model_config_cache:
            return ModelConfigService._model_config_cache[model_id]
            
        try:
            with db_session() as session:
                query = text("""
                    SELECT * FROM model_config 
                    WHERE id = :model_id AND enabled = 1
                """)
                
                result = session.execute(query, {'model_id': model_id}).fetchone()
                
                if not result:
                    logger.warning("模型配置不存在或未启用: {id}", id=model_id)
                    return None
                    
                # 转换为字典 - 使用更安全的方式，避免字典键的问题
                config = {}
                for idx, column_name in enumerate(result._mapping.keys()):
                    config[column_name] = result[idx]
                
                # 解析config字段
                try:
                    if isinstance(config['config'], str):
                        config['params'] = json.loads(config['config'])
                    else:
                        # MySQL JSON类型可能已经自动转换为Python字典
                        config['params'] = config['config']
                except Exception as e:
                    logger.warning("解析模型配置的config字段失败: {id}, 错误: {error}", id=model_id, error=str(e))
                    config['params'] = {}
                
                # 缓存配置
                ModelConfigService._model_config_cache[model_id] = config
                
                return config
                
        except Exception as e:
            logger.error("获取模型配置失败: {id}, 错误: {error}", id=model_id, error=str(e))
            return None
    
    @staticmethod
    def get_model_config_by_type(tenant_id: str, model_type: str) -> Optional[Dict[str, Any]]:
        """
        根据租户ID和模型类型获取模型配置
        
        Args:
            tenant_id: 租户ID
            model_type: 模型类型
            
        Returns:
            模型配置字典，如果不存在则返回None
        """
        # 缓存键
        cache_key = f"{tenant_id}_{model_type}"
        
        # 先从缓存获取
        if cache_key in ModelConfigService._model_config_cache:
            return ModelConfigService._model_config_cache[cache_key]
            
        try:
            with db_session() as session:
                # 先尝试获取租户特定的模型配置
                query = text("""
                    SELECT * FROM model_config 
                    WHERE tenant_id = :tenant_id AND model_type = :model_type AND enabled = 1
                """)
                
                result = session.execute(query, {'tenant_id': tenant_id, 'model_type': model_type}).fetchone()
                
                # 如果租户没有特定配置，且不是system租户，则尝试获取system租户的配置
                if not result and tenant_id != "system":
                    query = text("""
                        SELECT * FROM model_config 
                        WHERE tenant_id = 'system' AND model_type = :model_type AND enabled = 1
                    """)
                    
                    result = session.execute(query, {'model_type': model_type}).fetchone()
                
                if not result:
                    logger.warning("未找到租户 {tenant_id} 的 {model_type} 模型配置", 
                                 tenant_id=tenant_id, model_type=model_type)
                    return None
                    
                # 转换为字典
                config = {}
                for idx, column_name in enumerate(result._mapping.keys()):
                    config[column_name] = result[idx]
                
                # 解析config字段
                try:
                    if isinstance(config['config'], str):
                        config['params'] = json.loads(config['config'])
                    else:
                        # MySQL JSON类型可能已经自动转换为Python字典
                        config['params'] = config['config']
                except Exception as e:
                    logger.warning("解析模型配置的config字段失败: {tenant_id} {model_type}, 错误: {error}",
                                 tenant_id=tenant_id, model_type=model_type, error=str(e))
                    config['params'] = {}
                
                # 缓存配置
                ModelConfigService._model_config_cache[cache_key] = config
                
                return config
                
        except Exception as e:
            logger.error("获取模型配置失败: {tenant_id} {model_type}, 错误: {error}",
                       tenant_id=tenant_id, model_type=model_type, error=str(e))
            return None
    
    @staticmethod
    def get_tenant_model_id(tenant_id: str, business_type: str) -> Optional[str]:
        """
        获取租户特定业务类型的模型ID
        
        Args:
            tenant_id: 租户ID
            business_type: 业务类型
            
        Returns:
            模型ID，如果不存在则返回None
        """
        try:
            # 将业务类型名称转换为小写
            business_type = business_type.lower()
            
            # 获取租户的模型配置
            model_config = TenantFeatureService.get_feature_config(tenant_id, "model_config")
            
            # 如果租户没有配置，使用系统租户的配置
            if not model_config and tenant_id != "system":
                logger.info("租户 {tenant_id} 未配置模型，使用系统租户配置", tenant_id=tenant_id)
                model_config = TenantFeatureService.get_feature_config("system", "model_config")
            
            # 如果系统租户也没有配置，返回None
            if not model_config:
                logger.warning("系统租户未配置模型")
                return None
                
            # 获取业务类型对应的模型ID
            if business_type in model_config:
                return model_config[business_type]
            # 如果租户配置了模型但没有配置此业务类型，且不是system租户，则使用system租户配置
            elif tenant_id != "system":
                logger.info("租户 {tenant_id} 未配置业务类型 {business_type} 的模型，尝试使用系统租户配置", 
                           tenant_id=tenant_id, business_type=business_type)
                system_config = TenantFeatureService.get_feature_config("system", "model_config")
                if system_config and business_type in system_config:
                    return system_config[business_type]
                else:
                    logger.warning("系统租户未配置业务类型 {business_type} 的模型", business_type=business_type)
            else:
                logger.warning("业务类型 {business_type} 未配置模型", business_type=business_type)
                return None
                
        except Exception as e:
            logger.error("获取租户模型ID失败: {tenant_id}, {business_type}, 错误: {error}", 
                        tenant_id=tenant_id, business_type=business_type, error=str(e))
            return None
    
    @staticmethod
    def get_tenant_model_config(tenant_id: str, business_type: str) -> Optional[Dict[str, Any]]:
        """
        获取租户特定业务类型的模型配置
        
        Args:
            tenant_id: 租户ID
            business_type: 业务类型
            
        Returns:
            模型配置字典，如果不存在则返回None
        """
        # 获取模型ID
        model_id = ModelConfigService.get_tenant_model_id(tenant_id, business_type)
        
        if not model_id:
            return None
            
        # 获取模型配置
        return ModelConfigService.get_model_config(model_id)
    
    @staticmethod
    def clear_cache():
        """清除缓存"""
        ModelConfigService._model_config_cache.clear()
        logger.info("已清除模型配置缓存")
        
    @staticmethod
    def get_config_value(config: Dict[str, Any], key: str, default: Any = None) -> Any:
        """
        从模型配置中获取值
        
        Args:
            config: 模型配置字典
            key: 键名
            default: 默认值
            
        Returns:
            配置值或默认值
        """
        if not config or 'params' not in config:
            return default
            
        params = config['params']
        return params.get(key, default) 