import ast
import asyncio
import json
import os
import re
import traceback
from collections import defaultdict, OrderedDict
from copy import deepcopy
from datetime import datetime
from difflib import SequenceMatcher  # 添加用于字符串相似度计算

from langfuse.decorators import observe, langfuse_context

from src.api.schema import TransformerStreamData
from src.common.argus_post_transform import transform_json
from src.common.context import TraceContext
from src.common.logger import logger
from src.conf.config import Config
from src.conf.prompt import get_prompts, read_prompt
from src.database import crud
from src.database.db_mysql import db_session
from src.database.models.entity import DictionaryMapping, PromptTemplate
from src.services.chat.doubao_client import httpx_client
from src.services.chat.model_factory import ModelFactory, BusinessType
from src.utils.json_helper import JSONHelper
from src.utils.util import get_language, normalize_keys, call_func, extract_content_from_response

# 添加异步信号量控制并发
doubao_semaphore = asyncio.Semaphore(int(os.getenv("AZURE_GPT_CONCURRENT", 50)))

@observe(as_type="generation")
async def request_transformer(trace_id, system_prompt, user_prompt, second_system_prompt=None, _key="转换处理"):
    # 更新langfuse上下文，添加动态标识
    langfuse_context.update_current_observation(
        name = f"transformer_doubao ({_key})",
        metadata = {"trace_id": trace_id, "has_second_prompt": second_system_prompt is not None}
    )

    # 使用模型工厂获取结构化处理模型客户端
    tenant_id = TraceContext.get_tenant_id()
    transformer_client = ModelFactory.get_client(BusinessType.TRANSFORMER, tenant_id)
    
    # 日志记录客户端类型，便于调试
    logger.info(f"{trace_id} | 使用模型客户端类型: {transformer_client.__class__.__name__}")

    # 使用客户端的build_transformer_payload方法构建payload
    # 这样每个客户端可以根据自己的特性构建不同的payload
    payload = transformer_client.build_transformer_payload(
        system_prompt, user_prompt, second_system_prompt
    )

    # 在异步层面使用信号量控制并发
    async with doubao_semaphore:
        logger.info(f"{trace_id} | 获取doubao请求信号量，开始处理请求")
        # 使用do_reason方法，所有模型客户端都必须实现此方法
        response, reason_content = await call_func(transformer_client.do_reason, payload, trace_id, _key)
        logger.info(f"{trace_id} | 释放doubao请求信号量")

    return extract_content_from_response(response)


def is_sub_directory(parent_directory, sub_directory_name):
    subdirectory_path = os.path.join(parent_directory, sub_directory_name)
    if os.path.isdir(subdirectory_path):
        return True
    else:
        return False


def is_consistent(data_list, data_dict):
    dict1 = {key: sum(1 for value in value_list if value) for item in data_list for key, value_list in item.items()}
    dict2 = {key: len({k for k, v in subdict.items() if v and v != ['']}) for key, subdict in data_dict.items()}
    return dict1 == dict2


def merge_and_deduplicate(data):
    merged_data = defaultdict(set)
    for item in data:
        key, value = next(iter(item.items()))
        merged_data[key].add(value)
    transformed_data = [{key: list(values)} for key, values in merged_data.items()]
    return transformed_data


def update_encoding_values(chunk_item, encoded_result):

    def update_item_value(item, key, val):
        if key in item and item[key] in val:
            corresponding_values = val[item[key]]
            item[key] = corresponding_values[0] if len(corresponding_values) == 1 else corresponding_values

    for encoded_key, encoded_val in encoded_result.items():
        if isinstance(chunk_item, dict):
            update_item_value(chunk_item, encoded_key, encoded_val)
        else:
            for chunk in chunk_item:
                update_item_value(chunk, encoded_key, encoded_val)
                for chunk_key in chunk.keys():
                    if "Rel_Hist_Table_" in chunk_key and "pat_hist_rptd" in chunk_key:
                        update_item_value(chunk, chunk_key, encoded_val)
                    elif "Ind_Table_" in chunk_key and "_ind_reptd" in chunk_key:
                        update_item_value(chunk, chunk_key, encoded_val)
    return chunk_item


class Transformer:

    def __init__(self, md5, tenant_id, trace_id):
        self.tenant_id = tenant_id
        self.trace_id = trace_id
        self.md5 = md5
        self.language = get_language(self.md5)
        self.prompts_dict = get_prompts(self.language, self.tenant_id)
        self.config = Config(self.md5)
        self.extract_result_path = self.config.extraction.get('extract_result_path')
        self.structured_path = self.config.structured.get('structured_path')
        self.raw_transformer_path = self.config.transformer.get('raw_transformer_path')
        self.transformer_path = self.config.transformer.get('transformer_path')
        self.protocal_number = None

    def get_module_rule(self, module, file_name=None):
        base_path = read_prompt(self.prompts_dict['transformer_convert_rule'], self.language)
        if file_name:
            module_rule_path = base_path.joinpath(module).joinpath(file_name)
        else:
            module_rule_path = base_path.joinpath(f"{module}.md")
        try:
            return read_prompt(str(module_rule_path), self.language)
        except (Exception,) as e:
            logger.warning("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=e)
            return

    def get_module_dict_from_db(self, project_code: str, module: str) -> str:
        """
        从数据库获取字典映射数据并替换占位符，直接使用租户的全局字典
        Args:
            project_code: 项目编号 (不再使用，保留参数兼容性)
            module: 模块名
        Returns:
            str: 处理后的字典映射内容
        """
        try:
            with db_session() as session:
                logger.info(
                    "{md5} | {id} | 开始获取全局字典映射数据 | module={module} | tenant_id={tenant_id}",
                    md5=self.md5,
                    id=self.trace_id,
                    module=module,
                    tenant_id=self.tenant_id
                )
                
                # 1. 从 prompt_templates 表获取模板
                template_key = f"transformer_convert_dict_{module}.md"
                logger.debug(
                    "{md5} | {id} | 查询模板 | template_key={key} | language={lang} | tenant_id={tenant_id}",
                    md5=self.md5,
                    id=self.trace_id,
                    key=template_key,
                    lang=self.language,
                    tenant_id=self.tenant_id
                )
                
                template = session.query(PromptTemplate).filter(
                    PromptTemplate.company_code.in_([self.tenant_id, 'system']),
                    PromptTemplate.template_key == template_key,
                    PromptTemplate.language == self.language
                ).first()
                
                if not template:
                    logger.warning(
                        "{md5} | {id} | 未找到模板 | template_key={key} | language={lang} | tenant_id={tenant_id}",
                        md5=self.md5,
                        id=self.trace_id,
                        key=template_key,
                        lang=self.language,
                        tenant_id=self.tenant_id
                    )
                    return None
                    
                # 2. 直接获取全局字典映射数据
                logger.debug(
                    "{md5} | {id} | 直接查询全局字典映射 | module={module} | tenant_id={tenant_id}",
                    md5=self.md5,
                    id=self.trace_id,
                    module=module,
                    tenant_id=self.tenant_id
                )

                mappings = session.query(DictionaryMapping).filter(
                    DictionaryMapping.project_code == "GLOBAL",
                    DictionaryMapping.module == module,
                    DictionaryMapping.company_code == self.tenant_id,
                    DictionaryMapping.content != ''
                ).all()

                # 对每个mapping的content按行去重
                for mapping in mappings:
                    if mapping.content:
                        # 按行分割并去重
                        lines = mapping.content.splitlines()
                        unique_lines = list(dict.fromkeys(lines))  # 保持原有顺序去重
                        # 重新组合成字符串
                        mapping.content = '\n'.join(unique_lines)

                logger.debug(
                    "{md5} | {id} | 字典映射数据行去重完成 | module={module} | 映射数量={count} | 总行数={total_lines} | 去重后行数={unique_lines}",
                    md5=self.md5,
                    id=self.trace_id,
                    module=module,
                    count=len(mappings),
                    total_lines=sum(len(m.content.splitlines()) for m in mappings),
                    unique_lines=sum(len(set(m.content.splitlines())) for m in mappings)
                )
                
                if not mappings:
                    logger.warning(
                        "{md5} | {id} | 未找到全局字典映射数据 | module={module} | tenant_id={tenant_id} | language={lang}",
                        md5=self.md5,
                        id=self.trace_id,
                        module=module,
                        tenant_id=self.tenant_id,
                        lang=self.language
                    )
                    return None
                    
                # 3. 替换模板中的占位符
                template_content = template.content
                for mapping in mappings:
                    placeholder = mapping.placeholder
                    if placeholder in template_content:
                        logger.debug(
                            "{md5} | {id} | 替换占位符 | placeholder={placeholder}",
                            md5=self.md5,
                            id=self.trace_id,
                            placeholder=mapping.placeholder
                        )
                        template_content = template_content.replace(placeholder, mapping.content)
                
                logger.info(
                    "{md5} | {id} | 成功获取全局字典映射数据 | module={module} | mappings_count={count}",
                    md5=self.md5,
                    id=self.trace_id,
                    module=module,
                    count=len(mappings)
                )
                
                # 记录查询到的详细映射数据，方便调试
                for i, mapping in enumerate(mappings):
                    content_preview = mapping.content[:50] + "..." if len(mapping.content) > 50 else mapping.content
                    logger.debug(
                        "{md5} | {id} | 全局字典映射项[{index}/{total}] | placeholder={placeholder} | content_preview={preview}",
                        md5=self.md5,
                        id=self.trace_id,
                        index=i+1,
                        total=len(mappings),
                        placeholder=mapping.placeholder,
                        preview=content_preview
                    )
                
                # 记录替换后的模板内容长度和预览
                template_preview = template_content[:100] + "..." if len(template_content) > 100 else template_content
                logger.debug(
                    "{md5} | {id} | 替换后的字典模板 | 长度={length} | 内容预览={preview}",
                    md5=self.md5,
                    id=self.trace_id,
                    length=len(template_content),
                    preview=template_preview
                )
                
                return template_content
                    
        except Exception as e:
            logger.error(
                "{md5} | {id} | 获取全局字典映射失败 | module={module} | tenant_id={tenant_id} | language={lang} | error={error}\n{traceback}",
                md5=self.md5,
                id=self.trace_id,
                module=module,
                tenant_id=self.tenant_id,
                lang=self.language,
                error=str(e),
                traceback=traceback.format_exc()
            )
            return None

    async def generate_transformer_data(self):
        system_prompt = read_prompt(self.prompts_dict['transformer_system_prompt'], self.language)
        user_prompt = read_prompt(self.prompts_dict['transformer_user_prompt'], self.language)
        structured_convert_rules = read_prompt(self.prompts_dict['structured_convert_rules'], self.language)

        with open(self.structured_path, 'r', encoding='utf-8') as file:
            structured_result = file.read()
        structured_data = JSONHelper.transformer(structured_result)
        self.protocal_number = structured_data['reportBaseInfo']['protocalNumber']

        raw_transformer_data = {}
        final_transformer_data = {}

        async def _process(module, rule):
            module_structured_data = structured_data[module]
            if module == 'reportSubjectInfo':
                # 获取字典映射数据
                report_subject_info_1_module_dict = self.get_module_dict_from_db(
                    project_code=self.protocal_number,
                    module='reportSubjectInfo_1'
                )
                report_subject_info_2_module_dict = self.get_module_dict_from_db(
                    project_code=self.protocal_number,
                    module='reportSubjectInfo_2'
                )
                report_subject_info_3_module_dict = self.get_module_dict_from_db(
                    project_code=self.protocal_number,
                    module='reportSubjectInfo_3'
                )
                
                # 获取规则
                report_subject_info_1_module_rule = self.get_module_rule(module, "reportSubjectInfo_1.md")
                report_subject_info_2_module_rule = self.get_module_rule(module, "reportSubjectInfo_2.md")
                report_subject_info_3_module_rule = self.get_module_rule(module, "reportSubjectInfo_3.md")

                counter = 0
                max_iterations = 3
                while counter < max_iterations:
                    try:
                        report_subject_structured_data = deepcopy(module_structured_data)
                        report_subject_rule = deepcopy(rule)
                        sub_report_subject_convert_rules = {}
                        sub_report_subject_structured_data = {}
                        keys_to_extract = ['rel_hist_add', 'refExamineTable']
                        for key in keys_to_extract:
                            sub_report_subject_convert_rules[key] = report_subject_rule.pop(key, None)
                            sub_report_subject_structured_data[key] = report_subject_structured_data.pop(key, None)
                        sub_report_subject_convert_rules['report_subject_info'] = report_subject_rule.copy()
                        sub_report_subject_structured_data[
                            'report_subject_info'] = report_subject_structured_data.copy()
                        report_subject_info_result = {}

                        async def _sub_process(rule_key, rule_value):
                            if rule_key in ['rel_hist_add', 'refExamineTable']:
                                chunk = 10
                                sub_chunked_report_subject_structured_list = [
                                    sub_report_subject_structured_data[rule_key][i:i + chunk]
                                    for i in range(0, len(sub_report_subject_structured_data[rule_key]), chunk)
                                ]
                                chunked_report_subject_info_list = []
                                if rule_key == 'rel_hist_add':
                                    report_subject_info_module_dict = report_subject_info_2_module_dict
                                    report_subject_info_module_rule = report_subject_info_2_module_rule
                                else:
                                    report_subject_info_module_dict = report_subject_info_3_module_dict
                                    report_subject_info_module_rule = report_subject_info_3_module_rule

                                for chunk_item in sub_chunked_report_subject_structured_list:
                                    extract_keys = ['labtest', 'TXT_labunit_0']
                                    if any(k in chunk_item[0] for k in extract_keys):
                                        items = [
                                            {extract_key: item[extract_key]} for item in chunk_item
                                            for extract_key in extract_keys if item.get(extract_key)
                                        ]
                                        items = merge_and_deduplicate(items)
                                        encoded_result = crud.query_encoded_data(items, self.tenant_id, self.protocal_number)
                                    else:
                                        items = [
                                            {'rel_hist_table_pat_hist_rptd': entry[match]} for entry in chunk_item
                                            for match in entry.keys() if re.match(r'Rel_Hist_Table_\d+_pat_hist_rptd', match)
                                        ]
                                        items = merge_and_deduplicate(items)
                                        encoded_result = crud.query_encoded_data(items, self.tenant_id, self.protocal_number)
                                    if is_consistent(items, encoded_result):
                                        update_encoding = update_encoding_values(deepcopy(chunk_item), encoded_result)
                                        chunked_report_subject_info_list.extend(update_encoding)
                                    else:
                                        # 提取需要匹配的术语
                                        input_terms = []
                                        if rule_key == 'rel_hist_add':
                                            # 提取病史报告术语
                                            for entry in chunk_item:
                                                for key in entry.keys():
                                                    if re.match(r'Rel_Hist_Table_\d+_pat_hist_rptd', key):
                                                        input_terms.append(entry[key])
                                        else:
                                            for item in chunk_item:
                                                if 'labtest' in item:
                                                    input_terms.append(item['labtest'])
                                                if 'TXT_labunit_0' in item:
                                                    input_terms.append(item['TXT_labunit_0'])
                                        
                                        if rule_key == 'rel_hist_add':
                                            filtered_module_dict = await self.filter_dictionary_data(
                                                report_subject_info_2_module_dict,
                                                input_terms
                                            )
                                            logger.info(
                                                "{md5} | {id} | 应用 rel_hist_add 的字典过滤 | terms_count={count}",
                                                md5=self.md5,
                                                id=self.trace_id,
                                                count=len(input_terms)
                                            )
                                        else:
                                            # 应用 labtest 的字典过滤
                                            filtered_module_dict = await self.filter_dictionary_data(
                                                report_subject_info_3_module_dict,
                                                input_terms
                                            )
                                            logger.info(
                                                "{md5} | {id} | 应用 labtest 的字典过滤 | terms_count={count}",
                                                md5=self.md5,
                                                id=self.trace_id,
                                                count=len(input_terms)
                                            )

                                        sub_formatted_system_prompt = system_prompt \
                                            .replace("${finalStructuredData}", str(chunk_item)) \
                                            .replace("${rule}", str(rule_value)) \
                                            .replace("${moduleDict}", str(filtered_module_dict)) \
                                            .replace("${moduleRule}", str(report_subject_info_module_rule))
                                        sub_formatted_user_prompt = user_prompt \
                                            .replace("${finalStructuredData}", str(chunk_item)) \
                                            .replace("${rule}", str(rule_value)) \
                                            .replace("${moduleDict}", str(filtered_module_dict)) \
                                            .replace("${moduleRule}", str(report_subject_info_module_rule))
                                        sub_transformer_result = await request_transformer(self.trace_id,
                                                                                            sub_formatted_system_prompt,
                                                                                            sub_formatted_user_prompt,
                                                                                            None,
                                                                                            f"reportSubjectInfo_rel_hist_add_{module}")
                                        try:
                                            # 为了避免当只有一个检查或者病史的时候，模型可能只返回对象而不是数组，需要手动拼接数组
                                            sub_transformer_result_json = JSONHelper.transformer(sub_transformer_result)
                                            if not isinstance(sub_transformer_result_json, list):
                                                sub_transformer_result_json = [sub_transformer_result_json]
                                            chunked_report_subject_info_list.extend(sub_transformer_result_json)
                                        except (Exception,) as exc:
                                            logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=exc)
                                            logger.warning("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=sub_transformer_result)
                                            chunked_report_subject_info_list.extend(
                                                ast.literal_eval(sub_transformer_result))
                                report_subject_info_result[rule_key] = chunked_report_subject_info_list
                            else:
                                # 提取需要匹配的术语
                                input_terms = []
                                for key, value in sub_report_subject_structured_data[rule_key].items():
                                    if isinstance(value, str) and value:
                                        input_terms.append(value)
                                
                                filtered_module_dict = report_subject_info_1_module_dict
                                logger.info(
                                    "{md5} | {id} | 跳过 reportSubjectInfo 主要部分的字典过滤",
                                    md5=self.md5,
                                    id=self.trace_id
                                )

                                sub_formatted_system_prompt = system_prompt \
                                    .replace("${finalStructuredData}",
                                             str(sub_report_subject_structured_data[rule_key])) \
                                    .replace("${rule}", str(rule_value)) \
                                    .replace("${moduleDict}", str(filtered_module_dict)) \
                                    .replace("${moduleRule}", str(report_subject_info_1_module_rule))
                                sub_formatted_user_prompt = user_prompt \
                                    .replace("${finalStructuredData}",
                                             str(sub_report_subject_structured_data[rule_key])) \
                                    .replace("${rule}", str(rule_value)) \
                                    .replace("${moduleDict}", str(filtered_module_dict)) \
                                    .replace("${moduleRule}", str(report_subject_info_1_module_rule))
                                sub_transformer_result = await request_transformer(self.trace_id,
                                                                                    sub_formatted_system_prompt,
                                                                                    sub_formatted_user_prompt,
                                                                                    None,
                                                                                    f"reportSubjectInfo_{rule_key}")
                                try:
                                    report_subject_info_result.update(**JSONHelper.transformer(sub_transformer_result))
                                except (Exception,) as exc:
                                    logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=exc)
                                    logger.warning("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=sub_transformer_result)
                                    report_subject_info_result.update(**ast.literal_eval(sub_transformer_result))

                        sub_tasks = [asyncio.create_task(_sub_process(rule_key, rule_value)) for
                                     rule_key, rule_value in
                                     sub_report_subject_convert_rules.items()]
                        await asyncio.gather(*sub_tasks)
                        ordered_report_subject_info_result = OrderedDict(
                            (key, report_subject_info_result[key]) for key in module_structured_data if
                            key in report_subject_info_result)
                        raw_transformer_data[module] = deepcopy(ordered_report_subject_info_result)
                        final_transformer_data[module] = self.post_process(module, module_structured_data,
                                                                           ordered_report_subject_info_result)
                        final_transformer_data[module] = await self.secondary_transformer_data(module,
                                                                                               final_transformer_data[
                                                                                                   module])
                    except (Exception,) as e:
                        logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=f"{counter}: {e}")
                        traceback.print_exc()
                    else:
                        break
                    finally:
                        counter += 1
                transformed_data = transform_json(final_transformer_data, self.tenant_id, self.language)

                final_transformer_data[module] = transformed_data[module]
                return str(
                    TransformerStreamData(
                        data={'type': 'message', 'module': module,
                              'message': transformed_data[module]})
                )

            elif module == 'drugInfo':
                # 获取字典映射数据
                drug_info_1_module_dict = self.get_module_dict_from_db(
                    project_code=self.protocal_number,
                    module='drugInfo_1'
                )
                drug_info_2_module_dict = self.get_module_dict_from_db(
                    project_code=self.protocal_number,
                    module='drugInfo_2'
                )
                drug_info_3_module_dict = self.get_module_dict_from_db(
                    project_code=self.protocal_number,
                    module='drugInfo_3'
                )
                
                # 获取规则
                drug_info_1_module_rule = self.get_module_rule(module, "drugInfo_1.md")
                drug_info_2_module_rule = self.get_module_rule(module, "drugInfo_2.md")
                drug_info_3_module_rule = self.get_module_rule(module, "drugInfo_3.md")

                counter = 0
                max_iterations = 3
                while counter < max_iterations:
                    try:
                        report_subject_structured_data = deepcopy(module_structured_data)
                        report_subject_rule = deepcopy(rule)
                        sub_drug_info_convert_rules = {}
                        sub_drug_info_structured_data = {}
                        keys_to_extract = ['试验用药', '合并用药', '治疗用药','既往用药']
                        for key in keys_to_extract:
                            if report_subject_structured_data.get(key) is not None:
                                sub_drug_info_convert_rules[key] = report_subject_rule.pop(key, None)
                                sub_drug_info_structured_data[key] = report_subject_structured_data.pop(key, None)

                        report_drug_info_result = {}

                        async def _sub_process(rule_key, rule_value):
                            _chunk = 1
                            chunked_structured_list = [
                                sub_drug_info_structured_data[rule_key][i:i + _chunk]
                                for i in range(0, len(sub_drug_info_structured_data[rule_key]), _chunk)
                            ]

                            async def _process_chunk(chunked_items):
                                async def _process_item(chunked_item):
                                    chunked_convert_rules, chunked_structured_data = {}, {}
                                    rule_value_replica = deepcopy(rule_value[0])
                                    for chunked_key in ['btnAddInd', 'expDoseTable']:
                                        chunked_convert_rules[chunked_key] = rule_value_replica.pop(chunked_key, None)
                                        chunked_structured_data[chunked_key] = chunked_item.pop(chunked_key, None)
                                    chunked_convert_rules['remain_drug_info'] = rule_value_replica.copy()
                                    chunked_structured_data['remain_drug_info'] = chunked_item.copy()

                                    chunked_item_result = {}

                                    async def _process_fields(field_key):
                                        if field_key == 'remain_drug_info':
                                            update_keys = ['product_name', 'TXT_formulation_id']
                                            chunked_data = deepcopy(chunked_structured_data[field_key])
                                            items = [{update_keys[0]: chunked_data.get(update_keys[0])}]
                                            if update_keys[1] in chunked_data:
                                                items.append({update_keys[1]: chunked_data.get(update_keys[1])})
                                            items = merge_and_deduplicate(items)
                                            encoded_result = crud.query_encoded_data(items, self.tenant_id, self.protocal_number)
                                            if is_consistent(items, encoded_result):
                                                update_encoding = update_encoding_values(chunked_data, encoded_result)
                                                chunked_item_result.update(update_encoding)
                                            else:
                                                # 提取需要匹配的术语
                                                input_terms = []
                                                for key in update_keys:
                                                    if key in chunked_data and chunked_data[key]:
                                                        input_terms.append(chunked_data[key])
                                                
                                                # 应用药品名称的字典过滤
                                                filtered_module_dict = await self.filter_dictionary_data(
                                                    drug_info_1_module_dict,
                                                    input_terms,
                                                    drug_type=rule_key
                                                )
                                                logger.info(
                                                    "{md5} | {id} | 应用 product_name (药品名称) 的字典过滤 | terms_count={count}",
                                                    md5=self.md5,
                                                    id=self.trace_id,
                                                    count=len(input_terms)
                                                )
                                                
                                                sub_formatted_system_prompt = system_prompt \
                                                    .replace("${finalStructuredData}",
                                                             str(chunked_structured_data[field_key])) \
                                                    .replace("${rule}", str(chunked_convert_rules[field_key])) \
                                                    .replace("${moduleDict}", str(filtered_module_dict)) \
                                                    .replace("${moduleRule}", str(drug_info_1_module_rule))
                                                sub_formatted_user_prompt = user_prompt \
                                                    .replace("${finalStructuredData}",
                                                             str(chunked_structured_data[field_key])) \
                                                    .replace("${rule}", str(chunked_convert_rules[field_key])) \
                                                    .replace("${moduleDict}", str(filtered_module_dict)) \
                                                    .replace("${moduleRule}", str(drug_info_1_module_rule))
                                                sub_transformer_result = await request_transformer(self.trace_id,
                                                                                                    sub_formatted_system_prompt,
                                                                                                    sub_formatted_user_prompt,
                                                                                                    None,
                                                                                                    f"drugInfo_product_name_{rule_key}")
                                                try:
                                                    sub_transformer_result = JSONHelper.transformer(sub_transformer_result)
                                                except (Exception,) as exc:
                                                    # 有的时候模型会返回单引号json，造成无法正常格式化
                                                    logger.warning("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=exc)
                                                    logger.warning("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=sub_transformer_result)
                                                    try:
                                                        sub_transformer_result = ast.literal_eval(
                                                            sub_transformer_result)
                                                    except (Exception,) as exc:
                                                        logger.warning("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=exc)
                                                        try:
                                                            sub_transformer_result = normalize_keys(
                                                                ast.literal_eval(
                                                                    sub_transformer_result.replace("\n", "")))
                                                        except (Exception,) as exc:
                                                            logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=exc)
                                                            traceback.print_exc()
                                                chunked_item_result.update(sub_transformer_result)
                                        elif field_key == 'btnAddInd':
                                            chunked_data = deepcopy(chunked_structured_data[field_key])
                                            items = [{'Ind_Table_ind_reptd': value} for d in chunked_data for value in d.values()]
                                            items = merge_and_deduplicate(items)
                                            encoded_result = crud.query_encoded_data(items, self.tenant_id, self.protocal_number)
                                            if is_consistent(items, encoded_result):
                                                update_encoding = update_encoding_values(chunked_data, encoded_result)
                                                chunked_item_result[field_key] = update_encoding
                                            else:
                                                # 提取需要匹配的术语
                                                input_terms = []
                                                for d in chunked_data:
                                                    for value in d.values():
                                                        if value:
                                                            input_terms.append(value)
                                                
                                                # 应用适应症的字典过滤
                                                filtered_module_dict = await self.filter_dictionary_data(
                                                    drug_info_2_module_dict,
                                                    input_terms,
                                                    drug_type=rule_key
                                                )
                                                logger.info(
                                                    "{md5} | {id} | 应用 btnAddInd (适应症) 的字典过滤 | terms_count={count}",
                                                    md5=self.md5,
                                                    id=self.trace_id,
                                                    count=len(input_terms)
                                                )
                                                
                                                sub_formatted_system_prompt = system_prompt \
                                                    .replace("${finalStructuredData}",
                                                             str(chunked_structured_data[field_key])) \
                                                    .replace("${rule}", str(chunked_convert_rules[field_key])) \
                                                    .replace("${moduleDict}", str(filtered_module_dict)) \
                                                    .replace("${moduleRule}", str(drug_info_2_module_rule))
                                                sub_formatted_user_prompt = user_prompt \
                                                    .replace("${finalStructuredData}",
                                                             str(chunked_structured_data[field_key])) \
                                                    .replace("${rule}", str(chunked_convert_rules[field_key])) \
                                                    .replace("${moduleDict}", str(filtered_module_dict)) \
                                                    .replace("${moduleRule}", str(drug_info_2_module_rule))

                                                sub_transformer_result = await request_transformer(self.trace_id,
                                                                                                    sub_formatted_system_prompt,
                                                                                                    sub_formatted_user_prompt,
                                                                                                    None,
                                                                                                    f"drugInfo_ind_{rule_key}")
                                                try:
                                                    sub_transformer_result = JSONHelper.transformer(sub_transformer_result)
                                                except (Exception,) as exc:
                                                    logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=str(exc))
                                                    logger.warning("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=sub_transformer_result)
                                                    try:
                                                        sub_transformer_result = ast.literal_eval(
                                                            sub_transformer_result)
                                                    except (Exception,) as exc:
                                                        logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=str(exc))
                                                        try:
                                                            sub_transformer_result = normalize_keys(
                                                                ast.literal_eval(
                                                                    sub_transformer_result.replace("\n", "")))
                                                        except (Exception,) as exc:
                                                            logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=str(exc))
                                                            traceback.print_exc()
                                                chunked_item_result[field_key] = sub_transformer_result
                                        elif field_key == 'expDoseTable':
                                            update_keys = ['TXT_cdr_dose_unit_id',
                                                           'TXT_freq_id',
                                                           'TXT_cdr_admin_route_id'
                                                           ]
                                            chunked_data = deepcopy(chunked_structured_data[field_key])
                                            items = []
                                            for d in chunked_data:
                                                items.append({update_keys[0]: d[update_keys[0]]})
                                                items.append({update_keys[1]: d[update_keys[1]]})
                                                items.append({update_keys[2]: d[update_keys[2]]})
                                            items = merge_and_deduplicate(items)
                                            encoded_result = crud.query_encoded_data(items, self.tenant_id, self.protocal_number)
                                            if is_consistent(items, encoded_result):
                                                update_encoding = update_encoding_values(chunked_data, encoded_result)
                                                chunked_item_result[field_key] = update_encoding
                                            else:
                                                sub_formatted_system_prompt = system_prompt \
                                                    .replace("${finalStructuredData}",
                                                             str(chunked_structured_data[field_key])) \
                                                    .replace("${rule}", str(chunked_convert_rules[field_key])) \
                                                    .replace("${moduleDict}", str(drug_info_3_module_dict)) \
                                                    .replace("${moduleRule}", str(drug_info_3_module_rule))
                                                sub_formatted_user_prompt = user_prompt \
                                                    .replace("${finalStructuredData}",
                                                             str(chunked_structured_data[field_key])) \
                                                    .replace("${rule}", str(chunked_convert_rules[field_key])) \
                                                    .replace("${moduleDict}", str(drug_info_3_module_dict)) \
                                                    .replace("${moduleRule}", str(drug_info_3_module_rule))

                                                sub_transformer_result = await request_transformer(self.trace_id,
                                                                                                    sub_formatted_system_prompt,
                                                                                                    sub_formatted_user_prompt,
                                                                                                    None,
                                                                                                    f"drugInfo_dose_{rule_key}")
                                                try:
                                                    sub_transformer_result = JSONHelper.transformer(sub_transformer_result)
                                                except (Exception,) as exc:
                                                    logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=str(exc))
                                                    logger.warning("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=sub_transformer_result)
                                                    try:
                                                        sub_transformer_result = ast.literal_eval(
                                                            sub_transformer_result)
                                                    except (Exception,) as exc:
                                                        logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=str(exc))
                                                        try:
                                                            sub_transformer_result = normalize_keys(
                                                                ast.literal_eval(
                                                                    sub_transformer_result.replace("\n", "")))
                                                        except (Exception,) as exc:
                                                            logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=str(exc))
                                                            traceback.print_exc()
                                                chunked_item_result[field_key] = sub_transformer_result

                                    process_fields_tasks = [_process_fields(field_key) for field_key in
                                                            ['remain_drug_info', 'btnAddInd', 'expDoseTable']]
                                    await asyncio.gather(*process_fields_tasks)
                                    order_keys = list(rule_value[0].keys())
                                    return {order_key: chunked_item_result.get(order_key) for order_key in order_keys if
                                            order_key in chunked_item_result}

                                process_item_tasks = [_process_item(chunked_item) for chunked_item in chunked_items]
                                return await asyncio.gather(*process_item_tasks)

                            process_chunk_tasks = [_process_chunk(chunked_items) for chunked_items in
                                                   chunked_structured_list]
                            nested_list = await asyncio.gather(*process_chunk_tasks)
                            report_drug_info_result[rule_key] = [item for sublist in nested_list for item in sublist]

                        sub_tasks = [asyncio.create_task(_sub_process(rule_key, rule_value))
                                     for rule_key, rule_value in sub_drug_info_convert_rules.items()]
                        await asyncio.gather(*sub_tasks)
                        ordered_drug_info_result = OrderedDict(
                            (key, report_drug_info_result[key]) for key in module_structured_data if
                            key in report_drug_info_result)
                        raw_transformer_data[module] = deepcopy(ordered_drug_info_result)
                        final_transformer_data[module] = self.post_process(module, module_structured_data,
                                                                           ordered_drug_info_result)
                        final_transformer_data[module] = await self.secondary_transformer_data(module,
                                                                                               final_transformer_data[
                                                                                                   module])
                    except (Exception,) as e:
                        logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=f"{counter}: {str(e)}")
                        traceback.print_exc()
                    else:
                        break
                    finally:
                        counter += 1
                transformed_data = transform_json(final_transformer_data, self.tenant_id, self.language)
                final_transformer_data[module] = transformed_data[module]
                return str(TransformerStreamData(
                    data={'type': 'message', 'module': module,
                          'message': transformed_data[module]}))

            elif module == 'reportSaeDetailInfo':
                # 获取字典映射数据
                module_dict = self.get_module_dict_from_db(
                    project_code=self.protocal_number,
                    module=module
                )
                # 获取规则
                module_rule = self.get_module_rule(module)

                update_key = 'desc_reptd'
                data = deepcopy(module_structured_data)
                data = [data] if isinstance(data, dict) else data
                desc_reptd_items = [{update_key: item[update_key]} for item in data]
                desc_reptd_items = merge_and_deduplicate(desc_reptd_items)
                desc_reptd_encoded_result = crud.query_encoded_data(desc_reptd_items, self.tenant_id, self.protocal_number)
                if is_consistent(desc_reptd_items, desc_reptd_encoded_result):
                    desc_reptd_update_encoding = update_encoding_values(data, desc_reptd_encoded_result)
                    raw_transformer_data[module] = deepcopy(desc_reptd_update_encoding)
                    final_transformer_data[module] = self.post_process(module, module_structured_data,
                                                                       desc_reptd_update_encoding)
                else:
                    # 提取需要匹配的术语
                    input_terms = [item.get(update_key) for item in data if item.get(update_key)]
                    
                    # 应用过滤逻辑 (为 desc_reptd 保留过滤)
                    filtered_module_dict = await self.filter_dictionary_data(
                        module_dict,
                        input_terms
                    )
                    logger.info(
                        "{md5} | {id} | 应用 reportSaeDetailInfo (desc_reptd) 的字典过滤 | terms_count={count}",
                        md5=self.md5,
                        id=self.trace_id,
                        count=len(input_terms)
                    )
                    
                    formatted_system_prompt = system_prompt \
                        .replace("${finalStructuredData}", str(module_structured_data)) \
                        .replace("${rule}", str(rule)) \
                        .replace("${moduleDict}", str(filtered_module_dict)) \
                        .replace("${moduleRule}", str(module_rule))
                    formatted_user_prompt = user_prompt \
                        .replace("${finalStructuredData}", str(module_structured_data)) \
                        .replace("${rule}", str(rule)) \
                        .replace("${moduleDict}", str(filtered_module_dict)) \
                        .replace("${moduleRule}", str(module_rule))
                    transformer_result = await request_transformer(self.trace_id, 
                                                                  formatted_system_prompt,
                                                                  formatted_user_prompt,
                                                                  None,
                                                                  f"reportSaeDetailInfo")
                    try:
                        transformer_result = JSONHelper.transformer(transformer_result)
                    except (Exception,) as e:
                        logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=str(e))
                        logger.warning("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=transformer_result)
                        try:
                            transformer_result = ast.literal_eval(transformer_result)
                        except (Exception,) as e:
                            logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=str(e))
                            try:
                                transformer_result = ast.literal_eval(transformer_result.replace("\n", ""))
                                transformer_result = normalize_keys(transformer_result)
                            except (Exception,) as e:
                                logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=str(e))
                                traceback.print_exc()
                    raw_transformer_data[module] = deepcopy(transformer_result)
                    final_transformer_data[module] = self.post_process(module, module_structured_data,
                                                                       transformer_result)
                transformed_data = transform_json(final_transformer_data, self.tenant_id, self.language)
                final_transformer_data[module] = transformed_data[module]
                return str(
                    TransformerStreamData(
                        data={'type': 'message', 'module': module,
                              'message': transformed_data[module]})
                )

            else:
                # 获取字典映射数据
                module_dict = self.get_module_dict_from_db(
                    project_code=self.protocal_number,
                    module=module
                )
                # 获取规则
                module_rule = self.get_module_rule(module)

                if (module_dict is None or len(module_dict) == 0) and (module_rule is None or len(module_rule) == 0):
                    final_transformer_data[module] = module_structured_data
                    raw_transformer_data[module] = module_structured_data
                    transformed_data = transform_json(final_transformer_data, self.tenant_id, self.language)
                    final_transformer_data[module] = transformed_data[module]
                    return str(
                        TransformerStreamData(
                            data={'type': 'message', 'module': module,
                                  'message': transformed_data[module]})
                        )
                else:
                    formatted_system_prompt = system_prompt \
                        .replace("${finalStructuredData}", str(module_structured_data)) \
                        .replace("${rule}", str(rule)) \
                        .replace("${moduleDict}", str(module_dict)) \
                        .replace("${moduleRule}", str(module_rule))
                    formatted_user_prompt = user_prompt \
                        .replace("${finalStructuredData}", str(module_structured_data)) \
                        .replace("${rule}", str(rule)) \
                        .replace("${moduleDict}", str(module_dict)) \
                        .replace("${moduleRule}", str(module_rule))
                    transformer_result = await request_transformer(self.trace_id, 
                                                                  formatted_system_prompt,
                                                                  formatted_user_prompt,
                                                                  None,
                                                                  f"通用模块_{module}")
                    try:
                        transformer_result = JSONHelper.transformer(transformer_result)
                    except (Exception,) as e:
                        logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=str(e))
                        logger.warning("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=transformer_result)
                        try:
                            transformer_result = ast.literal_eval(transformer_result)
                        except (Exception,) as e:
                            logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=str(e))
                            try:
                                transformer_result = ast.literal_eval(transformer_result.replace("\n", ""))
                                transformer_result = normalize_keys(transformer_result)
                            except (Exception,) as e:
                                logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=str(e))
                                traceback.print_exc()
                    raw_transformer_data[module] = deepcopy(transformer_result)
                    final_transformer_data[module] = self.post_process(module, module_structured_data,
                                                                       transformer_result)
                    transformed_data = transform_json(final_transformer_data, self.tenant_id, self.language)
                    final_transformer_data[module] = transformed_data[module]
                    return str(
                        TransformerStreamData(
                            data={'type': 'message', 'module': module,
                                  'message': transformed_data[module]})
                        )

        convert_rules_prompt = JSONHelper.transformer(re.sub(r'//.*', '', structured_convert_rules))
        tasks = [asyncio.create_task(_process(module, rule)) for module, rule in convert_rules_prompt.items()]
        # 并发执行所有任务
        results = await asyncio.gather(*tasks)

        # 将药品中的既往用药 设置到病史中
        self.process_past_medication_history(final_transformer_data)

        # 按顺序返回结果
        for result in results:
            try:
                result_data = JSONHelper.transformer(result[result.index('{'):result.rindex('}')+1])
                if result_data.get('type') == 'message' and result_data.get('module') == 'reportSubjectInfo':
                    # 直接从 final_transformer_data 获取对应模块数据
                    subject_info = final_transformer_data.get('reportSubjectInfo')
                    subject_result = TransformerStreamData(
                        data={'type': 'message', 'module': 'reportSubjectInfo', 'message': subject_info}
                    )
                    yield str(subject_result)
                else:
                    yield result
            except Exception as e:
                logger.error("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=str(e))
                yield result

        convert_rules_keys = list(convert_rules_prompt.keys())
        sorted_final_transformer_data = OrderedDict(
            (key, final_transformer_data[key]) for key in convert_rules_keys if key in final_transformer_data)
        with open(self.transformer_path, 'w', encoding='utf-8') as file:
            json.dump(sorted_final_transformer_data, file, ensure_ascii=False, indent=4)

        sorted_raw_transformer_data = OrderedDict(
            (key, raw_transformer_data[key]) for key in convert_rules_keys if key in raw_transformer_data)
        with open(self.raw_transformer_path, 'w', encoding='utf-8') as file:
            json.dump(sorted_raw_transformer_data, file, ensure_ascii=False, indent=4)

        crud.insert_encoding_cache(self.trace_id, self.gpt_encoding_cache(), self.tenant_id, self.protocal_number)
        logger.info("{md5} | {id} | {message}", md5=self.md5, id=self.trace_id, message=f"finish transformer")

    async def secondary_transformer_data(self, module, module_transformer_data):

        if module == 'reportSubjectInfo':
            def sort_key(_item):
                for _key, _value in _item.items():
                    if _key.startswith("Rel_Hist_Table_") and _key.endswith("_pat_hist_type"):
                        if _value in ["既往用药"]:
                            # 返回一个较大的值，使这些项排在最后
                            return float('inf'),
                    if _key.startswith("Rel_Hist_Table_") and _key.endswith("_pat_hist_start"):
                        date_str = _value
                        if not date_str:
                            return -1,
                        date_str = date_str.replace("UK", "00").replace("??", "00")
                        parts = date_str.split("-")
                        if len(parts) == 1:
                            date_str = f"{parts[0]}-00-00"
                        elif len(parts) == 2:
                            date_str = f"{parts[0]}-{parts[1]}-00"
                        try:
                            parsed_date = datetime.strptime(date_str, "%Y-%m-%d")
                            return parsed_date.year, parsed_date.month, parsed_date.day
                        except ValueError:
                            return -1,
                return -1,

            secondary_system_prompt = read_prompt(self.prompts_dict['transformer_secondary_system_prompt'], self.language)
            secondary_user_prompt = read_prompt(self.prompts_dict['transformer_secondary_system_prompt'], self.language)
            module_dict_path = read_prompt(self.prompts_dict['transformer_convert_rule'], self.language).joinpath(module).joinpath(
                "reportSubjectInfo_2.md")
            convert_dict = read_prompt(str(module_dict_path), self.language)
            module_dict = ""
            if convert_dict.find("```moduleDict") > -1:
                module_dict_start_index = convert_dict.find("```moduleDict") + len("```moduleDict")
                module_dict_end_index = convert_dict.find("```", module_dict_start_index)
                module_dict = convert_dict[module_dict_start_index:module_dict_end_index].strip()

            formatted_secondary_system_prompt = secondary_system_prompt \
                .replace("${transformerData}", str(module_transformer_data["rel_hist_add"])) \
                .replace("${moduleDict}", str(module_dict))
            formatted_secondary_user_prompt = secondary_user_prompt \
                .replace("${transformerData}", str(module_transformer_data["rel_hist_add"])) \
                .replace("${moduleDict}", str(module_dict))
            secondary_transformer_result = await request_transformer(self.trace_id,
                                                                      formatted_secondary_system_prompt,
                                                                      formatted_secondary_user_prompt,
                                                                      None,
                                                                      f"secondary_transform_rel_hist_add")

            module_transformer_data["rel_hist_add"] = JSONHelper.transformer(secondary_transformer_result)
            sorted_rel_hist_add = sorted(module_transformer_data['rel_hist_add'], key=sort_key)
            reorder_data = []
            for i, item in enumerate(sorted_rel_hist_add):
                values = list(item.values())
                if len(values) < 6:
                    continue
                new_item = {
                    f"TXT_Rel_Hist_Table_{i}_pat_hist_type": values[0],
                    f"Rel_Hist_Table_{i}_pat_hist_rptd": values[1],
                    f"Rel_Hist_Table_{i}_pat_hist_rptd_lltname": values[2],
                    f"Rel_Hist_Table_{i}_pat_hist_start": values[3],
                    f"Rel_Hist_Table_{i}_pat_hist_stop": values[4],
                    f"Rel_Hist_Table_{i}_pat_hist_cont": values[5],
                }
                reorder_data.append(new_item)
            module_transformer_data["rel_hist_add"] = reorder_data
            return module_transformer_data

        elif module == 'drugInfo':
            def process_btn_add_ind(btn_index, btn_item):
                rep_llt_name = 'Ind_Table_{}_ind_reptd_lltname'.format(btn_index)
                if isinstance(btn_item[rep_llt_name], list):
                    return [
                        {
                            f"Ind_Table_{idx}_ind_reptd": btn_item[f'Ind_Table_{btn_index}_ind_reptd'],
                            f"Ind_Table_{idx}_ind_reptd_lltname": llt_name_item
                        }
                        for idx, llt_name_item in enumerate(btn_item[rep_llt_name])
                    ]
                else:
                    return [btn_item]

            def reorder_btn_add_ind(btn_add_ind):
                reorder_btn_add_ind_list = []
                for idx, btn_add_ind_item in enumerate(btn_add_ind):
                    val = list(btn_add_ind_item.values())
                    new_btn_item = {
                        f"Ind_Table_{idx}_ind_reptd": val[0],
                        f"Ind_Table_{idx}_ind_reptd_lltname": val[1]
                    }
                    reorder_btn_add_ind_list.append(new_btn_item)
                return reorder_btn_add_ind_list

            for drug_info in module_transformer_data.values():
                for drug_item in drug_info:
                    btn_add_ind_list = []
                    for index, item in enumerate(drug_item['btnAddInd']):
                        btn_add_ind_list.extend(process_btn_add_ind(index, item))
                    drug_item['btnAddInd'] = reorder_btn_add_ind(btn_add_ind_list)
        return module_transformer_data

    def gpt_encoding_cache(self):
        try:
            with open(self.structured_path, 'r', encoding='utf-8') as file:
                structured_data = JSONHelper.transformer(file.read())
            with open(self.transformer_path, 'r', encoding='utf-8') as file:
                transformer_data = JSONHelper.transformer(file.read())
            result_list = []
            report_subject_info = transformer_data.get("reportSubjectInfo")
            rel_hist_add = report_subject_info.get('rel_hist_add')
            for item in rel_hist_add:
                values = list(item.values())
                result_list.append({
                    "field_name": "rel_hist_table_pat_hist_rptd",
                    "original_value": values[1],
                    "encoded_value": values[2]
                })
            structured_ref_examine_table = structured_data.get("reportSubjectInfo").get('refExamineTable')
            transformer_ref_examine_table = report_subject_info.get('refExamineTable')
            for item1, item2 in zip(structured_ref_examine_table, transformer_ref_examine_table):
                result_list.append({
                    "field_name": "labtest",
                    "original_value": item1["labtest"],
                    "encoded_value": item2["labtest"]
                })
                result_list.append({
                    "field_name": "TXT_labunit_0",
                    "original_value": item1["TXT_labunit_0"],
                    "encoded_value": item2["TXT_labunit_0"]
                })
            transformer_drug_info = transformer_data.get("drugInfo")
            structured_drug_info_shiyan = structured_data.get("drugInfo").get('试验用药')
            transformer_drug_info_shiyan = transformer_drug_info.get('试验用药')
            for item1, item2 in zip(structured_drug_info_shiyan, transformer_drug_info_shiyan):
                result_list.append({
                    "field_name": "pat_exposure",
                    "original_value": item1["pat_exposure"],
                    "encoded_value": item2["pat_exposure"]
                })
            for drug in ['合并用药', '治疗用药']:
                for item1, item2 in zip(structured_data.get("drugInfo").get(drug), transformer_drug_info.get(drug)):
                    result_list.append({
                        "field_name": "TXT_formulation_id",
                        "original_value": item1["TXT_formulation_id"],
                        "encoded_value": item2["TXT_formulation_id"]
                    })
            for _, items in transformer_drug_info.items():
                for item in items:
                    if "product_name" in item and "product_name_lltname" in item:
                        result_list.append({
                            "field_name": "product_name",
                            "original_value": item["product_name"],
                            "encoded_value": item["product_name_lltname"]
                        })
            for item1, item2 in zip(structured_data.get("drugInfo").items(), transformer_drug_info.items()):
                for sub_item1, sub_item2 in zip(item1[1], item2[1]):
                    for sub_sub_item1, sub_sub_item2 in zip(sub_item1['expDoseTable'], sub_item2['expDoseTable']):
                        result_list.append({
                            "field_name": "TXT_cdr_dose_unit_id",
                            "original_value": sub_sub_item1["TXT_cdr_dose_unit_id"],
                            "encoded_value": sub_sub_item2["TXT_cdr_dose_unit_id"]
                        })
                        result_list.append({
                            "field_name": "TXT_freq_id",
                            "original_value": sub_sub_item1["TXT_freq_id"],
                            "encoded_value": sub_sub_item2["TXT_freq_id"]
                        })
                        result_list.append({
                            "field_name": "TXT_cdr_admin_route_id",
                            "original_value": sub_sub_item1["TXT_cdr_admin_route_id"],
                            "encoded_value": sub_sub_item2["TXT_cdr_admin_route_id"]
                        })
            for _, items in transformer_drug_info.items():
                for sub_item in items:
                    for sub_sub_item in sub_item.get('btnAddInd'):
                        values = list(sub_sub_item.values())
                        result_list.append({
                            "field_name": "Ind_Table_ind_reptd",
                            "original_value": values[0],
                            "encoded_value": values[1]
                        })
            transformer_report_sae_detail_info = transformer_data.get("reportSaeDetailInfo")
            for item in transformer_report_sae_detail_info:
                result_list.append({
                    "field_name": "desc_reptd",
                    "original_value": item['desc_reptd'],
                    "encoded_value": item['desc_coded']
                })
            return result_list
        except (Exception,) as e:
            logger.error("{md5} | {id} | 编码缓存生成失败 | 错误={error}\n{traceback}",
                        md5=self.md5, id=self.trace_id,
                        error=str(e), traceback=traceback.format_exc())
            return []

    @staticmethod
    def post_process(module, module_structured_data, module_transformer_data):

        if module == 'reportSubjectInfo':
            for i, rel_hist in enumerate(module_transformer_data["rel_hist_add"]):
                llt_key = f"Rel_Hist_Table_{i}_pat_hist_rptd_lltname"
                llt_val = rel_hist.get(f'Rel_Hist_Table_{i}_pat_hist_rptd')
                rel_hist[f'Rel_Hist_Table_{i}_pat_hist_rptd'] = module_structured_data["rel_hist_add"][i].get(
                    f'Rel_Hist_Table_{i}_pat_hist_rptd')
                keys = list(rel_hist.keys())
                index_position = keys.index(f'Rel_Hist_Table_{i}_pat_hist_rptd')
                values = list(rel_hist.values())
                keys.insert(index_position + 1, llt_key)
                values.insert(index_position + 1, llt_val)
                module_transformer_data["rel_hist_add"][i] = dict(zip(keys, values))

            for i, ref_examine_table in enumerate(module_structured_data["refExamineTable"]):
                module_transformer_data['refExamineTable'][i]['labtestreptd'] = ref_examine_table['labtestreptd']

        if module == 'drugInfo':
            for key, data_list in module_transformer_data.items():
                for item_index, item in enumerate(data_list):
                    if key == "试验用药":
                        if not item.get('pat_exposure'):
                            item['pat_exposure'] = item.get('product_name', '').split('(')[0].strip()

                    # 处理 product_name 字段
                    product_name_key = 'product_name'
                    product_name_llt_key = 'product_name_lltname'
                    product_name_llt_val = item.get(product_name_key)

                    # 插入 product_name_lltname 字段到 product_name 后面
                    keys = list(item.keys())
                    values = list(item.values())
                    index_position = keys.index(product_name_key)
                    keys.insert(index_position + 1, product_name_llt_key)
                    values.insert(index_position + 1, product_name_llt_val)
                    module_transformer_data[key][item_index] = dict(zip(keys, values))

                    # 保留原始 product_name 值
                    original_product_name_val = module_structured_data[key][item_index].get(product_name_key)
                    item_dict = dict(zip(keys, values))
                    item_dict[product_name_key] = original_product_name_val
                    module_transformer_data[key][item_index] = item_dict

                    if isinstance(item['btnAddInd'], dict):
                        item['btnAddInd'] = [item['btnAddInd']]
                    for btn_index, btn_item in enumerate(item['btnAddInd']):
                        if isinstance(module_structured_data[key][item_index]['btnAddInd'], dict):
                            module_structured_data[key][item_index]['btnAddInd'] = [
                                module_structured_data[key][item_index]['btnAddInd']]
                        reported_key = list(btn_item.keys())[0]
                        llm_key = 'Ind_Table_{}_ind_reptd_lltname'.format(btn_index)
                        llm_val = btn_item.get('Ind_Table_{}_ind_reptd'.format(btn_index))
                        module_transformer_data[key][item_index]['btnAddInd'][btn_index][reported_key] = \
                            module_structured_data[key][item_index]['btnAddInd'][btn_index][reported_key]
                        module_transformer_data[key][item_index]['btnAddInd'][btn_index][llm_key] = llm_val

        if module == 'reportSaeDetailInfo':
            if isinstance(module_structured_data, dict):
                module_structured_data = [module_structured_data]
            if isinstance(module_transformer_data, dict):
                module_transformer_data = [module_transformer_data]
            for i, item in enumerate(module_structured_data):
                keys = list(module_transformer_data[i].keys())
                values = list(module_transformer_data[i].values())
                index_position = keys.index('desc_reptd')
                keys.insert(index_position + 1, "desc_coded")
                values.insert(index_position + 1, module_transformer_data[i]['desc_reptd'])
                new_dict = dict(zip(keys, values))
                new_dict['desc_reptd'] = item["desc_reptd"]
                module_transformer_data[i] = new_dict

        return module_transformer_data

    # 实验室检查最后转换
    async def ref_examine_final_transformer(self, ref_examine_table):
        # 如果实验室检查列表为空则直接返回
        if not ref_examine_table:
            return ref_examine_table

        # 获取提示词模板
        ref_examine_final_system_prompt = read_prompt(self.prompts_dict['transformer_ref_examine_final_system_prompt'], self.language)
        ref_examine_final_user_prompt = read_prompt(self.prompts_dict['transformer_ref_examine_final_user_prompt'], self.language)

        # 如果提示词模板不存在则直接返回
        if not ref_examine_final_system_prompt or not ref_examine_final_user_prompt:
            logger.warning(
                "{md5} | {id} | 未找到实验室检查转换提示词模板",
                md5=self.md5,
                id=self.trace_id
            )
            return ref_examine_table

        try:
            # 读取全息视图的数据
            with open(self.extract_result_path, 'r', encoding='utf-8') as file:
                extract_result = JSONHelper.transformer(file.read())

            # 获取与事件相关的实验室检查数据
            related_examines = extract_result.get('与事件相关的实验室检查', [])
            
            # 如果没有相关实验室检查数据，直接返回原数据
            if not related_examines:
                return ref_examine_table

            # 分批处理实验室检查数据
            batch_size = 10
            final_result = []

            # 将数据分成批次
            batches = [ref_examine_table[i:i + batch_size] for i in range(0, len(ref_examine_table), batch_size)]
            
            # 创建批次处理任务
            batch_tasks = []
            for batch_index, batch_data in enumerate(batches):
                logger.info(
                    "{md5} | {id} | 处理实验室检查数据批次 | batch={batch}/{total}",
                    md5=self.md5,
                    id=self.trace_id,
                    batch=batch_index + 1,
                    total=len(batches)
                )
                
                # 创建批次处理任务
                batch_tasks.append(self._process_batch(batch_data, batch_index, related_examines, 
                                                     ref_examine_final_system_prompt, ref_examine_final_user_prompt))
            
            # 并发执行所有批次处理任务
            batch_results = await asyncio.gather(*batch_tasks)
            
            # 合并所有批次的结果
            for result in batch_results:
                final_result.extend(result)

            logger.info(
                "{md5} | {id} | 实验室检查数据处理完成 | total_items={count}",
                md5=self.md5,
                id=self.trace_id,
                count=len(final_result)
            )

            return final_result

        except (Exception,) as e:
            logger.error(
                "{md5} | {id} | 实验室检查数据处理失败 | error={error}",
                md5=self.md5,
                id=self.trace_id,
                error=str(e)
            )
            raise Exception(f"实验室检查数据处理失败: {e}")

    async def _process_batch(self, batch_data, batch_index, related_examines, system_prompt, user_prompt):
        """处理单个批次的实验室检查数据
        
        Args:
            batch_data: 批次数据
            batch_index: 批次索引
            related_examines: 相关检查数据
            system_prompt: 系统提示词
            user_prompt: 用户提示词
            
        Returns:
            list: 处理后的批次数据
        """
        try:
            # 匹配批次数据与相关实验室检查数据
            matched_data = []
            matched_related_examines = []
            for item in batch_data:
                # 检查是否有必要的字段
                if 'examineDate' not in item or 'labtestreptd' not in item:
                    logger.warning(
                        "{md5} | {id} | 实验室检查数据缺少必要字段 | item={item}",
                        md5=self.md5,
                        id=self.trace_id,
                        item=item
                    )
                    raise ValueError(f"实验室检查数据缺少必要字段: {item}")

                # 查找匹配的相关检查数据
                matched = False
                for related_item in related_examines:
                    if ('检查日期' in related_item and
                            '检查项目名称' in related_item and
                            related_item['检查日期'] == item['examineDate'] and
                            related_item['检查项目名称'] == item['labtestreptd']):
                        matched = True
                        matched_related_examines.append(related_item)
                        break

                if not matched:
                    logger.warning(
                        "{md5} | {id} | 未找到匹配的实验室检查数据 | date={date}, test={test}",
                        md5=self.md5,
                        id=self.trace_id,
                        date=item['examineDate'],
                        test=item['labtestreptd']
                    )
                    raise ValueError(
                        f"未找到匹配的实验室检查数据: 日期={item['examineDate']}, 检查项目={item['labtestreptd']}")

                matched_data.append(item)

            # 格式化提示词
            formatted_system_prompt = system_prompt \
                .replace("${refExamineTable}", json.dumps(matched_data, ensure_ascii=False)) \
                .replace("${relatedExamines}", str(matched_related_examines))

            # 调用大模型进行转换
            transformer_result = await request_transformer(
                self.trace_id,
                formatted_system_prompt,
                formatted_system_prompt,
                None,
                f"ref_examine_final_batch_{batch_index}"
            )

            try:
                # 尝试识别和提取JSON部分
                json_content = transformer_result
                # 查找JSON数组的开始和结束位置
                if "[" in transformer_result and "]" in transformer_result:
                    start_idx = transformer_result.find("[")
                    end_idx = transformer_result.rfind("]") + 1
                    if start_idx > -1 and end_idx > start_idx:
                        json_content = transformer_result[start_idx:end_idx]
                        logger.info(
                            "{md5} | {id} | 成功提取JSON内容 | batch={batch}",
                            md5=self.md5,
                            id=self.trace_id,
                            batch=batch_index + 1
                        )
                
                # 尝试解析JSON
                batch_result = JSONHelper.transformer(json_content)
                return batch_result
            except (Exception,) as e:
                logger.error(
                    "{md5} | {id} | 实验室检查数据转换失败 | batch={batch} | error={error}",
                    md5=self.md5,
                    id=self.trace_id,
                    batch=batch_index + 1,
                    error=str(e)
                )
                logger.warning(
                    "{md5} | {id} | 原始转换结果 | result={result}",
                    md5=self.md5,
                    id=self.trace_id,
                    result=transformer_result
                )
                try:
                    # 尝试通过正则表达式提取JSON数组
                    json_match = re.search(r'\[(.*?)\]', transformer_result, re.DOTALL)
                    if json_match:
                        json_content = "[" + json_match.group(1) + "]"
                        batch_result = JSONHelper.transformer(json_content)
                        logger.info(
                            "{md5} | {id} | 通过正则表达式成功提取JSON | batch={batch}",
                            md5=self.md5,
                            id=self.trace_id,
                            batch=batch_index + 1
                        )
                        return batch_result
                    else:
                        # 如果正则表达式也失败，尝试使用ast.literal_eval
                        json_content = transformer_result
                        if "```json" in transformer_result:
                            start_idx = transformer_result.find("```json") + 7
                            end_idx = transformer_result.find("```", start_idx)
                            if 6 < start_idx < end_idx:
                                json_content = transformer_result[start_idx:end_idx].strip()
                        
                        batch_result = ast.literal_eval(json_content)
                        return batch_result
                except (Exception,) as nested_e:
                    logger.error(
                        "{md5} | {id} | 实验室检查数据转换失败 | batch={batch} | error={error}",
                        md5=self.md5,
                        id=self.trace_id,
                        batch=batch_index + 1,
                        error=str(nested_e)
                    )
                    # 对于无法解析的情况，直接使用原始批次数据
                    logger.warning(
                        "{md5} | {id} | 解析失败，使用原始批次数据 | batch={batch}",
                        md5=self.md5,
                        id=self.trace_id,
                        batch=batch_index + 1
                    )
                    return batch_data
        except Exception as batch_e:
            logger.error(
                "{md5} | {id} | 实验室检查批次处理失败 | batch={batch} | error={error}",
                md5=self.md5,
                id=self.trace_id,
                batch=batch_index + 1,
                error=str(batch_e)
            )
            raise ValueError(f"批次 {batch_index + 1} 处理失败: {batch_e}")

    async def filter_dictionary_data(self, original_data, input_terms, min_similarity=0.3, drug_type=None):
        """
        基于模糊匹配和相似度匹配对字典数据进行过滤，并发处理每一行
        
        Args:
            original_data: 原始字典数据
            input_terms: 需要匹配的输入术语列表
            min_similarity: 最小相似度阈值
            drug_type: 药品类型，可能值为 "试验用药"、"合并用药" 或 None（针对非药品的其他模块）
            
        Returns:
            str: 过滤后的字典数据
            
        特性:
            1. 支持模糊匹配和相似度匹配
            2. 并发处理提高性能
            3. 针对特定字段（如desc_reptd、ind_reptd等）进行特殊去重处理:
               - 对于符合"编码(文本)"格式的数据（如"10037844(皮疹)"或"016193.02.038(Palonosetron)"）
               - 当第一列和括号中的文本相同时进行分组
               - 在每组中保留编码字符串最小的记录
            4. 保留数据字典的原始结构和格式
        """
        if not original_data or not input_terms:
            # 记录字典数据为空的情况
            logger.warning(
                "{md5} | {id} | 字典数据为空 | original_data={data} | input_terms={terms}",
                md5=self.md5,
                id=self.trace_id,
                data="空" if not original_data else "有数据",
                terms="空" if not input_terms else f"数量: {len(input_terms)}"
            )
            return original_data
        
        try:
            # 清理输入术语，移除空值和重复项
            cleaned_terms = []
            for term in input_terms:
                if term and isinstance(term, str) and term.strip():
                    cleaned_terms.append(term.strip())
            
            if not cleaned_terms:
                # 记录清理后术语为空的情况
                logger.warning(
                    "{md5} | {id} | 清理后术语为空 | original_terms={terms}",
                    md5=self.md5,
                    id=self.trace_id,
                    terms=f"原始术语数量: {len(input_terms)}"
                )
                return original_data
                
            # 提取字典数据部分
            if isinstance(original_data, str):
                # 尝试找到字典数据部分 (通常在 ```moduleDict 和 ``` 之间)
                dict_pattern = r'```(?:json|moduleDict)?\s*([\s\S]*?)```'
                dict_match = re.search(dict_pattern, original_data)
                
                if dict_match:
                    dict_content = dict_match.group(1).strip()
                    dict_prefix = original_data[:dict_match.start()]
                    dict_suffix = original_data[dict_match.end():]
                else:
                    # 如果没有明确的格式，假设整个字符串就是字典内容
                    dict_content = original_data
                    dict_prefix = ""
                    dict_suffix = ""
            else:
                # 如果不是字符串类型，直接返回
                return original_data
            
            # 将字典内容按行分割
            lines = dict_content.strip().split('\n')
            
            # 识别模板结构和数据行
            template_blocks = []
            current_block = []
            in_data_section = False
            current_field = ""
            
            for i, line in enumerate(lines):
                line_stripped = line.strip()
                
                # 新的字段说明行开始一个新的模板块
                if line_stripped.startswith('#'):
                    # 如果有前一个块，保存它
                    if current_block:
                        template_blocks.append((current_field, current_block))
                    
                    current_block = [line]
                    in_data_section = False
                    # 尝试提取字段名
                    field_match = re.search(r'#\s*"([^"]+)"', line_stripped)
                    current_field = field_match.group(1) if field_match else ""
                
                # 表头行
                elif line_stripped.startswith('|') and i < len(lines) - 1 and lines[i+1].strip().startswith('|') and '-' in lines[i+1]:
                    current_block.append(line)
                    in_data_section = False
                
                # 分隔行 (|---|---|)
                elif line_stripped.startswith('|') and '-' in line_stripped:
                    current_block.append(line)
                    in_data_section = False
                
                # 空行
                elif not line_stripped:
                    current_block.append(line)
                    in_data_section = False
                
                # 占位符行 ({field_name})
                elif line_stripped.startswith('{') and line_stripped.endswith('}'):
                    current_block.append(line)
                    in_data_section = True
                
                # 数据行
                else:
                    if not in_data_section:
                        in_data_section = True
                        current_block.append(line)  # 第一行数据
                    else:
                        current_block.append(line)  # 后续数据行
            
            # 保存最后一个块
            if current_block:
                template_blocks.append((current_field, current_block))
            
            logger.info(
                "{md5} | {id} | 识别模板结构 | 模板块数={blocks}",
                md5=self.md5,
                id=self.trace_id,
                blocks=len(template_blocks)
            )
            
            # 计算相似度函数
            def similarity(a, b):
                if not a or not b:
                    return 0
                return SequenceMatcher(None, str(a).lower(), str(b).lower()).ratio()
            
            # 处理单行的匹配逻辑
            async def process_line(line, field_name):
                if not line.strip():
                    return None
                
                # 计算行与所有输入术语的最大相似度
                max_sim = 0
                match_method = "相似度计算"
                matched_term = ""
                
                loop = asyncio.get_event_loop()
                
                # 对于不同字段的匹配规则
                if field_name == "pat_exposure":
                    # 对于pat_exposure字段保持整行匹配（虽然这里实际不会执行到，因为已单独处理）
                    comparison_text = line
                elif field_name == "TXT_formulation_id":
                    # 对于TXT_formulation_id字段，匹配第二列数据
                    line_parts = line.split('|')
                    if len(line_parts) <= 2:  # 不符合表格格式的行
                        comparison_text = line
                    else:
                        # 第二列数据（去除前后空格）
                        comparison_text = line_parts[2].strip() if len(line_parts) > 2 else ""
                else:
                    # 其他字段匹配第一列
                    line_parts = line.split('|')
                    if len(line_parts) <= 1:  # 不符合表格格式的行
                        comparison_text = line
                    else:
                        # 第一列数据（去除前后空格）
                        comparison_text = line_parts[1].strip() if len(line_parts) > 1 else ""
                        
                if not comparison_text:
                    return None
                
                for term in cleaned_terms:
                    # 包含关系匹配
                    contain_sim = 0
                    if term and term.lower() in comparison_text.lower():
                        contain_sim = 0.95
                    
                    # 相似度计算
                    seq_sim = await loop.run_in_executor(
                        None, similarity, term, comparison_text
                    )
                    
                    sim = max(contain_sim, seq_sim)
                    match_type = "包含关系" if contain_sim > seq_sim else "相似度计算"
                    
                    if sim > max_sim:
                        max_sim = sim
                        match_method = match_type
                        matched_term = term
                
                if max_sim >= min_similarity:
                    return (line, max_sim, match_method, matched_term)
                return None
            
            # 统计
            total_original_data_lines = 0
            total_filtered_data_lines = 0
            match_by_contain = 0
            match_by_similarity = 0
            
            # 对每个模板块进行处理
            filtered_blocks = []
            
            for field_name, block in template_blocks:
                logger.info(
                    "{md5} | {id} | 处理模板块 | field={field} | 行数={lines} | drug_type={drug_type}",
                    md5=self.md5,
                    id=self.trace_id,
                    field=field_name,
                    lines=len(block),
                    drug_type=drug_type if drug_type else "无"
                )
                
                # 根据药品类型决定是否跳过某些字段
                if drug_type == "试验用药" and field_name == "product_name":
                    logger.info(
                        "{md5} | {id} | 试验用药不需要product_name字典，完全排除该字段",
                        md5=self.md5,
                        id=self.trace_id
                    )
                    # 不添加到filtered_blocks，直接跳过
                    continue
                elif (drug_type == "合并用药" or drug_type == "治疗用药") and field_name == "pat_exposure":
                    logger.info(
                        "{md5} | {id} | {drug_type}不需要pat_exposure字典，完全排除该字段",
                        md5=self.md5,
                        id=self.trace_id,
                        drug_type=drug_type
                    )
                    # 不添加到filtered_blocks，直接跳过
                    continue
                
                # 找到数据部分的起始位置
                header_end_idx = -1
                for i, line in enumerate(block):
                    if '---' in line and '|' in line:
                        header_end_idx = i
                        break
                
                if header_end_idx == -1:
                    # 如果没有找到表头分隔行，保持原块不变
                    filtered_blocks.append(block)
                    continue
                
                # 将块分为模板部分和数据部分
                template_part = block[:header_end_idx+1]
                data_part = block[header_end_idx+1:]
                
                # 找到占位符行和实际数据行
                placeholder_lines = [line for line in data_part if line.strip().startswith('{') and line.strip().endswith('}')]
                data_lines = [line for line in data_part if not (line.strip().startswith('{') and line.strip().endswith('}'))]
                
                total_original_data_lines += len(data_lines)
                
                if not data_lines:
                    # 如果没有实际数据行，保持原块不变
                    filtered_blocks.append(block)
                    continue
                
                # 处理不同字段的数据
                if field_name == "pat_exposure":
                    # 专门处理pat_exposure字典 - 只按protocal_number过滤
                    logger.info(
                        "{md5} | {id} | 专门处理pat_exposure字典 | protocal_number={protocal} | 数据行数={lines}",
                        md5=self.md5,
                        id=self.trace_id,
                        protocal=self.protocal_number,
                        lines=len(data_lines)
                    )
                    
                    # 过滤数据行 - 仅保留匹配protocal_number的行，并删除protocal_number列
                    filtered_data = []
                    for line in data_lines:
                        line_parts = line.split('|')
                        if len(line_parts) > 2 and line_parts[1].strip() == self.protocal_number:
                            # 删除第一列：保留第0部分(开头的|)和第2部分及之后的内容
                            new_line = line_parts[0] + '|' + '|'.join(line_parts[2:])
                            filtered_data.append(new_line)
                            logger.info(
                                "{md5} | {id} | 匹配到protocal_number并删除该列 | 原行={old_line} | 新行={new_line}",
                                md5=self.md5,
                                id=self.trace_id,
                                old_line=line.strip(),
                                new_line=new_line.strip()
                            )
                    
                    # 表头和分隔行保持不变，直接使用原始的template_part
                    # 重新构建结果块
                    result_block = template_part.copy()
                    result_block.extend(placeholder_lines)
                    result_block.extend(filtered_data)
                    
                    # 将这个块直接添加到过滤后的块中
                    filtered_blocks.append(result_block)
                    
                    if filtered_data:
                        logger.info(
                            "{md5} | {id} | pat_exposure过滤结果 | 原始行数={original} | 过滤后行数={filtered}",
                            md5=self.md5,
                            id=self.trace_id,
                            original=len(data_lines),
                            filtered=len(filtered_data)
                        )
                    else:
                        logger.warning(
                            "{md5} | {id} | 没有找到匹配protocal_number的pat_exposure | protocal_number={protocal}",
                            md5=self.md5,
                            id=self.trace_id,
                            protocal=self.protocal_number
                        )
                        
                    # 已经处理过这个块，跳过后面的处理
                    continue
                else:
                    # 处理其他字典 - 使用相似度过滤
                    logger.info(
                        "{md5} | {id} | 使用相似度过滤 | field={field} | 数据行数={lines} | 输入术语数={terms}",
                        md5=self.md5,
                        id=self.trace_id,
                        field=field_name,
                        lines=len(data_lines),
                        terms=len(cleaned_terms)
                    )
                    
                    # 创建过滤任务并执行，注意传入字段名
                    tasks = [process_line(line, field_name) for line in data_lines]
                    results = await asyncio.gather(*tasks)
                    
                    # 过滤结果
                    matched_results = [r for r in results if r is not None]
                    
                    if not matched_results:
                        logger.warning(
                            "{md5} | {id} | 没有找到匹配项 | field={field} | 原始行数={original}",
                            md5=self.md5,
                            id=self.trace_id,
                            field=field_name,
                            original=len(data_lines)
                        )
                        # 如果没有匹配项，直接置空数据
                        filtered_data = []
                        continue
                    
                    # 排序结果
                    matched_results.sort(key=lambda x: x[1], reverse=True)
                    
                    # 统计匹配方式
                    for _, _, method, _ in matched_results:
                        if method == "包含关系":
                            match_by_contain += 1
                        else:
                            match_by_similarity += 1
                    
                    # 提取过滤后的行
                    filtered_data = [line for line, _, _, _ in matched_results]
                    
                    # 记录匹配详情
                    for i, (line, sim, method, term) in enumerate(matched_results[:10]):  # 只记录前10个
                        logger.info(
                            "{md5} | {id} | 字段'{field}'匹配项{i} | 相似度={sim:.2f} | 方式={method} | 术语={term} | 行={line}",
                            md5=self.md5,
                            id=self.trace_id,
                            field=field_name,
                            i=i+1,
                            sim=sim,
                            method=method,
                            term=term,
                            line=line[:50] + "..." if len(line) > 50 else line
                        )
                    
                    # 去重 - 添加针对特定情况的特殊处理，如 [ind_reptd] 和 [desc_reptd]
                    filtered_data_unique = []
                    
                    # 检查是否需要特殊处理
                    if field_name in ["Ind_Table_{index}_ind_reptd", "desc_reptd", "labtest", "Rel_Hist_Table_{index}_pat_hist_rptd", "product_name"]:
                        logger.info(
                            "{md5} | {id} | 对字段{field}应用特殊去重处理 | 数量={count}",
                            md5=self.md5,
                            id=self.trace_id,
                            field=field_name,
                            count=len(filtered_data)
                        )
                        
                        # 按照第一列和括号中的文本进行分组
                        group_by_code_text = {}
                        
                        for line in filtered_data:
                            line_parts = line.split('|')
                            if len(line_parts) >= 3:  # 至少有两列内容
                                first_col = line_parts[1].strip() if len(line_parts) > 1 else ""
                                second_col = line_parts[2].strip() if len(line_parts) > 2 else ""
                                
                                # 匹配第二列中的数字(文本)格式
                                # pattern = r'(\d+)\((.*?)\)'
                                pattern = r'([\d\.]+)\((.*?)\)'
                                match = re.search(pattern, second_col)
                                
                                if match:
                                    code = match.group(1)       # 数字部分（可能包含小数点）
                                    text = match.group(2)       # 括号中的文本
                                    key = f"{first_col}|{text}" # 用第一列和括号中的文本作为key
                                    
                                    # 如果已存在，使用字符串比较，保留code较小的
                                    if key in group_by_code_text:
                                        existing_line, existing_code = group_by_code_text[key]
                                        
                                        # 直接使用字符串比较
                                        if code < existing_code:
                                            group_by_code_text[key] = (line, code)
                                            logger.debug(
                                                "{md5} | {id} | 替换记录(字符串比较) | key={key} | 原code={old_code} | 新code={new_code}",
                                                md5=self.md5,
                                                id=self.trace_id,
                                                key=key,
                                                old_code=existing_code,
                                                new_code=code
                                            )
                                    else:
                                        group_by_code_text[key] = (line, code)
                            else:
                                # 不符合表格格式的行，保留原样
                                filtered_data_unique.append(line)
                        
                        # 添加每个分组中代码最小的行
                        for key, (line, _) in group_by_code_text.items():
                            filtered_data_unique.append(line)
                        
                        special_dedup_count = len(filtered_data) - len(filtered_data_unique)
                        if special_dedup_count > 0:
                            logger.info(
                                "{md5} | {id} | 特殊去重结果 | field={field} | 去重前={before} | 去重后={after} | 移除条数={removed}",
                                md5=self.md5,
                                id=self.trace_id,
                                field=field_name,
                                before=len(filtered_data),
                                after=len(filtered_data_unique),
                                removed=special_dedup_count
                            )
                    else:
                        # 标准去重逻辑 - 基于整行文本
                        seen_lines = set()
                        for line in filtered_data:
                            line_stripped = line.strip()
                            if line_stripped and line_stripped not in seen_lines:
                                seen_lines.add(line_stripped)
                                filtered_data_unique.append(line)
                        
                        duplicate_count = len(filtered_data) - len(filtered_data_unique)
                        if duplicate_count > 0:
                            logger.info(
                                "{md5} | {id} | 标准去重 | field={field} | 去重前={before} | 去重后={after} | 重复项={dupes}",
                                md5=self.md5,
                                id=self.trace_id,
                                field=field_name,
                                before=len(filtered_data),
                                after=len(filtered_data_unique),
                                dupes=duplicate_count
                            )
                    
                    filtered_data = filtered_data_unique
                
                # 统计过滤后的行数
                total_filtered_data_lines += len(filtered_data)
                
                # 构建过滤后的块
                # 顺序: 模板部分 + 占位符行 + 过滤后的数据行
                result_block = template_part.copy()
                result_block.extend(placeholder_lines)
                result_block.extend(filtered_data)
                
                filtered_blocks.append(result_block)
            
            # 合并所有过滤后的块
            all_filtered_lines = []
            for i, block in enumerate(filtered_blocks):
                all_filtered_lines.extend(block)
                # 在块之间添加空行，除非当前块的最后一行或下一个块的第一行已经是空行
                if i < len(filtered_blocks) - 1:  # 不是最后一个块
                    if (not block or block[-1].strip() != '') and (not filtered_blocks[i+1] or filtered_blocks[i+1][0].strip() != ''):
                        all_filtered_lines.append('')
            
            # 重建字典内容
            filtered_content = '\n'.join(all_filtered_lines)
            
            # 恢复原始格式
            if dict_match:
                result = f"{dict_prefix}```moduleDict\n{filtered_content}\n```{dict_suffix}"
            else:
                result = filtered_content
                
            logger.info(
                "{md5} | {id} | 字典过滤完成 | 原始行数={original} | 模板块数={blocks} | 原始数据行数={original_data} | 过滤后数据行数={filtered_data} | 包含匹配={contain} | 相似度匹配={similarity}",
                md5=self.md5,
                id=self.trace_id,
                original=len(lines),
                blocks=len(template_blocks),
                original_data=total_original_data_lines,
                filtered_data=total_filtered_data_lines,
                contain=match_by_contain,
                similarity=match_by_similarity
            )
            
            return result
            
        except Exception as e:
            logger.error(
                "{md5} | {id} | 字典数据过滤失败 | error={error}\n{traceback}",
                md5=self.md5,
                id=self.trace_id,
                error=str(e),
                traceback=traceback.format_exc()
            )
            # 如果处理失败，返回原始数据
            return original_data

    def process_past_medication_history(self, final_structured_data):
        """
        处理既往用药数据并更新病史信息。

        Args:
            final_structured_data (dict): 最终结构化数据。

        Returns:
            dict: 更新后的最终结构化数据。
        """
        # 检查 final_structured_data['drugInfo']['既往用药'] 是否不为空
        past_medication = final_structured_data.get('drugInfo', {}).get('既往用药', [])
        # 初始化既往用药映射字典
        past_medication_map = {}
        split_flag = "_"
        # 遍历既往用药列表
        for past_med_item in past_medication:
            product_name = past_med_item.get('product_name', '')
            start_datetime = ''
            stop_datetime = ''
            if 'expDoseTable' in past_med_item :
                if len(past_med_item['expDoseTable']) > 0:
                    for exp_dose in past_med_item['expDoseTable']:
                        start_datetime = exp_dose.get('start_datetime', '')
                        stop_datetime = exp_dose.get('stop_datetime', '')
                        key = product_name + split_flag+ start_datetime +split_flag + stop_datetime
                        past_medication_map[key] = past_med_item
                else:
                    key = product_name + split_flag + start_datetime + split_flag + stop_datetime
                    past_medication_map[key] = past_med_item

        # 处理病史信息
        report_subject_info = final_structured_data.get('reportSubjectInfo', {})
        rel_hist_add = report_subject_info.get('rel_hist_add', [])
        for entry in rel_hist_add:
            # 查找 TXT_Rel_Hist_Table_{index}_pat_hist_type 且值为 "用药史" 的条目
            for key in entry.keys():
                # and entry[key] == '用药史'  既往病史的 每个租户不一样 暂时无法准确判断
                if re.match(r'TXT_Rel_Hist_Table_\d+_pat_hist_type', key):
                    index_match = re.search(r'TXT_Rel_Hist_Table_(\d+)_pat_hist_type', key)
                    if index_match:
                        index = index_match.group(1)
                        pat_hist_rptd_key = f'Rel_Hist_Table_{index}_pat_hist_rptd'
                        pat_hist_start_key = f'Rel_Hist_Table_{index}_pat_hist_start'
                        pat_hist_stop_key = f'Rel_Hist_Table_{index}_pat_hist_stop'

                        pat_hist_rptd = entry.get(pat_hist_rptd_key, '')
                        pat_hist_start = entry.get(pat_hist_start_key, '')
                        pat_hist_stop = entry.get(pat_hist_stop_key, '')

                        key = pat_hist_rptd +split_flag + pat_hist_start +split_flag + pat_hist_stop
                        if key in past_medication_map:
                            medication_info = past_medication_map[key]
                            # 替换病史中的 Rel_Hist_Table_{index}_pat_hist_rptd_lltname
                            hist_rptd_lltname_key = f'Rel_Hist_Table_{index}_pat_hist_rptd_lltname'
                            entry[hist_rptd_lltname_key] = medication_info.get('product_name_lltname', '')
                            # 设置 Rel_Hist_Table_{index}_pat_ind_rptd 和 Rel_Hist_Table_{index}_pat_ind_rptd_lltname
                            btn_add_ind = medication_info.get('btnAddInd', [])
                            if btn_add_ind:
                                first_ind = btn_add_ind[0]
                                ind_rptd_key = f'Rel_Hist_Table_{index}_pat_ind_rptd'
                                ind_rptd_lltname_key = f'Rel_Hist_Table_{index}_pat_ind_rptd_lltname'
                                entry[ind_rptd_key] = first_ind.get('Ind_Table_0_ind_reptd', '')
                                entry[ind_rptd_lltname_key] = first_ind.get('Ind_Table_0_ind_reptd_lltname', '')
                            break

        return final_structured_data