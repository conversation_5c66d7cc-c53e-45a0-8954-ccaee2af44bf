import os
from enum import Enum, auto
from typing import Dict, Optional, Type, Union, Any, List

from src.common.logger import logger
from src.services.chat.aws_claude_base import claude_client
from src.services.chat.doubao_client import AsyncHTTPClient as DoubaoReasonClient
from src.services.chat.vision_llm_client import AsyncHT<PERSON>Client as DoubaoNormalClient
from src.services.tenant_config import TenantFeatureService
from src.common.context import TraceContext
from src.services.model_config import ModelConfigService


class ModelType(Enum):
    """模型类型枚举"""
    CLAUDE = auto()  # AWS Claude
    DOUBAO_REASON = auto()  # 豆包推理模型
    DOUBAO_NORMAL = auto()  # 豆包普通模型


class BusinessType(Enum):
    """业务类型枚举"""
    VISION = auto()  # 普通识别
    RECOGNIZE = auto()  # 识别
    EXTRACT_NORMAL = auto()  # 抽取
    REASON = auto()
    NARRATIVE = auto()  # 叙述
    STRUCTURED = auto()
    TRANSFORMER = auto()


class ModelConfigKeys:
    """模型配置键名常量"""
    FEATURE_KEY = "model_config"  # 配置键名


class ModelFactory:
    """模型工厂类，根据租户配置获取适合的模型客户端"""

    # 模型类型到客户端创建函数的映射
    _MODEL_CREATORS = {
        ModelType.CLAUDE: lambda tenant_id: claude_client,
        ModelType.DOUBAO_REASON: lambda tenant_id: DoubaoReasonClient(tenant_id),
        ModelType.DOUBAO_NORMAL: lambda tenant_id: DoubaoNormalClient(tenant_id),
    }

    # 模型实例缓存
    _model_instances: Dict[str, Any] = {}
    # 租户模型实例缓存
    _tenant_model_instances: Dict[str, Dict[str, Any]] = {}

    @classmethod
    def get_client(cls, business_type: BusinessType, tenant_id: Optional[str] = None) -> Any:
        """
        获取模型客户端
        
        Args:
            business_type: 业务类型
            tenant_id: 租户ID，如果为None则尝试从上下文获取，如果上下文也没有则使用默认配置
            
        Returns:
            模型客户端实例
        """
        # 如果未提供租户ID，尝试从上下文获取
        if tenant_id is None:
            tenant_id = TraceContext.get_tenant_id()
        
        # 如果仍然没有租户ID，使用系统租户
        if tenant_id is None:
            tenant_id = "system"

        # 获取模型类型
        model_type = cls._get_model_info(business_type, tenant_id)
        cache_key = f"{business_type.name}_{model_type.name}"
        
        # 从缓存获取或创建模型实例
        if tenant_id not in cls._tenant_model_instances:
            cls._tenant_model_instances[tenant_id] = {}
            
        # 从租户缓存获取或创建模型实例
        if cache_key not in cls._tenant_model_instances[tenant_id]:
            cls._tenant_model_instances[tenant_id][cache_key] = cls._create_model_instance(model_type, tenant_id)
            
        # 获取模型实例
        return cls._tenant_model_instances[tenant_id][cache_key]
    
    @classmethod
    def _get_model_info(cls, business_type: BusinessType, tenant_id: str) -> ModelType:
        """
        根据业务类型和租户ID获取模型类型
        
        Args:
            business_type: 业务类型
            tenant_id: 租户ID
            
        Returns:
            模型类型
        """
        # 将BusinessType转换为字符串
        business_type_str = business_type.name
        business_key = business_type.name.lower()
        
        # 获取租户的模型配置映射
        tenant_config = TenantFeatureService.get_feature_config(tenant_id, ModelConfigKeys.FEATURE_KEY)
        
        # 获取系统租户的配置（用于回退）
        system_config = None
        if tenant_id != "system":
            system_config = TenantFeatureService.get_feature_config("system", ModelConfigKeys.FEATURE_KEY)
        
        # 如果两者都没有配置，抛出异常
        if not tenant_config and not system_config:
            raise ValueError(f"未找到租户 {tenant_id} 和系统租户的模型配置")
        
        # 从租户配置中获取业务类型对应的模型ID
        model_id = None
        if tenant_config and business_key in tenant_config:
            model_id = tenant_config[business_key]
        # 如果租户配置中没有该业务类型，尝试从系统配置中获取
        elif system_config and business_key in system_config:
            model_id = system_config[business_key]
        else:
            # 如果两者都没有该业务类型，抛出异常
            raise ValueError(f"业务类型 {business_key} 在租户 {tenant_id} 和系统租户的配置中均不存在")
        
        # 根据模型ID获取模型配置
        model_config = ModelConfigService.get_model_config(model_id)
        if not model_config:
            raise ValueError(f"模型ID '{model_id}' 不存在或未启用")
        
        # 从模型配置中获取模型类型
        model_type_str = model_config.get('model_type')
        if not model_type_str:
            raise ValueError(f"模型ID '{model_id}' 的配置中缺少model_type字段")
        
        # 尝试将字符串转换为ModelType枚举
        try:
            model_type = ModelType[model_type_str]
            return model_type
        except (KeyError, ValueError):
            raise ValueError(f"模型类型 '{model_type_str}' 无效")
    
    @classmethod
    def _create_model_instance(cls, model_type: ModelType, tenant_id: Optional[str] = None) -> Any:
        """
        创建模型实例
        
        Args:
            model_type: 模型类型
            tenant_id: 租户ID
            
        Returns:
            模型客户端实例
        """
        if model_type in cls._MODEL_CREATORS:
            return cls._MODEL_CREATORS[model_type](tenant_id)
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
            
    @classmethod
    def get_available_models(cls) -> Dict[str, List[str]]:
        """
        获取所有可用的模型类型，用于前端展示
        
        Returns:
            Dict[业务类型名称, 可用模型ID列表]
        """
        result = {}
        for business_type in BusinessType:
            result[business_type.name] = [model_type.name for model_type in ModelType]
        return result 
        
    @classmethod
    def clear_cache(cls):
        """清除模型实例缓存"""
        cls._model_instances.clear()
        cls._tenant_model_instances.clear()
        # 同时清除ModelConfigService的缓存
        ModelConfigService.clear_cache()
        logger.info("已清除模型工厂缓存") 