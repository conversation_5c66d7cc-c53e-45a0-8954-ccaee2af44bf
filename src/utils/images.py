import base64
from mimetypes import guess_type


def image_to_base64(image_path):
    with open(image_path, "rb") as image_file:
        base64_encoded_data = base64.b64encode(image_file.read()).decode('utf-8')
    return base64_encoded_data


def openai_image_base64_list(image_path_list):
    """
    接收一个图片路径列表，但只处理最后一张图片，
    并返回符合OpenAI格式的列表。
    """
    # 检查列表是否为空，避免错误
    if not image_path_list:
        return []

    # 直接获取最后一张图片的路径
    last_image_path = image_path_list[-1]

    # 获取图片的MIME类型
    mime_type, _ = guess_type(last_image_path)
    if mime_type is None:
        mime_type = 'application/octet-stream'  # 默认类型

    # 将图片转换为Base64编码
    base64_encoded_data = image_to_base64(last_image_path)

    # 构建符合OpenAI要求的字典结构
    image_url_dict = {
        "type": "image_url",
        "image_url": {
            "url": f"data:{mime_type};base64,{base64_encoded_data}",
            "detail": "high"
        }
    }

    # 将这一个字典放入列表中并返回
    return [image_url_dict]


def claude_image_base64_list(image_path_list):
    base64_data_list = []
    for image_path in image_path_list:
        base64_encoded_data = image_to_base64(image_path)
        base64_data_list.append(base64_encoded_data)
    image_data_list = []
    for base64_data in base64_data_list:
        image_data_dict = {
            "type": "image",
            "source": {
                "type": "base64",
                "media_type": "image/png",
                "data": base64_data
            }
        }
        image_data_list.append(image_data_dict)
    return image_data_list
