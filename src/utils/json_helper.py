import ast
import json
import re
from enum import Enum
from typing import Optional

from src.common.logger import logger


class ServiceType(Enum):
    """服务类型枚举"""
    NARRATIVE = "narrative"
    EXTRACTION = "extraction"
    STRUCTURE = "structure"
    TRANSFORMER = "transformer"


class JSONHelper:
    # 不同服务类型对应的key映射
    KEY_MAPPING = {
        ServiceType.NARRATIVE: "正文",
        ServiceType.EXTRACTION: "SAE/ECI/AESI的描述",
        ServiceType.STRUCTURE: "SAE/ECI/AESI的描述",
        ServiceType.TRANSFORMER: "SAE/ECI/AESI的描述"
    }

    @classmethod
    def narrative(cls, json_str: str, trace_id: str = None, md5: str = None, strict: bool = False) -> dict:
        """Narrative服务的JSON解析工厂方法"""
        return cls.safe_loads(json_str, ServiceType.NARRATIVE, trace_id, md5, strict)

    @classmethod
    def extraction(cls, json_str: str, trace_id: str = None, md5: str = None, strict: bool = False) -> dict:
        """Extraction服务的JSON解析工厂方法"""
        return cls.safe_loads(json_str, ServiceType.EXTRACTION, trace_id, md5, strict)

    @classmethod
    def structure(cls, json_str: str, trace_id: str = None, md5: str = None, strict: bool = False) -> dict:
        """Extraction服务的JSON解析工厂方法"""
        return cls.safe_loads(json_str, ServiceType.STRUCTURE, trace_id, md5, strict)

    @classmethod
    def transformer(cls, json_str: str, trace_id: str = None, md5: str = None, strict: bool = False) -> dict:
        """Extraction服务的JSON解析工厂方法"""
        return cls.safe_loads(json_str, ServiceType.TRANSFORMER, trace_id, md5, strict)

    @staticmethod
    def safe_loads(json_str: str, service_type: Optional[ServiceType] = None, trace_id: str = None, 
                  md5: str = None, strict: bool = False) -> dict:
        """
        安全地解析JSON字符串，处理特定字段中的嵌套引号，包含清理和错误处理功能

        Args:
            json_str: 要解析的JSON字符串
            service_type: 服务类型，用于确定处理的key名称，为None时不进行特殊处理
            trace_id: 追踪ID，用于日志记录
            md5: MD5值，用于日志记录
            strict: 是否使用严格模式解析，默认False

        Returns:
            解析后的字典，如果解析失败则返回空字典
        """
        if not isinstance(json_str, str):
            return {}

        try:
            # 首先尝试直接解析
            return json.loads(json_str, strict=strict)
        except json.JSONDecodeError:
            try:
                # 预处理JSON字符串
                cleaned_json = json_str.strip()
                # 替换字符串值中的控制字符，例如回车符
                cleaned_json = cleaned_json.replace('\r', '')

                if service_type is not None:
                    # 处理特定字段中的嵌套引号
                    def process_specific_key(text: str, key_name: str) -> str:
                        # 构建正则表达式，匹配指定的键及其值
                        pattern = rf'("{re.escape(key_name)}"\s*:\s*")(.*?)("(?=[,\n\r]*[\}}]))'

                        def replace_quotes(match):
                            start = match.group(1)
                            content = match.group(2)
                            end = match.group(3)
                            # 转义值中的双引号和反斜杠
                            escaped_content = content.replace('\\', '\\\\').replace('"', '\\"')
                            # 替换换行符为 \\n
                            escaped_content = escaped_content.replace('\n', '\\n')
                            return f'{start}{escaped_content}{end}'

                        return re.sub(pattern, replace_quotes, text, flags=re.DOTALL)

                    # 处理模型返回的单引号json数据
                    if service_type == ServiceType.STRUCTURE or service_type == ServiceType.TRANSFORMER:
                        # 将单引号字符串转换为 Python 字典
                        cleaned_json = ast.literal_eval(cleaned_json)
                        # 将 Python 字典转换为标准 JSON 字符串
                        cleaned_json = json.dumps(cleaned_json, ensure_ascii=False)

                    # 根据服务类型获取对应的key并处理
                    key_to_process = JSONHelper.KEY_MAPPING.get(service_type)
                    if key_to_process:
                        cleaned_json = process_specific_key(cleaned_json, key_to_process)

                # 尝试解析处理后的JSON
                return json.loads(cleaned_json, strict=strict)

            except (json.JSONDecodeError, Exception) as e:
                if trace_id and md5:
                    logger.warning("{md5} | {id} | JSON解析失败: {message}",
                                 md5=md5, id=trace_id,
                                 message=f"{str(e)}. 原始数据: {json_str}")
                return {}