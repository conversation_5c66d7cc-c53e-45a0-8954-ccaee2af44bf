```moduleRule
Apply the following custom conversion logic when extracting data. The "[e.g., .......]" is an example of generating content from unstructured description,  please refer to the extraction logic for generation.

- **"TXT_Pat_Race_Table_0_ethnicity_id": "种族信息，包含以下五个选项，东亚人种（黄种人），澳洲人种（棕种人），尼格罗人种（黑种人），高加索人种（白种人）"*
    - 如果原始数据种族的取值在选项中存在，则转换为对应的字典，否则留空，不要进行推测和衍生
    -  [e.g., '种族: 汉族' The value "TXT_Pat_Race_Table_0_ethnicity_id" is "".]
    -  [e.g., '种族: 黄种人' The value "TXT_Pat_Race_Table_0_ethnicity_id" is "东亚人种（黄种人）".]
```