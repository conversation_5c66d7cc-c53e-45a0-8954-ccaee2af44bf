```moduleRule
Apply the following custom conversion logic when extracting data. The "[e.g., .......]" is an example of generating content from unstructured description,  please refer to the extraction logic for generation.

- **"TXT_Rel_Hist_Table_{index}_pat_hist_type": "病史类型，从当前**报告模块**字段取值,如果是既往病史请转成既往疾病,如果是‘既往用药信息’,则填入‘既往用药’"**:
    - Locate all pieces of data related to Concomitant Disease/Historical Condition in both the event description's unstructured data and the relevant medical history related to the event's structured data.
	- [e.g., 'The patient has a history of anemia diagnosed as CTCAE Grade 2: Started in 2023, UK, and continues.' The value "TXT_Rel_Hist_Table_{index}_pat_hist_type" is "合并疾病".] 
- **"Rel_Hist_Table_{index}_pat_hist_rptd": "疾病名称，{index}代表当前记录的索引，从0开始，如果疾病名称存在空格则需要移除，请不要修改当前字段的名称。如果病史类型为‘既往用药信息’，那么这个值不要进行转换，放入‘疾病名称’字段的值即可。"**
    - Locate all pieces of data related to Concomitant Disease/Historical Condition in both the event description's unstructured data and the relevant medical history related to the event's structured data.
	- [e.g., 'The patient has a history of anemia diagnosed as CTCAE Grade 2: Started in 2023, UK, and continues.' The value "Rel_Hist_Table_{index}_pat_hist_rptd" is "anemia".] 
- **"Rel_Hist_Table_{index}_pat_hist_start": "开始日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空，{index}代表当前记录的索引，从0开始"**
    - Locate all pieces of data related to Concomitant Disease/Historical Condition in both the event description's unstructured data and the relevant medical history related to the event's structured data.
	- [e.g., 'The patient has a history of anemia diagnosed as CTCAE Grade 2: Started in 2023, UK, and continues.' The value "Rel_Hist_Table_{index}_pat_hist_start" is "2023-??-??".] 
- **"Rel_Hist_Table_{index}_pat_hist_stop": "结束日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空，{index}代表当前记录的索引，从0开始"**
    - Locate all pieces of data related to Concomitant Disease/Historical Condition in both the event description's unstructured data and the relevant medical history related to the event's structured data.
	- [e.g., 'The patient has a history of anemia diagnosed as CTCAE Grade 2: Started in 2023, UK, and continues.' The value "Rel_Hist_Table_{index}_pat_hist_stop" is "".] 
- **"Rel_Hist_Table_{index}_pat_hist_cont": "是否持续，是为1，否为0，未知为2，空为-1，当是否持续为否的时候才能填写结束日期，{index}代表当前记录的索引，从0开始"**
    - Locate all pieces of data related to Concomitant Disease/Historical Condition in both the event description's unstructured data and the relevant medical history related to the event's structured data.
	- [e.g., 'The patient has a history of anemia diagnosed as CTCAE Grade 2: Started in 2023, UK, and continues.' The value "Rel_Hist_Table_{index}_pat_hist_cont" is "1".] 
```