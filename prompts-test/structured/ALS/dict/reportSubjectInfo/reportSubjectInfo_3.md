```moduleRule
Apply the following custom conversion logic when extracting data. The "[e.g., .......]" is an example of generating content from unstructured description,  please refer to the extraction logic for generation.

- **"labtestreptd": "Laboratory/Diagnostic test's item name, this value is included in section 'Laboratory/Diagnostic Test' and section 'Description of SAE/AESI'."**:
    - Locate all pieces of data related to laboratory/diagnostic tests in both the event description's unstructured data and the laboratory test's structured data.
	- [e.g., 'Left pleural effusion was detected as non target lesion based on screening MRI scan dated 15 Jan 2020. Thickness was 3cm.' The value "labtestreptd" is MRI.] 
- **"labtest": "Laboratory/Diagnostic test's item name, this value is included in section 'Laboratory/Diagnostic Test' and section 'Description of SAE/AESI'."**:
    - Locate all pieces of data related to laboratory/diagnostic tests in both the event description's unstructured data and the laboratory test's structured data.
	- [e.g., 'Left pleural effusion was detected as non target lesion based on screening MRI scan dated 15 Jan 2020. Thickness was 3cm.' The value "labtest" is MRI..] 
- **"examineDate": "Laboratory/Diagnostic test's date, this value is included in section 'Laboratory/Diagnostic Test' and section 'Description of SAE/AESI'."**:
    - [e.g., 'Left pleural effusion was detected as non target lesion based on screening MRI scan dated 15 Jan 2020. Thickness was 3cm.' The value "examineDate" is 15 Jan 2020.] 
- **"labresult": "Laboratory/Diagnostic test's result and unit, this value is included in section 'Laboratory/Diagnostic Test' and section 'Description of SAE/AESI'."**:
    - Locate all pieces of data related to laboratory/diagnostic tests in both the event description's unstructured data and the laboratory test's structured data.
	- [e.g., 'Left pleural effusion was detected as non target lesion based on screening MRI scan dated 15 Jan 2020. Thickness was 3cm.' The value "labresult" is 3cm.] 
```