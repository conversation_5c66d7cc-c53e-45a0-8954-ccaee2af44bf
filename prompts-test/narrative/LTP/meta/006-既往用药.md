**不良反应事件报告内容：**
```
{PDF报告识别的结果}
```

我是临床试验研究员，负责记录和总结临床试验受试者的不良事件（AE）、严重不良事件（SAE）、临床关注事件（ECI）和特别关注不良事件（AESI）以及既往用药史。

现在需要将此事件报告概括为临床个例报告描述（Clinical ICSRs），请遵循以下规则：

---

#### 基本规范 ####

1. **输出必须严格按照提供的模板格式生成，不可增加或改变结构**
2. 输出为JSON格式：
   ```json
   {
     "模块": "既往用药",
     "正文": "<既往用药合并文本>"
   }
   ```

---

#### 数据提取规则 ####

- 从报告中"报告模块": "既往用药信息"的部分提取数据
- 提取每条记录的"既往用药品名称"、"既往用药适应症"、"既往用药剂量"、"既往用药频率"、"既往用药给药途径"、"既往用药开始日期"、"既往用药结束日期"和"既往用药是否继续用药"字段
- 如果没有找到任何用药记录，则使用"受试者无既往用药"模板

---

#### 日期处理规则 ####

**1. 日期标准格式**
- 所有日期输出格式为："YYYY年MM月DD日"
- 月份和日期如为个位数必须补零，如"01"而非"1"
- 示例：2024-7-5 应转换为 2024年07月05日

**2. 部分日期处理**
- 当日期中包含"?"或日期不完整时：
    - 只保留已知部分，缺失的部分用"未知日期"替代
    - 示例："2025-??-??" 转换为 "2025年未知日期"
    - 示例："2025-02-??" 转换为 "2025年02月未知日期"

**3. 时间描述生成**
根据可用的日期信息，生成以下格式的时间描述：
- **单一日期**：开始和结束日期相同且都已知
  格式：`YYYY年MM月DD日`
- **固定区间**：开始和结束日期都已知且不同
  格式：`YYYY年MM月DD日至YYYY年MM月DD日`
- **持续事件(开始日期已知)**：有开始日期，无结束日期，"是否继续"为"是"或"继续"
  格式：`自YYYY年MM月DD日起`
- **持续事件(开始日期未知)**：开始日期为空或包含"?"，无结束日期或结束日期为空，且"是否继续"为"是"或"继续"
  格式：`自未知日期起`
  示例：当开始日期为空白、"??"或部分缺失，且是继续时，使用"自未知日期起"
- **未知开始有结束**：开始日期未知但有结束日期
  格式：`至YYYY年MM月DD日`
- **完全未知且非持续**：开始和结束日期均未知，且"是否继续"不为"是"或"继续"
  格式：`时间未报告`

---

#### 用药格式规则 ####

**1. 基本格式**

单个药物记录格式：
```
受试者的既往用药包括：<药物名称>（<剂量>，<给药途径>，<频率>，<用药时间>）用于<适应症>；
```

**2. 相同药品名称和适应症但不同时间段的合并**

当有相同药品名称和适应症但在不同时间段或使用不同频率/给药途径时，应合并为一条记录：
```
<药物名称>（<剂量1>，<给药途径1>，<频率1>，<用药时间1>；<剂量2>，<给药途径2>，<频率2>，<用药时间2>）用于<适应症>；
```

示例：
```
依诺肝素钠注射液（0.6mL，ih，st，2025年04月07日；0.6mL，ih，qd，自2025年04月08日起）用于肺动脉栓塞、左下肢静脉血栓；
```

**3. 相同开始日期、适应症的不同药物合并**
```
<药物名称1>（<剂量1>，<给药途径1>，<频率1>，<用药时间1>）和<药物名称2>（<剂量2>，<给药途径2>，<频率2>，<用药时间2>）均用于<适应症>；
```

```
<药物名称1>（<剂量1>，<给药途径1>，<频率1>，<用药时间1>），<药物名称2>（<剂量2>，<给药途径2>，<频率2>，<用药时间2>）和<药物名称3>（<剂量3>，<给药途径3>，<频率3>，<用药时间3>）均用于<适应症>；
```

**4. 特殊规则**

- **药品名称和适应症匹配**：先按药品名称和适应症分组，相同的合并处理
- **缺失字段处理**：如果某字段缺失，将其标记为"未报告"
- **英文缩写**：频率和给药途径如为英文缩写，统一用小写表示（如"qd"而非"QD"）
- **无既往用药**：如果没有任何用药记录，使用"受试者无既往用药"或"受试者的既往用药未报告"

---

#### 处理流程 ####

1. **数据提取与预处理**：
    - 提取所有"既往用药信息"相关数据
    - 应用日期处理规则，生成标准时间描述
    - 将频率和给药途径的英文缩写转为小写

2. **药物分组**：
    - 首先按药品名称和适应症分组，识别相同药品和适应症但不同时间段的记录
    - 其次识别用于相同开始日期、适应症的不同药物，将它们合并到一条描述中

3. **格式化输出**：
    - 对于相同药品名称和适应症的多个时间段记录，在括号内用分号分隔不同时间段的用药信息
    - 按照基本格式为每组药物生成记录
    - 确保每个药物记录只包含存在的字段
    - 用分号分隔不同药物组记录

4. **最终输出**：按指定JSON格式输出结果