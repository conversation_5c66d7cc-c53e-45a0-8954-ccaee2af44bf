**不良反应事件报告内容：**
```
{PDF报告识别的结果}
```

我是临床试验研究员，负责记录和总结临床试验受试者的不良事件（AE）、严重不良事件（SAE）、临床关注事件（ECI）和特别关注不良事件（AESI）以及治疗用药史。

现在需要将此事件报告概括为临床个例报告描述（Clinical ICSRs），请严格遵循以下规则：

---

#### 基本规范 ####  
1. **输出必须严格按照提供的模板格式生成，不可增加或改变结构**
2. 输出为 JSON，结构固定如下：
```json
{
	"模块": "治疗用药",
	"正文": "<治疗用药合并文本>"
}
```

---

#### 数据提取规则 ####  
• 仅从 `"报告模块": "治疗用药"` 的记录中提取  
• 提取字段：
- 药物名称
- 适应症
- 剂量
- 剂量单位
- 频率
- 给药途径
- 开始日期
- 结束日期
- 是否继续

若完全未找到用药记录，则输出 `该事件无相关治疗信息。`  
若存在治疗用药模块但关键信息均缺失无法判断，则输出 `研究者未报告相关治疗信息。`

---

#### 日期处理规则 ####  

1. **日期标准化**
	- 输出格式统一为 `YYYY年MM月DD日`
	- 月、日为个位时补零（例：2024-7-5 → 2024年07月05日）

2. **部分日期**
	- `YYYY-??-??` → `YYYY年，具体开始日期不详`
	- `YYYY-MM-??` → `YYYY年MM月，具体开始日期不详`
	- 年份含 “?” 视为未知

**3. 时间描述生成**
根据可用的日期信息，生成以下格式的时间描述：
- **单一日期**：开始和结束日期相同且都已知
  格式：`YYYY年MM月DD日`
- **固定区间**：开始和结束日期都已知且不同
  格式：`开始日期至结束日期`
- **持续事件**：有开始日期，无结束日期，"是否继续"为"是"
  格式：`自开始日期起持续`
- **未知开始有结束**：开始日期未知但有结束日期
  格式：`未知日期至结束日期，具体开始日期不详`
- **完全未知但持续**：开始和结束日期均未知，但"是否继续"为"是"
  格式：`自未知日期起持续，具体开始日期不详`
- **完全未知不持续**：所有时间信息均未知且"是否继续"不是"是"
  格式：`时间未报告`

---

#### 用药格式规则 ####  

1. **单条药物描述**
```
<用药时间>，给予受试者<药物名称>（<剂量><剂量单位>，<频率>，<给药途径>）用于治疗<适应症>
```

2. **日期合并规则**
	- **同一时间描述** 下的药物必须合并为一条记录
	- 根据适应症再次合并药物名称：  
	  • 两个药物 → 用 “和” 连接，末尾写一次 “均用于治疗<适应症>”  
	  • 三个及以上药物 → 先用 “、” 分隔，最后两药物用 “和” 连接，末尾加 “均用于治疗<适应症>”

3. **字段缺失处理**
	- 无适应症：省略 “用于治疗<适应症>”
	- 无剂量/频率/给药途径：缺什么就省略什么及其前后逗号

4. **中英文统一**
	- 频率及给药途径缩写一律小写（如 qd、bid、po）
	- 频率 `日一次` 统一转换为 `每日1次`

5. **整体模板**
	- 有记录：
	  ```
      该事件的治疗信息包括：<记录1>。<记录2>。…<最后一条记录>。
      ```
	- 无记录：`该事件无相关治疗信息。`
	- 信息不明确：`未报告相关的治疗信息。`

6. **标点要求**
	- 不同时间描述之间使用中文句号 “。”
	- 句内项目用中文逗号 “，”，同一适应症下多药品按照合并规则用 “、” 和 “和” 连接
	- 禁用分号 “；”

---

#### 处理流程 ####  

1. **提取与预处理**
	- 提取字段并标准化日期
	- 将频率 `日一次` → `每日1次`
	- 所有英文缩写小写化

2. **分组合并**
	- 按时间描述分组
	- 同组内按适应症合并药物名称

3. **格式化**
	- 按上述格式规则生成文本
	- 多条时间描述用 “。” 分隔并以 “。” 结束

4. **输出**
	- 按“基本规范”中的 JSON 模板输出结果