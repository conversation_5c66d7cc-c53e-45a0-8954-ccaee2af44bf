不良反应事件报告内容：
"""
{PDF报告识别的结果}
"""
我是一个临床试验的研究员，日常工作是记录和总结临床试验中受试者发生的不良事件（AE）、严重不良事件（SAE）、临床关注事件（ECI）和特别关注不良事件（AESI），包括但不限于受试者的病史、合并用药、研究药物使用情况、不良事件的临床过程描述、接受的治疗以及事件的转归。
现在需要将本次事件报告概括为完整的临床个例报告描述（Clinical ICSRs），每个模块都需要体现在描述中，要求：
1、输出结果中的“正文”必须严格按照“模板格式”生成，不能增加“模板格式”中不存在的内容；
2、模板格式中的“<>”代表是一个参数，需要在事件报告中提取相应的值进行替换；
3、模板格式中的“[例如：.......]”是对生产内容的举例说明，请参考其中的示例样式进行生成，输出正文时需删除；
4、模板格式中的“如果.......”是条件判断，当满足条件时，才能参考其中的模板格式进行生成；
5、模板格式中的“（说明：.......）”是说明性文字，对前面内容的说明和补充，输出正文时需删除；
6、输出结果的日期中的月份和日，如月份和日都是个位数，须在数字前面加“0”，如“2024年07月05日”；
7、如模板格式中“<>”代表的参数在报告内容中未提及，则该参数置空；
8、模板格式中的“时间：YYYY年MM月DD日至YYYY年MM月DD日”，当2个“YYYY年MM月DD日”一致时，模板格式只需要输出“时间：YYYY年MM月DD日”;
9、模板格式中的“时间：YYYY年MM月DD日至YYYY年MM月DD日”，当“是否继续”为“继续”时，输出“时间：YYYY年MM月DD日起持续”;
10、如模板格式中的“YYYY年MM月DD日”日期格式不全，只需要输出存在明确数字的部分，如“YYYY年MM月”或“YYYY年”；
11、“正文”需按照“开始日期”分组，并按“开始日期”升序排序；
12、报告中的“不良事件（AE）”，每一个json格式的“不良事件（AE）”生成一条“正文”内容；
13、输出结果为json格式，要求输出：
	{
		"试验方案编号":"<试验方案编号>", 
		"模块":"不良事件（AE）", 
		"正文":["<正文>"......]（请不要输出“模板格式”以外的内容）
	}；

#### 模板格式 #### 
此病例被评定为严重病例，因"<SAE报告术语>"<严重性标准>。

[例如：此病例被评定为严重病例，因"血小板计数降低"导致住院。]