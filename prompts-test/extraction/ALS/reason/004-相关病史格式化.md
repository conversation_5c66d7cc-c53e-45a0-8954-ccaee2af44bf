请根据以下要求，对提供的数据进行格式化和优化，并输出为标准的 **Markdown 表格** 格式：

---

### **数据处理步骤：**

#### **一、数据格式识别**

首先判断输入的数据类型：

- 如果是 **HTML 表格数据**，请按照 **步骤二** 进行处理。
- 如果是 **大段文本数据**，请按照 **步骤三** 进行处理。

---

#### **二、HTML 表格数据处理**

1. **删除无关数据**：移除页眉、页脚、页码等与表格内容无关的数据，确保表格内容纯净。
2. **合并被截断的单元格内容**：由于跨页或其他原因导致的单元格内容被分割，需要将其合并，确保数据的完整性。
3. **处理合并单元格**：针对原始表格中存在的合并单元格，在转换为 Markdown 表格时进行适当处理，避免因 Markdown 不支持合并单元格而导致渲染问题。
4. **替换字典列内容**：对于表头中标识为“字典列”的列，将表格中该列的数值替换为对应的字典文本。
5. **修复表格结构**：修复不完整的表格行和列，确保转换后的 Markdown 表格能够正常渲染。
6. **合并多张表格**：将所有表格合并为一个整体，以首次出现的表头为准，忽略后续重复的表头。
7. **添加序号列**：在合并后的表格中增加一列“序号”，显示每条记录的顺序编号。

---

#### **三、大段文本数据处理**

1. **提取指定字段信息**：从文本中提取以下字段的数据：
    - **疾病名称**（仅提取现病史中的疾病名称，如果记录中存在药品名称、适应症名称等其它名称字段，则说明此记录不是现病史的数据）
    - **开始日期**
    - **结束日期**
    - **是否继续**

2. **整理成表格**：将提取和处理后的数据，按照上述字段顺序整理成标准的 **Markdown 表格**，其中表头即为上述字段名称。

3. **数据清洗和校验**：对提取的结果进行校对，确保数据准确、完整。如有缺失的数据，表格中对应单元格保持为空。

---

#### **四、输出格式**

请根据以上要求，对提供的数据进行处理，并输出为标准的 **Markdown 表格** 格式，示例如下：

| 序号 | 疾病名称  | 开始日期    | 结束日期    | 是否继续 |
| ---- | --------- | ----------- | ----------- | -------- |
| 1    | XXX       | YYYY.MM.DD  | YYYY.MM.DD  | 是/否    |
| ...  | ...       | ...         | ...         | ...      |

---

请对以下数据进行处理，并输出为标准的 **Markdown 表格** 格式：

```html
{{tableData}}
```