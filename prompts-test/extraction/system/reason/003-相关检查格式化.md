# 医学检查数据处理与格式化优化提示词

请根据以下要求，对提供的医学检查数据进行格式化和优化，并输出为标准的 **Markdown 表格** 格式：

---

### **数据处理步骤：**

#### **一、数据格式识别与检查项目筛选**

首先判断输入的数据类型：

- 如果是 **HTML 表格数据**，请按照 **步骤二** 进行处理。
- 如果是 **大段文本数据**，请按照 **步骤三** 进行处理。
- 如果同时包含以上两种数据类型，请分别进行处理后合并结果。

**重要说明**：
1. **仅身高、体重不需要提取，其他所有医学检查和评估都需要提取。**
2. 需要提取的医学检查和评估包括但不限于：
    - 实验室检查（血液、尿液、生化、免疫学、微生物学等检查）
    - 生命体征监测（血压、脉搏、呼吸、体温等）
    - 临床评分和评估（如ECOG评分、Karnofsky评分、疼痛评分等）
    - 仪器检查（心电图、超声、CT、核磁共振等）
    - 功能测试（肺功能、心功能等）

---

#### **二、HTML 表格数据处理**

1. **删除无关数据**：移除页眉、页脚、页码等与表格内容无关的数据，确保表格内容纯净。
2. **合并被截断的单元格内容**：由于跨页或其他原因导致的单元格内容被分割，需要将其合并，确保数据的完整性。
3. **处理合并单元格**：针对原始表格中存在的合并单元格，在转换为 Markdown 表格时进行适当处理，避免因 Markdown 不支持合并单元格而导致渲染问题。
4. **替换字典列内容**：对于表头中标识为"字典列"的列，将表格中该列的数值替换为对应的字典文本。
5. **修复表格结构**：修复不完整的表格行和列，确保转换后的 Markdown 表格能够正常渲染。
6. **合并多张表格**：将所有表格合并为一个整体，以首次出现的表头为准，忽略后续重复的表头。
7. **保留检查时间信息**：如果表格中包含检查时间信息，应将其提取并保留在"检查时间"列中显示。

---

#### **三、大段文本数据处理**

1. **体格检查和专科检查的完整提取**：

   **重要规则**: 当文本中出现"体格检查示："或类似表述时，应将其识别为一个完整的检查项目，检查项目名称为"体格检查"，检查结果为该表述后的整段文字直到下一个明确的标题或分隔符。

    - **不要**将体格检查内容拆分为多个独立的检查项目（如眼底检查、光反射检查等）
    - **保持**体格检查描述的完整性，包括所有临床发现
    - 类似地，对于其他专科检查（如"眼科检查示："、"神经系统检查示："等），也应作为一个整体提取

2. **提取指定字段信息**：从文本中提取以下字段的数据：
    - **检查日期**
    - **检查时间**（记录具体的时分秒，如"08:30:00"、"14:45:37"等）
    - **检查项目名称**（注意：**仅身高、体重不需要提取，其他所有医学检查和评估包括ECOG等临床评分都需要提取。严格按原文保留检查项目的原始名称，不进行任何转换，例如"T"、"P"、"BP"、"R"等英文缩写必须保持原样，不得翻译或扩展为中文全称**）
    - **检查结果**（如果是定量检查结果，则只保留数值）
    - **检查结果限定符**（包含以下 7 个选项：>、<、=、>=、<=、≥、≤，当检查结果为定量数值时需要转换）
    - **检查结果单位**（如：mg、+、-、ng/ml、mg/L、分、级、度等）
    - **正常值范围**（**仅提取原文中明确给出的正常值范围，不要补充**）
    - **正常值上限**（仅当原文中明确给出正常值范围时才提取）
    - **正常值下限**（仅当原文中明确给出正常值范围时才提取）
    - **结果是否正常**（**仅当原文中明确给出正常值范围时才判断，否则置空**）

3. **处理和转换数据**：
    - **定量结果处理**：当检查结果为定量数值时，只保留数值部分，并将限定符和单位分别提取出来。
    - **限定符转换**：如果检查结果中包含限定符（如">"、"<"等），需要将其单独提取到"检查结果限定符"列。
    - **正常值范围处理**：
        - **仅当原文中明确给出正常值范围时，才解析出上限和下限。**
        - **如果原文未提供正常值范围，则"正常值范围"、"正常值上限"和"正常值下限"列保持为空。**
        - **不要根据医学参考值补充未在原文中明确给出的正常值范围。**
    - **结果判定**：
        - **仅当原文中明确给出正常值范围时，才根据检查结果与正常值范围判定"结果是否正常"。**
        - **如果原文未提供正常值范围，则"结果是否正常"列保持为空。**
    - **时间信息提取**：
        - 仔细提取文本中所有检查时间信息，确保不遗漏任何时间点。
        - 特别关注同一检查项目在不同时间点的多次测量，确保所有时间点都被记录。
    - **检查项目名称处理**：
        - **极其重要：必须原样保留原始文本中的检查项目名称，不进行任何自动转换、翻译或扩展**
        - **医学缩写（如T、P、BP、R等）必须保持原始形式，禁止转换为中文全称**
        - **例如，输入中的"T"在输出中必须仍为"T"，不能变为"体温"**

4. **数据复核机制**：
    - 使用关键词搜索方法，确保所有检查数据都被正确提取，尤其注意以下关键词：
        - 测量、监测、检查、体征、结果、评分、评估等词语周围的数据
        - 时间表达式（如"13:19"、"12点11分"等）周围的数据
        - 单位表达式（如"mmHg"、"次/分"、"分"、"级"等）周围的数据
    - **对于重要的生命体征（血压、脉搏、呼吸、体温）和临床评分（如ECOG评分），进行二次检查，确保所有数据都被提取。**
    - 检查是否存在时间点对应关系错误，确保每个检查时间都与正确的检查结果对应。
    - **最终检查：再次确认所有检查项目名称与原文完全一致，未发生任何转换或标准化**

5. **整理成表格**：将提取和处理后的数据，按照指定字段顺序整理成标准的 **Markdown 表格**。对于体格检查等整体性描述，将其完整内容放入"检查结果"列中。

---

#### **四、同一天多个检查时间点数据合并处理**

对于同一检查项目在同一天有多次检查的情况：

1. **识别和分组**：
   - 根据"检查项目名称"、"检查日期"、"检查结果单位"和"正常值范围"对数据进行分组。
   - 对于每个分组，检查是否存在多个检查时间点。

2. **数据合并**：
   - 对于同一检查项目在同一天有多次检查的情况，将其合并为表格中的一行记录。
   - 在"检查时间"列中，将多个检查时间点以换行形式展示，每个时间点占一行。
   - 在"检查结果"列中，将多个检查结果以相同的顺序展示，与"检查时间"列对应。
   - 格式示例（检查时间列）：
      ```
      08:30:00
      14:45:00
      ```
   - 格式示例（检查结果列）：
      ```
      123
      90
      ```

3. **结果判定处理**：
   - 对于"结果是否正常"字段，如果所有时间点结果判定一致，则保留该判定；否则标注为"多次检查结果不一致"。

---

#### **五、添加序号列**

在最终的表格中增加一列"序号"，显示每条记录的顺序编号，从1开始递增。

---

#### **六、输出格式**

请根据以下要求，对提供的数据进行处理，并输出为标准的 **Markdown 表格** 格式，包含以下列：

| 序号 | 检查日期 | 检查时间 | 检查项目名称 | 检查结果 | 检查结果限定符 | 检查结果单位 | 正常值范围 | 正常值上限 | 正常值下限 | 结果是否正常 |
| ---- | -------- | -------- | ------------ | -------- | -------------- | ------------ | ---------- | ---------- | ---------- | ------------ |

示例：

| 序号 | 检查日期    | 检查时间           | 检查项目名称 | 检查结果           | 检查结果限定符 | 检查结果单位 | 正常值范围      | 正常值上限 | 正常值下限 | 结果是否正常 |
| ---- | ----------- | ------------------ | ------------ | ------------------ | -------------- | ------------ | --------------- | ---------- | ---------- | ------------ |
| 1    | 2023.05.23  |                    | 体格检查     | 右眼结膜充血，角膜上皮大片脱落，荧光大片着染... |                |              |                 |            |            |              |
| 2    | 2023.05.23  | 08:30:00<br>14:45:00 | 血糖         | 123<br>90         | >              | mg/L         | 70-110 mg/L     | 110        | 70         | 异常         |
| 3    | 2023.05.23  | 09:47:31           | 白细胞计数   | 6.5               |                | 10^9/L       | 4.0-10.0        | 10.0       | 4.0        | 正常         |
| 4    | 2023.05.23  | 11:15:00           | CT扫描       | 肝内见多发囊性病变 |                |              |                 |            |            | 异常         |
| 5    | 2023.05.24  | 09:00:00           | 血压         | 120/80            |                | mmHg         | 90-120/60-80    | 120/80     | 90/60      | 正常         |

---

请对以下数据进行处理，并输出为标准的 **Markdown 表格** 格式：

```html
{{tableData}}
```