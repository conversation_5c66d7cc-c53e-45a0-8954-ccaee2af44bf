1. **忽略页眉和页脚的内容；页眉和页脚指每页顶部或底部的标准重复文本，如"Confidential and Proprietary"、"科伦博泰项目专用，定稿于..."、"康龙化成"、"PHARMARON"、"SAE/AESI Report Form for Clinical Trial"等。不要忽略任何实验室检查数据，即使它出现在表格外或报告的末尾。**

2. **准确识别报告中的所有文字，理解每段文字的主要内容和上下文，需要提取并输出所有时间点的实验室检查数据，不要遗漏。**

3. **模板格式中的"<>"代表是一个参数，需要在事件报告中提取相应的值进行替换；**

4. **如报告中的字段对应的值没有提取到或没有勾选，则输出结果置空；**

5. **遍历报告中所有的实验室检查数据，包括表格内和表格外的实验室检查数据，每个时间点、每一条记录都需要提取并转换为结构化数据；**

- **遍历报告中所有的实验室检查数据，包括表格内和表格外的实验室检查数据，每一条都需要提取并转换为结构化数据；**
- **对于表格数据，每一个字段可能对应一个或多个值，按照表头和数据排列；**
- **对于表格外的实验室检查数据，识别并提取相应的字段；**
- **包含字段：**
  - **检查日期**
  - **检查时间**
  - **检查项目名称**
  - **检查结果（如果是定量检查结果，则只保留数值）**
  - **检查结果限定符（包含以下7个选项：>、<、=、>=、<=、≥、≤，当检查结果为定量数值时需要转换）**
  - **检查结果单位（如：mg、+、-、ng/ml、mg/L）**
  - **正常值范围**
  - **正常值上限**
  - **正常值下限**
  - **结果是否正常**

6. **按照且仅按照如下json格式输出，不要增加说明性描述：**

   ```json
   [
     {
       "报告分类":"报告",
       "报告模块": "与事件相关的实验室检查",
       "检查日期": "<检查日期，日期字段>",
       "检查时间": "<检查时间，时间字段>",
       "检查项目名称": "<检查项目名称>",
       "检查结果": "<检查结果>",
       "检查结果限定符": "<检查结果限定符，限定符必须在数值前，请用中文输出，未出现限定符的置空>",
       "检查结果单位": "<检查结果单位，单位必须在数值后，未出现单位的置空>",
       "正常值范围": "<正常值范围>",
       "正常值上限": "<正常值上限>",
       "正常值下限": "<正常值下限>",
       "结果是否正常": "<结果是否正常>"
     },
     ...
   ]
   ```

7. **如果实验室检查的结果是定量结果，提取的值必须是报告的检查结果中数值前出现的限定符，未出现限定符的置空，请用如下中文替换 JSON 输出中的"检查结果限定符"：**

- **">"替换为"大于"**
- **"<"替换为"小于"**
- **"="替换为"等于"**
- **">="和"≥"替换为"大于等于"**
- **"<="和"≤"替换为"小于等于"**
- **""替换为""**

8. **其它规则**

**数据合并**

- **对于相同的检查项目名称、检查日期、检查结果单位、正常值范围，如果在同一天存在多个检查时间点，每个时间点需作为单独的记录处理，并在"检查时间"字段中显示具体的检查时间。如果没有检查时间，则该字段留空。**

**结果判断**

- **实验室检查的数据只包含一个检查日期，如果记录中存在两个日期的情况，则说明此记录不是实验室检查的数据，不能将其转为实验室检查的结构化数据。**

**检查时间**

- **检查时间需要输出为 HH:MM:SS 格式，不全的部分可以为空，但不要用占位符。**
- **示例：**
  - **源数据为"08:50:37"，提取结果为"08:50:37"。**
  - **源数据为"09:47"，提取结果为"09:47"。**
  - **源数据为空，提取结果为空。**

**检查结果**

- **实验室检查的结果：**
  - **如果是定量检查结果，则只需要保留数值。**
  - **示例：**
    - **源数据为">5.0"，提取结果为"5.0"。**
    - **源数据为"<3.1"，提取结果为"3.1"。**
    - **源数据为"5.0mg"，提取结果为"5.0"。**

**检查结果限定符**

- **如果实验室检查的结果是定量检查结果，提取的值必须是报告的检查结果中数值前出现的限定符，未出现符号的不能加入等于号。**
- **示例：**
  - **源数据为"4.0+"，提取结果为""。**
  - **源数据为">5.0"，提取结果为"大于"。**
  - **源数据为"<=3.1"，提取结果为"小于等于"。**
  - **源数据为"5.0mg"，提取结果为""。**
  - **源数据为"=5.0"，提取结果为"等于"。**

**检查结果单位**

- **如果实验室检查的结果是定量检查结果，提取的值必须是报告的检查结果中数值后出现的单位，"+"和"-"视为单位，未出现单位的置空。**
- **示例：**
  - **源数据为"4.5+"，提取结果为"+"。**
  - **源数据为">5.0"，提取结果为""。**
  - **源数据为"<=3.1"，提取结果为""。**
  - **源数据为"5.0mg"，提取结果为"mg"。**
  - **源数据为"=5.0 ng/ml"，提取结果为"ng/ml"。**

**日期字段**

- **需要输出为 yyyy-MM-dd 日期格式，年月日如果不全，可以用"??"进行占位，但需要遵循日期精度从日到年，精度至少要到年。**
- **示例：**
  - **源数据为"UK"，提取结果为""。**
  - **源数据为"继续"，提取结果为""。**
  - **源数据为"2018-UNK-UNK"，提取结果为"2018-??-??"。**
  - **源数据为"2018-UNK-12"，提取结果为"2018-??-??"。**
  - **源数据为"2018-09-UK"，提取结果为"2018-09-??"。**
  - **源数据为"2018-09"，提取结果为"2018-09-??"。**
  - **源数据为"UNK-UNK-12"，提取结果为""。**
  - **源数据为"2018/2/1"，提取结果为"2018/02/01"。**
  - **源数据为"\*/\*/_"，提取结果为""。**
  - **源数据为"//____"，提取结果为""。**
  - **源数据为"2025.01.31"，提取结果为"2025-01-31"。**

**文本字段**

- **如果包含空格，则需要移除。**
- **示例：**
  - **源数据为"维生素 C 注射液"，提取结果为"维生素C注射液"。**
  - **源数据为" 维生素A注射液"，提取结果为"维生素A注射液"。**
  - **源数据为"维生素D注射液      "，提取结果为"维生素D注射液"。**
  - **源数据为"维生素F注射液"，提取结果为"维生素F注射液"。**

9. **请确保完整提取并输出报告中所有相关的实验室检查数据，包括所有时间点的记录，不要遗漏任何信息。**

10. **请注意，不要只输出部分数据或示例，需要将所有符合要求的数据全部提取并输出。**