请根据以下要求，对提供的数据进行格式化和优化，并输出为标准的 **Markdown 表格** 格式：

---

### **数据处理步骤：**

#### **一、数据格式识别**

首先判断输入的数据类型：

- 如果是 **HTML 表格数据**，请按照 **步骤二** 进行处理。
- 如果是 **大段文本数据**，请按照 **步骤三** 进行处理。

---

#### **二、HTML 表格数据处理**

1. **删除无关数据**：移除页眉、页脚、页码等与表格内容无关的数据，确保表格内容纯净。
2. **合并被截断的单元格内容**：由于跨页或其他原因导致的单元格内容被分割，需要将其合并，确保数据的完整性。
3. **处理合并单元格**：针对原始表格中存在的合并单元格，在转换为 Markdown 表格时进行适当处理，避免因 Markdown 不支持合并单元格而导致渲染问题。
4. **替换字典列内容**：对于表头中标识为"字典列"的列，将表格中该列的数值替换为对应的字典文本。
5. **修复表格结构**：修复不完整的表格行和列，确保转换后的 Markdown 表格能够正常渲染。
6. **合并多张表格**：将所有表格合并为一个整体，以首次出现的表头为准，忽略后续重复的表头。
7. **添加序号列**：在合并后的表格中增加一列"序号"，显示每条记录的顺序编号。

---

#### **三、大段文本数据处理**

1. **提取指定字段信息**：从文本中提取以下字段的数据：
   - **检查日期**
   - **检查时间**（如果有）
   - **检查项目名称**
   - **检查结果**（如果是定量检查结果，则只保留数值）
   - **检查结果限定符**（包含以下 7 个选项：>、<、=、>=、<=、≥、≤，当检查结果为定量数值时需要转换）
   - **检查结果单位**（如：mg、+、-、ng/ml、mg/L）
   - **正常值范围**
   - **正常值上限**
   - **正常值下限**
   - **结果是否正常**（正常、异常、不详）

2. **处理和转换数据**：
   - **定量结果处理**：当检查结果为定量数值时，只保留数值部分，并将限定符和单位分别提取出来。
   - **限定符转换**：如果检查结果中包含限定符（如">"、"<"等），需要将其单独提取到"检查结果限定符"列。
   - **正常值范围解析**：对于正常值范围，解析出上限和下限，并分别填写到"正常值上限"和"正常值下限"列。
   - **结果判定**：根据检查结果与正常值范围，判定"结果是否正常"，填写"正常"、"异常"或"不详"。

3. **整理成表格**：将提取和处理后的数据，按照上述字段顺序整理成标准的 **Markdown 表格**，其中表头即为上述字段名称。

4. **数据清洗和校验**：对提取的结果进行校对，确保数据准确、完整。如有缺失的数据，表格中对应单元格保持为空。

---

#### **四、同一天多个检查时间点数据合并处理**

1. **识别和分组**：
   - 根据"检查项目名称"、"检查日期"、"检查结果单位"和"正常值范围"对数据进行分组。
   - 对于每个分组，检查是否存在多个检查时间点。

2. **数据合并**：
   - 对于同一检查项目在同一天有多次检查的情况，将其合并为表格中的一行记录。
   - 在"检查结果"列中，直接展示所有检查时间和对应结果，采用简洁易读的格式。
   - 格式示例：
      - 08:30: >123 mg/L
      - 14:45: <90 mg/L

3. **结果判定处理**：
   - 对于"结果是否正常"字段，如果所有时间点结果判定一致，则保留该判定；否则标注为"多次检查结果不一致"。

---

#### **五、输出格式**

请根据以上要求，对提供的数据进行处理，并输出为标准的 **Markdown 表格** 格式，示例如下：

| 序号 | 检查日期    | 检查项目名称 | 检查结果 | 检查结果限定符 | 检查结果单位 | 正常值范围      | 正常值上限 | 正常值下限 | 结果是否正常 |
| ---- | ----------- | ------------ | -------- | -------------- | ------------ | --------------- | ---------- | ---------- | ------------ |
| 1    | 2023.05.23  | 血糖         | 08:30: >123<br>14:45: <90 | >              | mg/L         | 70-110 mg/L     | 110        | 70         | 多次检查结果不一致 |
| 2    | 2023.05.23  | 血压         | 09:00: 120/80   |                | mmHg        | 90-120/60-80    | 120/80     | 90/60     | 正常         |
| ...  | ...         | ...          | ...      | ...            | ...          | ...             | ...        | ...        | ...          |

---

请对以下数据进行处理，并输出为标准的 **Markdown 表格** 格式：

```html
{{tableData}}
```