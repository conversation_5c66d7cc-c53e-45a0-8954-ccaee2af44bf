1、忽略页眉和页脚的内容；
2、准确识别报告中的所有文字，理解每段文字的主要内容和上下文；
3、模板格式中的“<>”代表是一个参数，需要在事件报告中提取相应的值进行替换；
4、如报告中的字段对应的值没有提取到或没有勾选，则输出结果置空；
5、提取报告中的“SAE信息“：
	- 表格数据：每一个字段可能对应一个或多个值，按照表头和数据排列
	- 包含字段
		- 不良事件名称
		- 严重性标准
		- 严重程度
		- CTCAE分级
		- 发生日期
		- 事件转归
		- 结束日期
		- 试验用药品名称
		- 与试验用药品的关系
		- SAE医学术语
		- SAE发生时间
		- SAE严重程度
		- SAE严重性
		- SAE转归
		- SAE结束日期
		- SAE是否结束
		- 与药物的因果关系
		- 受试者是否因SAE中止试验治疗
		- 停药后SAE是否消失或减轻
		- 再次给药后SAE是否再次发生
6、按照且仅按照如下json格式输出，不要增加说明性描述：
	```json
	[
	{
		"报告分类":"<报告内容是否“SAE报告”正文？是则输出“报告”: 否则输出“邮件”>",
		"报告模块":"SAE信息",
		"不良事件名称":"<不良事件名称>",
		"严重性标准":["<严重性标准>","<严重性标准>",...],
		"严重程度":"<严重程度>",
		"CTCAE分级":"<CTCAE分级>",
		"发生日期":"<发生日期>",
		"事件转归":"<事件转归>",
		"结束日期":"<结束日期>",
		"试验用药品名称":"<试验用药品名称1>",
		"与试验用药品的关系":"<与试验用药品1的关系>",
		"SAE医学术语":"<SAE医学术语>",
		"SAE发生时间":"<SAE发生时间>",
		"SAE严重程度":"<SAE严重程度>",
		"SAE严重性":"<SAE严重性>",
		"SAE转归":"<SAE转归>",
		"SAE结束日期":"<SAE结束日期>",
		"SAE是否结束":"<SAE是否结束>",
		"与药物的因果关系":"<与药物的因果关系>",
		"受试者是否因SAE中止试验治疗":"<受试者是否因SAE中止试验治疗>",
		"停药后SAE是否消失或减轻":"<停药后SAE是否消失或减轻>",
		"再次给药后SAE是否再次发生":"<再次给药后SAE是否再次发生>"
	},
	......
	]
	```；

7、其它规则
**日期字段**
- 需要输出为yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年
-  [e.g., The source is "UK", The value is "".]
-  [e.g., The source is "继续", The value is "".]
-  [e.g., The source is "2018-UNK-UNK", The value is "2018-??-??".]
-  [e.g., The source is "2018-UNK-12", The value is "2018-??-??".]
-  [e.g., The source is "2018-09-UK, The value is "2018-09-??".]
-  [e.g., The source is "2018-09, The value is "2018-09-??".]
-  [e.g., The source is "UNK-UNK-12", The value is "".]
-  [e.g., The source is "2018/2/1", The value is "2018/02/01".]
- [e.g., The source is "_/_/_", The value is "".]
- [e.g., The source is "____/____/____", The value is "".]

**文本字段**
- 如果包含空格则需要移除
-  [e.g., The source is "维生素 C 注射液", The value is "维生素C注射液".]
-  [e.g., The source is " 维生素A注射液", The value is "维生素A注射液".]
-  [e.g., The source is "维生素D注射液 ", The value is "维生素D注射液".]
-  [e.g., The source is "维生素F注射液", The value is "维生素F注射液".]
