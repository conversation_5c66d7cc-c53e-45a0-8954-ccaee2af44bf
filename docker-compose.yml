services:
  server:
    image: pv-model-service:latest
    container_name: pv-model-service
    restart: always
    user: root
    ports:
      - "2189:2189"
    environment:
      - AZURE_API_URL=https://phc-openai-pv-dev.openai.azure.com/openai/deployments/pv-dev-gpt-4o/chat/completions?api-version=2024-08-01-preview
      - AZURE_API_KEY=********************************
      - AZURE_GPT_CONCURRENT=5
      - USE_CLAUDE_MODEL=true
      - CLAUDE_CONCURRENT=5
      - AWS_ACCESS_KEY=********************
      - AWS_SECRET_KEY=yYfLmRa9iSXz0V0xKE+1JQUTjVgOVvEccIjczAkE
      - AWS_REGION=us-east-1
      - AWS_MODEL_ID=anthropic.claude-3-5-sonnet-20240620-v1:0
      - <PERSON><PERSON>_MODEL_ID_V2=us.anthropic.claude-3-5-sonnet-20241022-v2:0
      - AWS_REASON_MODEL_ID=us.anthropic.claude-3-7-sonnet-20250219-v1:0
      - AWS_ENDPOINT_URL=https://bedrockproxy-elb-nlb-dc5c168a8ce84d82.elb.us-east-1.amazonaws.com
      - ANTHROPIC_VERSION=bedrock-2023-05-31
      - MYSQL_HOST=**********
      - MYSQL_PORT=3306
      - MYSQL_USER=root
      - MYSQL_PASSWORD=Mysql#r2368
      - MYSQL_DATABASE=pv_copilot_report_process_test
      - PMP_HOST=**********
      - PMP_PORT=3306
      - PMP_USER=root
      - PMP_PASSWORD=Mysql#r2368
      - PMP_DATABASE=pv_copilot_migration_als_pre
      - PROD_MYSQL_HOST=**********
      - PROD_MYSQL_PORT=3306
      - PROD_MYSQL_USER=root
      - PROD_MYSQL_PASSWORD=Mysql#r2368
      - PROD_MYSQL_DATABASE=pv_copilot_report_process
      - REASON_LLM_API_URL=https://phc-openai-pv-dev.openai.azure.com/openai/deployments/pv-dev-o1-preview/chat/completions?api-version=2024-10-01-preview
      - REASON_LLM_API_KEY=********************************
      - VISION_LLM_NAME=ep-20250224150706-fqztf
      - VISION_LLM_API_URL=https://ark.cn-beijing.volces.com/api/v3/chat/completions
      - VISION_LLM_API_KEY=9c7c6b53-eff7-4d0d-b2bb-79dde1cb4162
      - LANGFUSE_SECRET_KEY=******************************************
      - LANGFUSE_PUBLIC_KEY=pk-lf-6eeb334d-ce34-454a-ba99-706d2b71b737
      - LANGFUSE_HOST=http://**********:3000
    volumes:
      - ./volumes/logs:/home/<USER>
      - ./volumes/documents:/home/<USER>
      - ./volumes/prompts:/home/<USER>
      - ./volumes/report:/home/<USER>