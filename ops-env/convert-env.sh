#!/usr/bin/bash

# 定义配置文件目录
CONFIG_DIR="/home/<USER>/build/pv-model-service-test/ops-env"

# 定义支持的环境类型
environments=("test" "prod" "dev")

# 定义最大备份文件数量
MAX_BACKUPS=3

# 清理旧的备份文件，只保留最新的N个
cleanup_old_backups() {
    local dir=$1
    local base_name=$2
    local keep_num=$3
    
    # 列出所有备份文件并按时间戳排序（最新的最后）
    local backup_files=($(ls -t "${dir}/${base_name}.bak."* 2>/dev/null))
    local count=${#backup_files[@]}
    
    # 如果备份文件数量超过限制，删除最旧的文件
    if [ $count -gt $keep_num ]; then
        for ((i=keep_num; i<count; i++)); do
            rm "${backup_files[i]}"
            echo "清理旧备份文件：${backup_files[i]}"
        done
    fi
}

# 遍历所有支持的环境
for env in "${environments[@]}"; do
    input_file="${CONFIG_DIR}/env-${env}.conf"
    
    # 根据环境设置输出目录
    if [ "$env" = "prod" ]; then
        output_dir="/home/<USER>/uat"
    elif [ "$env" = "test" ]; then
        output_dir="/home/<USER>/test"
    else
        echo "跳过 $env 环境：未配置输出目录"
        continue
    fi
    
    # 设置输出文件路径
    output_file="${output_dir}/docker-compose.yml"
    
    # 检查输入文件是否存在
    if [ ! -f "$input_file" ]; then
        echo "跳过 $env 环境：$input_file 不存在"
        continue
    fi

    # 检查输出目录是否存在，不存在则创建
    if [ ! -d "$output_dir" ]; then
        mkdir -p "$output_dir"
    fi

    # 备份原有配置文件（如果存在）
    if [ -f "$output_file" ]; then
        backup_file="${output_file}.bak.$(date +%Y%m%d_%H%M%S)"
        cp "$output_file" "$backup_file"
        echo "已备份原配置文件：$backup_file"
        
        # 清理旧的备份文件
        cleanup_old_backups "$output_dir" "docker-compose.yml" $MAX_BACKUPS
    fi

    # 根据环境设置配置
    if [ "$env" = "prod" ]; then
        port_mapping="2188:2189"
        container_name="pv-copilot-service"
    else
        # test和dev环境使用相同的配置
        port_mapping="2999:2189"
        container_name="pv-model-service"
    fi

    # 创建输出文件，写入基础配置
    cat > "$output_file" << EOL
services:
  server:
    image: pv-model-service:latest
    container_name: ${container_name}
    restart: always
    user: root
    ports:
      - "$port_mapping"
    environment:
EOL

    # 读取conf文件并转换格式
    while IFS= read -r line || [ -n "$line" ]; do
        # 跳过空行和以#开头的注释行
        if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
            continue
        fi
        
        # 移除行尾带空格的注释（# 前必须有空格）
        line=$(echo "$line" | sed -E 's/[[:space:]]+#.*$//')
        
        # 如果行不为空，进行处理
        if [ ! -z "$line" ]; then
            # 提取键和值
            if [[ "$line" =~ ^([^=]+)=(.*)$ ]]; then
                key="${BASH_REMATCH[1]}"
                value="${BASH_REMATCH[2]}"
                
                # 直接输出键值对，保持原始格式
                echo "      - $key=$value" >> "$output_file"
            fi
        fi
    done < "$input_file"

    # 添加DNS映射
    cat >> "$output_file" << EOL
    extra_hosts:
      - "minio:**********"
EOL

    # 添加volumes配置
    cat >> "$output_file" << EOL
    volumes:
      - ./volumes/logs:/home/<USER>
      - ./volumes/documents:/home/<USER>
      - ./volumes/prompts:/home/<USER>
      - /home/<USER>/uploadFile/pmp:/home/<USER>/uploadFile/pmp
      - ./volumes/reports:/home/<USER>
EOL

    echo "✅ 已完成 $env 环境配置转换"
    echo "   输入：$input_file"
    echo "   输出：$output_file"
    echo ""
done

echo "所有配置文件转换完成！"