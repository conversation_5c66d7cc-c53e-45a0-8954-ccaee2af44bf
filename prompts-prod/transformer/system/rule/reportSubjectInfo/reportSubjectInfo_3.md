```moduleRule
- **"labtest": "检测项目名称"**
    - 根据**labtest**字典列表，将**labtest**数据转换为标准术语中文名，格式应严格按照字典中的原始格式，即**编码(名称)**，不做任何格式修改。如果字典中没有相应的条目，则保留原始数据不变。
    - 示例：如果字典中有条目“乳腺癌，Ⅳ期”对应“10006202(乳腺癌 IV 期)”，则输出结果应为**"10006202(乳腺癌 IV 期)"**，即使输入数据中使用了不同的分隔符（如中文逗号），转换时仍应确保标准术语与字典中的格式一致。
    - 示例：如果字典中存在“乳腺癌 IV 期”对应“10006202(乳腺癌 IV 期)”，则输出结果应为**"10006202(乳腺癌 IV 期)"**，确保格式一致。
    - 示例：如果字典中有“高甘油三酯血症”对应“10020869(高甘油三酯血症)”，则输出应为**"10020869(高甘油三酯血症)"**，确保没有额外的空格或符号。

- **"TXT_labunit_0": "定量检查结果单位，使用UCUM的编码对应的中文名称，如果没有则转义为单位对应的中文名称，当检查结果类型为定量时需要转换"**
    - 请根据**TXT_labunit_0**字典列表中的标准术语英文名进行匹配。如果匹配成功，则输出标准术语中文名；如果匹配不成功，则赋值为空。
    - 示例：若字典中存在“mg/dL”对应“毫克每分升”，则输出为**"毫克每分升"**，不添加空格或额外符号。

- **"examineType": "检测结果类型，包括以下4种选项：定性，定量，非结构化，结果尚未可用"**
    - 如果当前**examineType**是“定量”，但**TXT_labunit_0**为空，则需要将当前**examineType**转换为“非结构化”。如果检查结果是定量并且单位为空，按照规则强制转换为非结构化，避免错误数据的出现。

- **"labnotes": "非结构化或定性检测结果，长文本格式，转换时保持原有换行和缩进，但去除markdown符号，如果检测结果类型是非结构化或定性则需要转换"**
    - 如果当前**examineType**是“非结构化”，且**TXT_labunit_0**为空，则需按照以下格式输出：
      - `{labresult}(正常范围{labtestlow}-{labtesthigh}，当前结果{如果chkMoreInfoAvl为true则显示“异常”，否则不显示当前结果})`
      - 示例：若**labresult**为“血糖值6.5”，**labtestlow**为“3.0”，**labtesthigh**为“8.0”，且**chkMoreInfoAvl**为true，则输出为**"血糖值6.5(正常范围3.0-8.0，当前结果异常)"**。请确保输出格式与示例一致，避免格式错误。
    - 如果当前**examineType**是“定性”，如下处理：
        1. 合并规则：
           - 若 qualitativeResultType 和 comments 都有值：
             labnotes = qualitativeResultType + "，" + comments
           - 若其中一个为空：
             labnotes = 非空的那个字段值
        2. 处理后操作将当前**qualitativeResultType**和**comments**字段内的值都置为空串，如下
           qualitativeResultType = ""
           comments = ""

- **"labresult": "定量检测结果值，如果检测结果类型是定量需要转换"**
    - 如果当前**examineType**是“非结构化”，则此字段应留空。确保当检测类型为非结构化时，不返回定量结果，避免误导。

- **"labtestlow": "当前检测结果的正常范围最小值，如果检测结果类型是定量需要转换，不允许非数字字符，如果原数据没有，留空，不做推测"**
    - 如果当前**examineType**是“非结构化”，则此字段应留空。确保在非结构化类型下不提供错误数据或做不必要的推测。

- **"labtesthigh": "当前检测结果的正常范围最大值，如果检测结果类型是定量需要转换，不允许非数字字符，如果原数据没有，留空，不做推测"**
    - 如果当前**examineType**是“非结构化”，则此字段应留空。避免在非结构化类型下返回无效的正常范围数据。
```