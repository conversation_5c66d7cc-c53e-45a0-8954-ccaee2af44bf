```moduleRule
Apply the following custom conversion logic when extracting data. The "[e.g., .......]" is an example of the generated content,  please refer to the extraction logic for generation.

- **"rep_first_name": "取值报告者信息中的研究者姓名的first name，如果看不清手写签名则留空，不要从邮件内容中推测"**
	- 保持中文，不要进行翻译或者转义
- **"rep_last_name": "取值报告者信息中的研究者姓名的last name，保持原文的语言，不要翻译，如果看不清则留空"**
	- 保持中文，不要进行翻译或者转义
- **"rep_suffix": "研究者的职称，医疗领域的职称，表示医学专业资格和职位，如果报告中没有提及，请不要生成"**
    - 如果职称中存在(Subl)，则需要转换为标准术语(Sub-I)，否则保持和原文一致
```