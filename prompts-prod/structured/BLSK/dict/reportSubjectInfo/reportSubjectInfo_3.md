```moduleRule
在提取数据时，请应用以下定制的转换逻辑。"[例如：.......]"是从非结构化描述中生成内容的示例，请参考提取逻辑进行生成。

- **"labtestreptd": "实验室/诊断检查的项目名称，该值包含在'实验室/诊断检查'部分和'SAE/AESI的描述'部分中。如果存在前后空格则需要移除，但保留内部空格。"**：
    - 在事件描述的非结构化数据和实验室检查的结构化数据中定位所有与实验室/诊断检查相关的数据。
    - 如果同一天同一检查项目有多个时间点的数据，检查项目名称保持不变。
    - [例如：'于2024年12月31日多次测量血压，结果为13:58 180/104mmHg，14:17 190/114mmHg，14:21 184/107mmHg。' "labtestreptd"的值为"血压"。]

- **"labtest": "实验室/诊断检查的项目名称，该值包含在'实验室/诊断检查'部分和'SAE/AESI的描述'部分中。如果存在前后空格则需要移除，但保留内部空格。"**：
    - 与"labtestreptd"相同。
    - [例如："labtest"的值为"血压"。]

- **"examineDate": "实验室/诊断检查的日期，格式为yyyy-MM-dd。年月日如果不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空。"**：
    - 如果同一天有多个时间点，日期保持不变。
    - [例如："2024-12-31"]

- **"labresult": "实验室/诊断检查的结果，必须是纯数值，不包含任何单位、符号或文本。如果检查结果不是纯数值，或者包含单位或符号，则需要将完整的结果（包括数值和单位）记录在'labnotes'中，'labresult'留空。"**：
    - [例如：对于结果"180/104mmHg"，因为包含单位"mmHg"，'labresult'留空，完整结果记录在'labnotes'中。]

- **"labnotes": "用于记录非结构化的检查结果，或者当'labresult'无法填写时记录完整的检查结果。对于同一天同一检查项目的多个时间点数据，将每个时间点的结果记录在'labnotes'中，格式为：'HH:MM，结果'。每条记录占一行，按时间顺序排列。结果中的数值和单位之间不需要空格。"**：
    - [例如：
      13:58，180/104mmHg
      14:17，190/114mmHg
      14:21，184/107mmHg
      ]

- **"examineType": "检查结果类型，包含以下4个选项：定性、定量、非结构化、暂未得到结果。"**：
    - 对于记录在'labnotes'中的多时间点结果，类型应设置为"非结构化"。
    - 对于单一时间点的定量或定性结果，如果'labresult'能够填写纯数值，分别设置为"定量"或"定性"。

- **"TXT_labunit_0": "检查结果的单位。即使'labresult'为空（例如，多时间点的结果记录在'labnotes'中），'TXT_labunit_0'也需要赋值。使用UCUM编码对应的中文名称，如果没有则转写为中文单位名称。"**：
    - 确保单位被正确保留和记录，即使'labresult'为空。
    - [例如：对于结果"180/104mmHg"，'TXT_labunit_0'的值为"mmHg"。]

- **"qualitativeResultType": "定性检查结果，包含以下4个选项：临界值、不可判定、阴性、阳性。当检查结果类型为定性时需要转换。"**：
    - 如适用，根据检查结果确定。

- **"quantitativeCompare": "定量结果限定符，包含以下5个选项：>、<、=、≥、≤。当检查结果类型为定量时需要转换。"**：
    - 如适用，根据检查结果确定。

- **"labtestlow": "当前检查结果正常值范围的最小值。不允许包含非数字字符，如果原始数据中没有，则留空，不要推测。当检查结果类型为定量时需要转换。"**：
    - 如适用。

- **"labtesthigh": "当前检查结果正常值范围的最大值。不允许包含非数字字符，如果原始数据中没有，则留空，不要推测。当检查结果类型为定量时需要转换。"**：
    - 如适用。

- **"comments": "定量检查结果的评估。如果结果低于正常范围，显示'偏低'；高于正常范围，显示'偏高'；在正常范围内，显示'正常'。"**：
    - 如适用。

```

**说明：**

- **关于`TXT_labunit_0`的赋值：**

  - 无论`labresult`是否为空，都需要在`TXT_labunit_0`中填写检查结果的单位。

  - 在多时间点的结果中，即使`labresult`为空，`TXT_labunit_0`也需要填写对应的单位。

  - 例如，结果为"180/104mmHg"，`TXT_labunit_0`的值为"mmHg"。

- **示例应用：**

  - **多时间点的结果：**

    - **labtestreptd**：血压

    - **labtest**：血压

    - **examineDate**：2024-12-31

    - **examineType**：非结构化

    - **labresult**：留空

    - **TXT_labunit_0**：mmHg

    - **labnotes**：
      ```
      13:58，180/104mmHg
      14:17，190/114mmHg
      14:21，184/107mmHg
      ```

  - **单一时间点，结果包含单位或非纯数值：**

    - **labtestreptd**：血压

    - **labtest**：血压

    - **examineDate**：2024-12-31

    - **examineType**：非结构化

    - **labresult**：留空

    - **TXT_labunit_0**：mmHg

    - **labnotes**：180/104mmHg

  - **单一时间点，定量结果为纯数值：**

    - **labtestreptd**：血红蛋白

    - **labtest**：血红蛋白

    - **examineDate**：2024-12-31

    - **examineType**：定量

    - **quantitativeCompare**：=

    - **labresult**：130

    - **TXT_labunit_0**：g/L

    - **labnotes**：留空

- **注意事项：**

  - 在处理数据时，请确保所有情况下`TXT_labunit_0`都被正确赋值，反映检查结果的单位。

  - 即使`labresult`为空（如在多时间点结果的情况下），也要在`TXT_labunit_0`中填写单位。

  - 在`labnotes`中，结果的数值和单位之间不添加空格。

  - 如果结果包含多个数值和单位（如血压"180/104mmHg"），在`TXT_labunit_0`中填写常用的单位表示，例如"mmHg"。
```