{
  "reportBaseInfo": {
    "protocalNumber": "临床试验的方案编号",
    "reportLanguage": "cn",
    "country_text": "不良事件发生的国家，允许值ISO 3166-1alpha-2，EU，取值对应2个字母的国家代码",
    // 安全中心收到日期
    "central_receipt_date": "取值报告类型中的收到报告日期，yyyy-MM-dd",
    // 研究者获知SAE时间
    "CSM_UD_DATE_1": "研究者获知首次报告/本次上报信息的日期，yyyy-MM-dd 00:00格式",
    // 信息来源 医疗机构
    "CSM_UD_NUMBER_2": "100015",
    // C.1.CN.2 报告分类 上市前境内报告
    "CSM_UD_NUMBER_3": "100021",
    "center_no": "取值受试者信息的受试者中心号",
    "observe_study_type": 1,
    "btnAddClass": [
      {
        "TXT_Class_Table_0_class_id": "临床试验"
      }
    ]
  },
  "reportReporterInfo": [
    {
      "rep_first_name": "取值报告者信息中的研究者姓名的first name，如果看不清手写签名则留空，不要从邮件内容中推测",
      "rep_last_name": "取值报告者信息中的研究者姓名的last name，如果看不清则留空",
      "rep_country_text": "研究者所在的国家，允许值ISO 3166-1alpha-2，EU，取值对应2个字母的国家代码",
      "rep_suffix": "研究者的职称，医疗领域的职称，表示医学专业资格和职位，如果报告中没有提及，请不要生成",
      "rep_hcp_flg": "是否医疗专业人员，是为1，否为0，未知为2，空为-1",
      "TXT_rep_occupation_id": "医生",
      "rep_institution": "中心名称",
      "rep_primary_contact_flg": 1,
      "TXT_rep_type": "医生",
      "rep_phone": "格式为+{国际区号}-{研究者的手机号}",
      "rep_email": "取值报告者信息中的研究者邮箱，如果看不清则留空，不要从邮件内容中推测",
      "rep_fax": "取值报告者信息中的研究者传真，如果看不清则留空，不要从邮件内容中推测",
      "rep_department": "研究者的部门，如果报告中没有提及，请不要生成，不要从邮件内容中推测"
    }
  ],
  "reportSubjectInfo": {
    "pat_subj_num": "{中心的编号}-{受试者筛选号}",
    "pat_rand_num": "受试者随机编号",
    // LaNova的受试者不需要做隐私处理
    "pat_initials": "受试者姓名缩写",
    "TXT_pat_gender": "性别",
    "pat_height": "身高，cm。如果小数点后数字为0，则只保留整数部分",
    "pat_weight": "体重，kg。如果小数点后数字为0，则只保留整数部分",
    "pat_dob": "出生日期，yyyy-MM-dd日期格式",
    "pat_age": "事件发生时的年龄，只允许输入数字，如果存在则转换，不存在则不转换",
    "TXT_pat_age_unit": "事件发生时的年龄单位",
    // D.CN.2 族群
    "TXT_pat_ethnic_group_id": "民族，使用GB 3304 1991，取对应字典的中文名称",
    "pat_race_add": [
      {
        "TXT_Pat_Race_Table_0_ethnicity_id": "种族信息，包含以下五个选项，东亚人种（黄种人），澳洲人种（棕种人），尼格罗人种（黑种人），高加索人种（白种人）"
      }
    ],
    "pat_country_text": "不良事件发生的国家，允许值ISO 3166-1alpha-2，EU，取值对应2个字母的国家代码",
    "isDead": "受试者是否死亡，是为1，否为0",
    "Subject's Primary Disease": "如果study indication有值，则直接赋值，否则从事件描述中提取受试者参加临床试验的疾病",
    "rel_hist_add": [
      {
        "TXT_Rel_Hist_Table_{index}_pat_hist_type": "病史类型，该字段值必须且只能为 疾病史",
        "Rel_Hist_Table_{index}_pat_hist_rptd": "疾病名称，{index}代表当前记录的索引，从0开始，如果疾病名称存在空格则需要移除，请不要修改当前字段的名称",
        "Rel_Hist_Table_{index}_pat_hist_start": "开始日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空，{index}代表当前记录的索引，从0开始",
        "Rel_Hist_Table_{index}_pat_hist_stop": "结束日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空，{index}代表当前记录的索引，从0开始",
        "Rel_Hist_Table_{index}_pat_hist_cont": "是否持续，是为1，否为0，未知为2，空为-1，当是否持续为否的时候才能填写结束日期，{index}代表当前记录的索引，从0开始"
      }
    ],
    "refExamineTable": [
      {
        "labtestreptd": "检查项目名称，如果存在前后空格则需要移除，但保留内部空格",
        "labtest": "检查项目名称，如果存在前后空格则需要移除，但保留内部空格",
        "examineType": "检查结果类型，包含以下4个选项，定性、定量、非结构化、暂未得到结果",
        "examineDate": "检查日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
        "qualitativeResultType": "定性检查结果，包含以下4个选项，临界值、没有结论、阴性、阳性，当检查结果类型为定性时需要转换",
        "quantitativeCompare": "定量结果限定符，包含以下5个选项，>、<、=、>=、<=，当检查结果类型为定量时需要转换",
        "labresult": "定量检查结果数值，当检查结果类型为定量时需要转换",
        "TXT_labunit_0": "定量检查结果单位，使用UCUM的编码对应的中文名称，如果没有则转义为单位对应的中文名称，当检查结果类型为定量时需要转换",
        "labtestlow": "当前检查结果正常值范围允许的最小数值，当检查结果类型为定量时需要转换，不允许出现非数字的字符，如果原始数据中没有则留空不要推测",
        "labtesthigh": "当前检查结果正常值范围允许的最大数值，当检查结果类型为定量时需要转换，不允许出现非数字的字符，如果原始数据中没有则留空不要推测",
        "labnotes": "非结构化检查结果，长文本格式，转换时需要保持原有的换行\n和缩进\t，但是需要移除markdown的符号，当检查结果类型为非结构化时需要转换",
        "comments": "定量检查结果的评估，检查结果在正常范围以下，则显示偏低，正常范围以上，则显示偏高，在正常范围之内，则显示正常}"
      }
    ],
    "deadReasonInfoTable": {
      "death_date": "受试者死亡日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
      "autopsy_done": "是否做过尸检，是为2，否为1，不详为3，空为0",
      "btnAdd": [
        {
          "CSDD_0_cause_reptd": "死亡原因的描述或名称"
        }
      ]
    }
  },
  "drugInfo": {
    "试验用药": [
      {
        "product_name": "试验用药药品名称，如果存在前后空格则需要移除，但保留内部空格",
        "drug_type_0": "1",
        // G.k.2.5 临床试验药物
        "pat_exposure": "临床试验项目中使用的研究药物的药物编号",
        "btnAddInd": [
          {
            "Ind_Table_{index}_ind_reptd": "药品适应症名称，{index}代表当前记录的索引，从0开始，如果试验用药信息中没有填写，则使用受试者基础诊断"
          }
        ],
        "TXT_act_taken_id": "针对药物采取的措施，包含以下选项，${systemdict.TXT_act_taken_id:，}",
        "expStopType": "停止用药类型，永久停药为1，暂停用药为2",
        "expStopDrugDate": "永久停药日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
        "expPauseDrugDate": "暂停用药日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
        "dechallenge": "停药后的结果，不明为2，阳性为1，阴性为0，不适用为3，空为-1，如果停药日期存在，则需要确保值为0、1或2中的一个，但始终保持数字格式输出",
        "dechall_date": "停药日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
        "expAgainStartDate": "重新用药日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
        "rechallenge": "重新用药后的结果，不明为2，阳性为1，阴性为0，不适用为3，空为-1，如果停药日期存在，则需要确保值为0、1或2中的一个，但始终保持数字格式输出",
        "rechall_start": "重新用药开始日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
        "rechall_stop": "重新用药结束日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
        "expLessDoseDate": "剂量减小日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
        "expLessDoseTo": "剂量减小数值",
        "expLessDoseToUnit": "剂量减小单位，使用UCUM的编码对应的中文名称，如果没有则转义为单位对应的中文名称",
        "expResumeDoseDate": "剂量减小日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
        "expResumeDoseTo": "剂量减小数值",
        "expResumeDoseToUnit": "剂量减小单位，使用UCUM的编码对应的中文名称，如果没有则转义为单位对应的中文名称",
        "expDoseTable": [
          {
            "cdr_dose": "药品剂量数值，不允许出现非数字的字符",
            "TXT_cdr_dose_unit_id": "药品剂量单位，使用UCUM的编码对应的中文名称，如果没有则转义为单位对应的中文名称",
            "TXT_freq_id": "药品给药频率，如果用药结束日期不存在则可以根据用药频率去计算结束日期",
            "TXT_cdr_admin_route_id": "药品给药途径",
            "start_datetime": "用药开始日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
            "ongoing": "是否持续用药，是为1，否为空，当是否持续为否的时候才能填写结束日期",
            "stop_datetime": "用药结束日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
            "cdr_lot_no": "药品生产批号",
            "dose_description": "给药描述，格式为<药品剂量数值> <药品剂量单位>, <药品给药频率>"
          }
        ]
      }
    ],
    "合并用药": [
      {
        "product_name": "合并用药药品名称，如果存在前后空格则需要移除，但保留内部空格",
        "drug_type_1": "1",
        "TXT_formulation_id": "药品用药剂型",
        "btnAddInd": [
          {
            "Ind_Table_{index}_ind_reptd": "药品适应症名称，{index}代表当前记录的索引，从0开始"
          }
        ],
        "expDoseTable": [
          {
            "cdr_dose": "药品剂量数值，不允许出现非数字的字符",
            "TXT_cdr_dose_unit_id": "药品剂量单位，使用UCUM的编码对应的中文名称，如果没有则转义为单位对应的中文名称",
            "TXT_freq_id": "药品给药频率",
            "TXT_cdr_admin_route_id": "药品给药途径",
            "start_datetime": "用药开始日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空，精度至少要到年，否则返回空",
            "ongoing": "是否持续用药，是为1，否为空，当是否持续为否的时候才能填写结束日期",
            "stop_datetime": "用药结束日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
            "cdr_lot_no": "药品生产批号",
            "dose_description": "给药描述，格式为<药品剂量数值> <药品剂量单位>, <药品给药频率>"
          }
        ]
      }
    ],
    "治疗用药": [
      {
        "product_name": "治疗用药药品名称，如果存在前后空格则需要移除，但保留内部空格",
        "drug_type_2": "1",
        "TXT_formulation_id": "药品用药剂型",
        "btnAddInd": [
          {
            "Ind_Table_{index}_ind_reptd": "药品适应症名称，{index}代表当前记录的索引，从0开始"
          }
        ],
        "expDoseTable": [
          {
            "cdr_dose": "药品剂量数值，不允许出现非数字的字符",
            "TXT_cdr_dose_unit_id": "药品剂量单位，使用UCUM的编码对应的中文名称，如果没有则转义为单位对应的中文名称",
            "TXT_freq_id": "药品给药频率",
            "TXT_cdr_admin_route_id": "药品给药途径",
            "start_datetime": "用药开始日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
            "ongoing": "是否持续用药，是为1，否为空，当是否持续为否的时候才能填写结束日期",
            "stop_datetime": "用药结束日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
            "cdr_lot_no": "药品生产批号",
            "dose_description": "给药描述，格式为<药品剂量数值> <药品剂量单位>, <药品给药频率>"
          }
        ]
      }
    ],
    "既往用药": [
      {
        "product_name": "既往用药药品名称，如果存在前后空格则需要移除，但保留内部空格",
        "drug_type_1": "1",
        "TXT_formulation_id": "药品用药剂型",
        "btnAddInd": [
          {
            "Ind_Table_{index}_ind_reptd": "药品适应症名称，{index}代表当前记录的索引，从0开始"
          }
        ],
        "expDoseTable": [
          {
            "cdr_dose": "药品剂量数值，不允许出现非数字的字符",
            "TXT_cdr_dose_unit_id": "药品剂量单位",
            "TXT_freq_id": "药品给药频率",
            "TXT_cdr_admin_route_id": "药品给药途径",
            "start_datetime": "用药开始日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空，精度至少要到年，否则返回空",
            "ongoing": "是否持续用药，是为1，否为空，当是否持续为否的时候才能填写结束日期",
            "stop_datetime": "用药结束日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
            "cdr_lot_no": "药品生产批号",
            "dose_description": "给药描述，格式为<药品剂量数值> <药品剂量单位>, <药品给药频率>"
          }
        ]
      }
    ]
  },
  "reportSaeDetailInfo": [
    {
      "country_occured_text": "不良事件发生的国家，允许值ISO 3166-1alpha-2，EU，取值对应2个字母的国家代码",
      "original_language": "SAE报告使用的语言，中文为6，英文为1",
      "CSE_UD_NUMBER_2": "是否为AESI，是为100007，否为100008，如果无法判断则赋值为空",
      // CTCAE分级
      "CSE_UD_NUMBER_1": "CTCAE分级，1级为100000，2级为100001，3级为100002，4级为100003，5级为100004",
      "desc_reptd": "SAE/AESI术语(临床诊断优先)",
      "onset": "开始日期(最早观察到症状/体征)，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
      "stop_date": "结束日期(如有)，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
      "receipt_date": "取值报告类型的收到报告日期",
      "sc_death": "严重性标准，导致死亡为1",
      "sc_threat": "严重性标准，危及生命为1",
      "sc_hosp": "严重性标准，导致住院/住院时间延长为1",
      "sc_disable": "严重性标准，残疾/功能丧失为1",
      "sc_cong_anom": "严重性标准，先天性异常或出生缺陷为1",
      "med_serious": "严重性标准，其他重要医学事件为1",
      "TXT_evt_outcome_id": "事件转归，包含以下6个选项，${systemdict.TXT_evt_outcome_id:、}",
      "event_caused": "是否需要住院，是为1，否为空",
      "prolonged": "是否延长住院时间，是为1，否为空",
      "end_date": "出院日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
      "start_date": "入院日期，yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年，否则返回空",
      "sc_other_text": "重要的医学事件，判断理由"
    }
  ],
  "药品和不良反应评价矩阵": [{
    "productName": "评价关联的试验用药的药品名称，药品名称必须是连续的字符串(不含空白字符)。示例：'注射用 紫杉醇' -> '注射用紫杉醇'",
    "eventName": "评价关联的不良事件名称",
    "assessmentSource": "评价来源，默认值为初始报告人",
    "assessmentMethod": "评价房价，默认值为Global Introspection",
    "assessmentResult": "评价结果，包含以下9个选项，${systemdict.assessmentResult:，}",
    "TXT_act_taken_id": "${systemdict.TXT_act_taken_id:，}",
    "dechallenge": "停药后的结果，不明为2，阳性为1，阴性为0，不适用为3，空为-1，如果停药日期存在，则需要确保值为0、1或2中的一个，但始终保持数字格式输出",
    "rechallenge": "重新用药后的结果，不明为2，阳性为1，阴性为0，不适用为3，空为-1，如果停药日期存在，则需要确保值为0、1或2中的一个，但始终保持数字格式输出"
  }],
  "saeDescribe": {
    "narrative": "SAE/AESI的描述，长文本格式，转换时需要保持原有的换行\n和缩进\t，但是需要移除markdown的符号"
  }
}