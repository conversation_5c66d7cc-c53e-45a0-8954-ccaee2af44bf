请根据以下要求，对提供的数据进行格式化和优化：

---

### **数据处理步骤：**

#### **一、数据格式识别和特殊情况检测**

首先检查数据内容：

1. **特殊情况检测**（最高优先级）：
   - 检查现病史和既往病史是否只包含"无"、"不详"或空白内容
   - 如果是，则这是特殊情况，**必须使用JSON格式输出**
   - 如果否，则按常规处理，输出Markdown表格

2. **常规数据类型判断**（仅当不是特殊情况时）：
   - 如果是 **HTML 表格数据**，请按照 **步骤二** 进行处理
   - 如果是 **大段文本数据**，请按照 **步骤三** 进行处理

---

#### **二、HTML 表格数据处理**（仅适用于常规情况）

1. **删除无关数据**：移除页眉、页脚、页码等与表格内容无关的数据，确保表格内容纯净。
2. **合并被截断的单元格内容**：由于跨页或其他原因导致的单元格内容被分割，需要将其合并，确保数据的完整性。
3. **处理合并单元格**：针对原始表格中存在的合并单元格，在转换为 Markdown 表格时进行适当处理，避免因 Markdown 不支持合并单元格而导致渲染问题。
4. **替换字典列内容**：对于表头中标识为"字典列"的列，将表格中该列的数值替换为对应的字典文本。
5. **修复表格结构**：修复不完整的表格行和列，确保转换后的 Markdown 表格能够正常渲染。
6. **合并多张表格**：将所有表格合并为一个整体，以首次出现的表头为准，忽略后续重复的表头。
7. **添加序号列**：在合并后的表格中增加一列"序号"，显示每条记录的顺序编号。
8. **重要：保持原表格中的疾病/症状描述**：对于已在表格中的疾病名称，保持原始格式，不进行标准化处理。

---

#### **三、大段文本数据处理**（仅适用于常规情况）

1. **提取所有病史相关信息**：从文本中提取所有与病史相关的信息（包括但不限于现病史、既往史、个人史、家族史、过敏史、用药史、手术史、外伤史等）。

2. **疾病名称标准化处理**（仅适用于非表格的大段文本）：从原始描述中提取实际的疾病/症状名称，去除修饰词和额外描述：
   - 去除"病史"、"史"、"AE"等修饰词，如"乙肝携带病史" → "乙肝"
   - 保留症状的具体描述，如"左下肢水肿" → "左下肢水肿"
   - 去除非疾病/症状核心的额外描述词
   - 对于疾病名称与部位的组合，保留完整信息以避免信息丢失

3. **提取以下字段的数据**：
   - **疾病/症状名称**（对于大段文本中提取的内容，应经过上述标准化处理）
   - **开始日期**
   - **结束日期**
   - **是否继续**

4. **整理成表格**：将提取和处理后的数据，按照上述字段顺序整理成标准的 **Markdown 表格**，其中表头即为上述字段名称。

---

#### **四、特殊情况处理 - 无实质性疾病数据**

当数据符合特殊情况（即现病史和既往病史都只有"无"、"不详"或空白内容）时：

1. **必须使用JSON格式输出**，格式如下：
   ```json
   {
     "现病史": "[值]",
     "既往病史": "[值]"
   }
   ```

2. **值的确定规则**：
   - 如果明确标记为"无"，则值为"无"
   - 如果只有空白单元格或没有明确标识，则值为"不详"

3. **重要提示**：
   - 在特殊情况下，**禁止**使用Markdown表格格式输出
   - 必须使用代码块包裹JSON输出（```json和```）

---

#### **五、数据合并与最终处理**（仅适用于常规情况）

1. **合并两种来源的数据**：将HTML表格数据（保持原始疾病名称）和大段文本数据（标准化后的疾病名称）合并到一个表格中。

2. **数据清洗和校验**：对提取的结果进行校对，确保数据准确、完整。如有缺失的数据，表格中对应单元格保持为空。

---

#### **六、输出格式**

1. **特殊情况输出**（无实质性疾病数据）：
   - 必须使用JSON格式，如步骤四所述

2. **常规情况输出**（有实质性疾病数据）：
   - 使用标准的 **Markdown 表格** 格式，示例如下：

| 序号 | 疾病/症状名称 | 开始日期   | 结束日期   | 是否继续 |
| ---- | ------------- | ---------- | ---------- | -------- |
| 1    | 高血压        | 2020.01.01 | 至今       | 是       |
| 2    | 阑尾切除      | 2010.05.15 | 2010.05.30 | 否       |
| 3    | 乙肝          | 2017.03.01 | 至今       | 是       |
| ...  | ...           | ...        | ...        | ...      |

---

请对以下数据进行处理。重要提示：如果是特殊情况（现病史和既往病史都只有"无"、"不详"或空白），必须使用JSON格式输出，禁止使用表格！

```html
{{tableData}}
```