1、忽略页眉和页脚的内容；
2、准确识别报告中的所有文字，理解每段文字的主要内容和上下文；
3、模板格式中的“<>”代表是一个参数，需要在事件报告中提取相应的值进行替换；
4、提取报告中的“报告者信息“：
	- 单模块数据：每一个字段都只有一个值，按照字段和分组排列
	- 包含字段
		- 研究者签名
		- 研究者签名日期
		- 研究者姓名（打印体）
		- 研究者职位
		- 研究者电话
		- 研究者邮箱
5、按照且仅按照如下json格式输出，不要增加说明性描述：
	```json
	{
		"报告分类":"<报告内容是否“SAE报告”正文？是则输出“报告”: 否则输出“邮件”>",
		"报告模块":"报告者信息",
		"研究者签名":"<研究者签名>",
		"研究者签名日期":"<研究者签名日期，日期字段>",
		"研究者姓名（打印体）":"<研究者姓名（打印体）>",
		"研究者职位":"<研究者职位>",
		"研究者电话":"<研究者电话>",
		"研究者邮箱":"<研究者邮箱>"
	}；

7、其它规则
**日期字段**
- 需要输出为yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年
-  [e.g., The source is "UK", The value is "".]
-  [e.g., The source is "继续", The value is "".]
-  [e.g., The source is "2018-UNK-UNK", The value is "2018-??-??".]
-  [e.g., The source is "2018-UNK-12", The value is "2018-??-??".]
-  [e.g., The source is "2018-09-UK, The value is "2018-09-??".]
-  [e.g., The source is "2018-09, The value is "2018-09-??".]
-  [e.g., The source is "UNK-UNK-12", The value is "".]
-  [e.g., The source is "2018/2/1", The value is "2018/02/01".]
- [e.g., The source is "_/_/_", The value is "".]
- [e.g., The source is "____/____/____", The value is "".]

**文本字段**
- 如果包含空格则需要移除
-  [e.g., The source is "维生素 C 注射液", The value is "维生素C注射液".]
-  [e.g., The source is " 维生素A注射液", The value is "维生素A注射液".]
-  [e.g., The source is "维生素D注射液 ", The value is "维生素D注射液".]
-  [e.g., The source is "维生素F注射液", The value is "维生素F注射液".]