1、忽略页眉和页脚的内容；
2、准确识别报告中的所有文字，理解每段文字的主要内容和上下文；
3、模板格式中的“<>”代表是一个参数，需要在事件报告中提取相应的值进行替换；
4、如报告中的字段对应的值没有提取到或没有勾选，则输出结果置空；
5、提取报告中的“合并疾病及治疗”：
	- 表格数据：每一个字段可能对应一个或多个值，按照表头和数据排列
	- 包含字段
		- 疾病名称（仅提取现病史中的疾病名称，如果记录中存在药品名称、适应症名称等其它名称字段，则说明此记录不是现病史的数据）
		- 开始日期
		- 结束日期
		- 是否继续
		- 仍持续
		- 疾病详情(包括手术名称和日期)
		- 其他病史
		- 过敏史
		- 家族史
		- 手术史
		- 吸烟史
		- 饮酒史
6、按照且仅按照如下json格式输出，不要增加说明性描述：
	```json
	[
	{
		"报告分类":"<报告内容是否“SAE报告”正文？是则输出“报告”: 否则输出“邮件”>",
		"报告模块":"与事件相关的现病史",
		"疾病名称":"<疾病名称>",
		"开始日期":"<开始日期>",
		"结束日期":"<结束日期>",
		"是否继续":"<是否继续>",
		"仍持续":"<仍持续>",
		"疾病详情":"<疾病详情>",
		"其他病史":"<其他病史？“有”: “无”: “不详”>",
		"过敏史":"<过敏史？是则输出“是”: 否则输出“否”>",
		"家族史":"<家族史？是则输出“是”: 否则输出“否”>",
		"手术史":"<手术史？是则输出“是”: 否则输出“否”>",
		"吸烟史":"<吸烟史？是则输出“是”: 否则输出“否”>",
		"饮酒史":"<饮酒史？是则输出“是”: 否则输出“否”>"
	},
	......
	]
	```；

7、其它规则
**结果判断**
- 现病史的记录只会包含一个现病史的名称，如果记录中存在药品名称、适应症名称等其它名称字段，则说明此记录不是现病史的数据，不要将其转为现病史的结构化数据
-  [e.g., The source is "| 复方甲氧那明胶囊 | 肺炎 | 2粒 tid | 口服 | 2024/8/19 | ___/___/___ | 1 |", The strcuture shouldn't be converted.]
-  [e.g., The source is "| 强力枇杷露 | 肺部感染 | 10ml/每日三次 | 口服 | 2024 / 07 / 29 | 2024 / 08 / 05 | ☐是☒否☐不详 |", The strcuture shouldn't be converted.]
- 现病史的名称是NA，不要将其转为现病史的结构化数据
-  [e.g., The source is "NA", The strcuture shouldn't be converted.]

**日期字段**
- 需要输出为yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年
-  [e.g., The source is "UK", The value is "".]
-  [e.g., The source is "继续", The value is "".]
-  [e.g., The source is "2018-UNK-UNK", The value is "2018-??-??".]
-  [e.g., The source is "2018-UNK-12", The value is "2018-??-??".]
-  [e.g., The source is "2018-09-UK, The value is "2018-09-??".]
-  [e.g., The source is "2018-09, The value is "2018-09-??".]
-  [e.g., The source is "UNK-UNK-12", The value is "".]
-  [e.g., The source is "2018/2/1", The value is "2018/02/01".]
- [e.g., The source is "_/_/_", The value is "".]
- [e.g., The source is "____/____/____", The value is "".]

**文本字段**
- 如果包含空格则需要移除
-  [e.g., The source is "维生素 C 注射液", The value is "维生素C注射液".]
-  [e.g., The source is " 维生素A注射液", The value is "维生素A注射液".]
-  [e.g., The source is "维生素D注射液 ", The value is "维生素D注射液".]
-  [e.g., The source is "维生素F注射液", The value is "维生素F注射液".]