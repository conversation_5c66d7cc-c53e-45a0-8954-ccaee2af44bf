1、忽略页眉和页脚的内容，页眉和页脚指每页顶部或底部的标准重复文本，如“Confidential and Proprietary”、“科伦博泰项目专用，定稿于...”、“康龙化成”、“PHARMARON”、“SAE/AESI Report Form for Clinical Trial”等；
2、准确识别报告中的所有文字，理解每段文字的主要内容和上下文；
3、模板格式中的“<>”代表是一个参数，需要在事件报告中提取相应的值进行替换；
4、如报告中的字段对应的值没有提取到或没有勾选，则输出结果置空；
5、提取报告中的“受试者信息”：
	- 单模块数据：每一个字段都只有一个值，按照字段和分组排列
	- 包含字段
		- 受试者中心号
		- 受试者筛选号
		- 中心名称
		- 受试者姓名缩写
		- 性别
		- 身高
		- 体重
		- 出生日期
		- 种族
		- 民族
6、按照且仅按照如下json格式输出，不要增加说明性描述：
	```json
	{
		"报告分类":"报告",
		"报告模块":"受试者信息",
		"受试者中心号":"<受试者中心号>",
		"受试者筛选号":"<受试者筛选号>",
		"中心名称":"<中心名称>",
		"受试者姓名缩写":"<受试者姓名缩写>",
		"性别":"<性别>",
		"身高":"<身高>",
		"体重":"<体重>",
		"出生日期":"<出生日期>",
		"种族":"<种族>",
		"民族":"<民族>"
	}
	```；

7、其它规则
**日期字段**
- 需要输出为yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年
-  [e.g., The source is "UK", The value is "".]
-  [e.g., The source is "继续", The value is "".]
-  [e.g., The source is "2018-UNK-UNK", The value is "2018-??-??".]
-  [e.g., The source is "2018-UNK-12", The value is "2018-??-??".]
-  [e.g., The source is "2018-09-UK, The value is "2018-09-??".]
-  [e.g., The source is "2018-09, The value is "2018-09-??".]
-  [e.g., The source is "UNK-UNK-12", The value is "".]
-  [e.g., The source is "2018/2/1", The value is "2018/02/01".]
- [e.g., The source is "_/_/_", The value is "".]
- [e.g., The source is "____/____/____", The value is "".]

**文本字段**
- 如果包含空格则需要移除
-  [e.g., The source is "维生素 C 注射液", The value is "维生素C注射液".]
-  [e.g., The source is " 维生素A注射液", The value is "维生素A注射液".]
-  [e.g., The source is "维生素D注射液 ", The value is "维生素D注射液".]
-  [e.g., The source is "维生素F注射液", The value is "维生素F注射液".]

**种族**
- 包含以下五个选项，东亚人种（黄种人），澳洲人种（棕种人），尼格罗人种（黑种人），高加索人种（白种人）
- 如果原始数据种族的取值在选项中存在，则转换为对应的字典，否则留空，不要进行推测和衍生
-  [e.g., '种族: 汉族' The value "种族" is "".]
-  [e.g., '种族: 黄种人' The value "种族" is "东亚人种（黄种人）".]
-  [e.g., '民族: 黄种人' The value "种族" is "东亚人种（黄种人）".]

**民族**
- 包含汉族、壮族等中国的民族分类
- 如果原始数据种族的取值在选项中存在，则转换为对应的字典，否则留空，不要进行推测和衍生
-  [e.g., '种族: 汉族' The value "民族" is "汉族".]
-  [e.g., '种族: 黄种人' The value "民族" is "".]
-  [e.g., '民族: 黄种人' The value "民族" is "".]