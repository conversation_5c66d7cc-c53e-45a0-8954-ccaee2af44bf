1、忽略页眉和页脚的内容，页眉和页脚指每页顶部或底部的标准重复文本，如“Confidential and Proprietary”、“科伦博泰项目专用，定稿于...”、“康龙化成”、“PHARMARON”、“SAE/AESI Report Form for Clinical Trial”等；
2、准确识别报告中的所有文字，理解每段文字的主要内容和上下文；
3、模板格式中的“<>”代表是一个参数，需要在事件报告中提取相应的值进行替换；
4、如报告中的字段对应的值没有提取到或没有勾选，则输出结果置空；
5、报告中的“SAE与药物的关系”，每一行记录生成一个药物评价；
6、“试验用药品名称”需根据“项目药品名称”进行映射，输出结果采用“项目药品名称”中最相似的值，如没有相似度高的值，则输出映射前的值；
7、提取报告中的“SAE/ECI/AESI的详细情况”：
	- 表格数据：每一个字段可能对应一个或多个值，按照表头和数据排列
	- 包含字段
		- 不良事件名称
		- 严重性标准
		- 严重程度
		- CTCAE分级
		- 发生日期
		- 事件转归
		- 结束日期
		- SAE报告情况国内
		- SAE报告情况国外
		- 药物信息（包括“试验用药品名称”和“与试验用药品的关系”）
8、一个不良事件可以对应多个药品，每个药品均会产生一个评价
	- 对于每一个不良事件记录，如果存在多个药品关联，包含所有药品的评价
9、按照且仅按照如下json格式输出，不要增加说明性描述：
   ```json
   [
     {
       "报告分类": "报告",
       "报告模块": "SAE/ECI/AESI的详细情况",
       "不良事件名称": "<不良事件名称>",
       "严重性标准": "<严重性标准>",
       "严重程度": "<严重程度>",
       "CTCAE分级": "<CTCAE分级>",
       "发生日期": "<发生日期>",
       "事件转归": "<事件转归>",
       "结束日期": "<结束日期>",
	   "SAE报告情况国内":"<SAE报告情况国内>",
	   "SAE报告情况国外":"<SAE报告情况国外>",
       "药物信息": [
         {
           "试验用药品名称": "<试验用药品名称>",
           "与试验用药品的关系": "<与试验用药品的关系>"
         },
         ...
       ]
     },
     ...
   ]
   ```
10、其它规则
**日期字段**
- 需要输出为yyyy-MM-dd日期格式，年月日如果不全，可以用"??"进行占位，但是需要遵循日期精度从日到年，精度至少要到年
-  [e.g., 原始值为"UK"，则输出值为""。]
-  [e.g., 原始值为"继续"，则输出值为""。]
-  [e.g., 原始值为"2018-UNK-UNK"，则输出值为"2018-??-??"。]
-  [e.g., 原始值为"2018-UNK-12"，则输出值为"2018-??-??"。]
-  [e.g., 原始值为"2018-09-UK"，则输出值为"2018-09-??"。]
-  [e.g., 原始值为"2018-09"，则输出值为"2018-09-??"。]
-  [e.g., 原始值为"UNK-UNK-12"，则输出值为""。]
-  [e.g., 原始值为"2018/2/1"，则输出值为"2018/02/01"。]
-  [e.g., 原始值为"_/_/_"，则输出值为""。]
-  [e.g., 原始值为"____/____/____"，则输出值为""。]

**文本字段**
- 如果包含空格则需要移除
-  [e.g., 原始值为"维生素 C 注射液"，则输出值为"维生素C注射液"。]
-  [e.g., 原始值为" 维生素A注射液"，则输出值为"维生素A注射液"。]
-  [e.g., 原始值为"维生素D注射液 "，则输出值为"维生素D注射液"。]
-  [e.g., 原始值为"维生素F注射液"，则输出值为"维生素F注射液"。]

**严重程度**
- 包含以下三个选项：轻、中、重
- 如果原始数据的取值在选项中存在，则转换为对应的值，否则留空，不要进行推测和衍生
-  [e.g., 原始值为"3级"，则输出值为""。]
-  [e.g., 原始值为"轻微"，则输出值为"轻"。]
-  [e.g., 原始值为"中级"，则输出值为"中"。]

**CTCAE分级**
- 包含以下5个选项：1级、2级、3级、4级、5级
- 如果原始数据的取值在选项中存在，则转换为对应的值，否则留空，不要进行推测和衍生
-  [e.g., 原始值为"3"，则输出值为"3级"。]
-  [e.g., 原始值为"轻微"，则输出值为""。]
-  [e.g., 原始值为"中级"，则输出值为""。]