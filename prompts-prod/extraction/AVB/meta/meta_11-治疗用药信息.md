1、忽略页眉和页脚的内容，页眉和页脚指每页顶部或底部的标准重复文本，如“Confidential and Proprietary”、“科伦博泰项目专用，定稿于...”、“康龙化成”、“PHARMARON”、“SAE/AESI Report Form for Clinical Trial”等；
2、准确识别报告中的所有文字，理解每段文字的主要内容和上下文；
3、模板格式中的“<>”代表是一个参数，需要在事件报告中提取相应的值进行替换；
4、如报告中的字段对应的值没有提取到或没有勾选，则输出结果置空；
5、报告中的“治疗用药”，每一个行记录生成一个json；
6、提取报告中的“治疗用药信息“，遍历报告中所有的治疗用药数据，每一条都需要提取并转换为结构化数据：
	- 表格数据：每一个字段可能对应一个或多个值，按照表头和数据排列
	- 包含字段
		- 英文缩写
		- 治疗用药品名称
		- 治疗用药适应症
		- 治疗用药剂型
		- 治疗用药剂量
		- 治疗用药频率
		- 治疗用药给药途径
		- 治疗用药开始日期
		- 治疗用药结束日期
		- 治疗用药是否继续用药（请用中文输出，1替换为“继续”：2替换为“不详”：3替换为“否”）
6、按照且仅按照如下json格式输出，不要增加说明性描述：
	```json
	[
	{
		"报告分类":"报告",
		"报告模块":"治疗用药信息",
		"英文缩写":[{"<英文缩写>":"<中文全称>"}......],
		"治疗用药品名称":"<治疗用药品名称>",
		"治疗用药适应症":"<治疗用药适应症>",
		"治疗用药剂型":"<治疗用药剂型>",
		"治疗用药剂量":"<治疗用药剂量>",
		"治疗用药频率":"<治疗用药频率>",
		"治疗用药给药途径":"<治疗用药给药途径>",
		"治疗用药开始日期":"<治疗用药开始日期，日期字段>",
		"治疗用药结束日期":"<治疗用药结束日期，日期字段>",
		"治疗用药是否继续用药":"<治疗用药是否继续用药？请用中文输出，1替换为“继续”：2替换为“不详”：3替换为“否”>"
	},
	......
	]
	```；

7、其它规则
**日期字段**
- 需要输出为yyyy-MM-dd日期格式，年月日如果可以不全，可以用??进行占位，但是需要遵循日期精度从日到年，精度至少要到年
-  [e.g., The source is "UK", The value is "".]
-  [e.g., The source is "继续", The value is "".]
-  [e.g., The source is "2018-UNK-UNK", The value is "2018-??-??".]
-  [e.g., The source is "2018-UNK-12", The value is "2018-??-??".]
-  [e.g., The source is "2018-09-UK, The value is "2018-09-??".]
-  [e.g., The source is "2018-09, The value is "2018-09-??".]
-  [e.g., The source is "UNK-UNK-12", The value is "".]
-  [e.g., The source is "2018/2/1", The value is "2018/02/01".]
- [e.g., The source is "_/_/_", The value is "".]
- [e.g., The source is "____/____/____", The value is "".]

**文本字段**
- 如果包含空格则需要移除
-  [e.g., The source is "维生素 C 注射液", The value is "维生素C注射液".]
-  [e.g., The source is " 维生素A注射液", The value is "维生素A注射液".]
-  [e.g., The source is "维生素D注射液 ", The value is "维生素D注射液".]
-  [e.g., The source is "维生素F注射液", The value is "维生素F注射液".]

**英文缩写**
- “英文缩写”是所有需要提取的字段中出现的英文缩写，如仅有中文全称不需要提取，如下列举了多个可能出现的“英文缩写”及其“中文全称”：
	“C1”=“第1周期”；
	“D2”=“第2天”；
	“C3D1”=“第3周期第1天”；
	“q2w”=“每2周1次”；
	“q8h”=“每8小时1次”；
	“q4d”=“每4天1次”
	“once”=“单次给药”；
	“qd”=“每日1次”；
	“bid”=“每日2次”；
	“tid”=“每日3次”；
	“po”=“口服”；
	“ig”=“灌胃”；
	“iv drip”=“静脉滴注”；
	“ivgtt”=“静脉滴注”；
	“iv”=“静脉注射”；
	“im”=“肌肉注射”；
	“ip”=“腹腔注射”；
	“ih”=“皮下注射”；
	“CS”=“有临床意义”；
	“St”=“立即”；
	“prn”=“按需”；

9、如果原始数据为空不要随意推测治疗用药，直接返回空的json对象节点即可。