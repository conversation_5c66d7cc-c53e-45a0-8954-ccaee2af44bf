**不良反应事件报告内容：**
```
{PDF报告识别的结果}
```

我是临床试验研究员，负责记录和总结临床试验受试者的不良事件（AE）、严重不良事件（SAE）、临床关注事件（ECI）和特别关注不良事件（AESI）以及合并用药史。

现在需要将此事件报告概括为临床个例报告描述（Clinical ICSRs），请遵循以下规则：

---

#### 基本规范 ####

1. **输出必须严格按照提供的模板格式生成，不可增加或改变结构**
2. 输出为JSON格式：
   ```json
   {
     "模块": "合并用药",
     "正文": "<合并用药合并文本>"
   }
   ```

---

#### 数据提取规则 ####

- 从报告中"报告模块": "合并用药"的部分提取数据
- 提取每条记录的"药物名称"、"适应症"、"剂量"、"剂量单位"、"频率"、"给药途径"、"开始日期"、"结束日期"和"是否继续"字段
- 如果没有找到任何用药记录，则使用"受试者无合并用药"模板

---

#### 日期处理规则 ####

**1. 日期标准格式**
- 所有日期输出格式为："YYYY年MM月DD日"
- 月份和日期如为个位数必须补零，如"01"而非"1"
- 示例：2024-7-5 应转换为 2024年07月05日

**2. 部分日期处理**
- 当日期中包含"?"（如"2025-02-??"）：
    * 日部分为"??"：只使用年月部分，如"2025年02月"
    * 月部分为"??"：只使用年部分，如"2025年"
    * 年部分包含"?"：视为日期完全未知

**3. 时间描述生成**
根据可用的日期信息，生成以下格式的时间描述：
- **单一日期**：开始和结束日期相同且都已知
  格式：`YYYY年MM月DD日`
- **固定区间**：开始和结束日期都已知且不同
  格式：`开始日期至结束日期`
- **持续事件**：有开始日期，无结束日期，"是否继续"为"是"
  格式：`开始日期起持续`
- **未知开始有结束**：开始日期未知但有结束日期
  格式：`未知日期至结束日期`
- **完全未知但持续**：开始和结束日期均未知，但"是否继续"为"是"
  格式：`未知日期起持续`
- **完全未知不持续**：所有时间信息均未知且"是否继续"不是"是"
  格式：`时间未报告`

---

#### 用药格式规则 ####

**1. 基本格式**

单个药物记录格式：
```
<药物名称>（时间：<用药时间>，适应症：<适应症>，剂量/频率：<剂量><剂量单位>，<频率>，<给药途径>）
```

**2. 特殊规则**

- **缺失字段处理**：如果某字段缺失，完全省略该字段及其标签（例如，无适应症时不显示"适应症："）
- **英文缩写**：频率和给药途径如为英文缩写，统一用小写表示（如"qd"而非"QD"）
- **按需剂量**：如果剂量被描述为"按需"，直接使用"按需"替代具体剂量值

**3. 整体结构**
- 有用药记录：`受试者的合并用药包括：<用药列表>。`
- 无用药记录：`受试者无合并用药。`
- 用药数据不明确：`受试者合并用药未报告。`

**4. 示例**
```
受试者的合并用药包括：缬沙坦（时间：未知日期起持续，适应症：高血压，剂量/频率：80mg，qd，po），氨氯地平（时间：2025年02月起持续，适应症：高血压，剂量/频率：5mg，qd，po），扑热息痛（时间：2025年02月17日，剂量/频率：500mg，按需，po）。
```

---

#### 处理流程 ####

1. **数据提取与预处理**：
    - 提取所有"合并用药"相关信息
    - 应用日期处理规则，生成标准时间描述
    - 将频率和给药途径的英文缩写转为小写
    - 特殊处理"按需"剂量

2. **格式化输出**：
    - 按照基本格式为每个药物生成记录
    - 确保每个药物记录只包含存在的字段
    - 用逗号分隔不同药物记录
    - 添加句号结束描述

3. **最终输出**：按指定JSON格式输出结果