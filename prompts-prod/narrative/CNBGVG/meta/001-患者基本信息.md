**不良反应事件报告内容：**
```
{PDF报告识别的结果}
```

我是临床试验研究员，负责记录和总结临床试验中受试者发生的不良事件（AE）和严重不良事件（SAE），包括受试者的病史、合并用药、研究药物使用情况、不良事件的临床过程描述、接受的治疗以及事件的转归。

现在需要将本次事件报告概括为完整的临床个例报告描述（Clinical ICSRs），每个模块都需要体现在描述中，要求如下：

1. **输出结果中的"正文"必须严格按照"模板格式"生成，不能增加模板中不存在的内容。**
2. 模板格式中的"`<>`"代表参数，需要在事件报告中提取相应的值进行替换。
3. 模板格式中的"`[例如：.......]`"是示例说明，供参考样式，生成正文时请删除。
4. 模板格式中的"`如果.......`"是条件判断，满足条件时才参考其中的模板格式生成。
5. 模板格式中的"`（说明：.......）`"为说明性文字，对前面内容的解释和补充，生成正文时请删除。
6. **日期格式要求：月份和日为个位数时，需在前面加"0"，例如"2024年07月05日"。**
7. 如果模板中"`<>`"的参数在报告内容中未提及，则该参数置空。
8. **输出结果为 JSON 格式，格式如下：**
   ```json
   {
     "试验方案编号": "<试验方案编号>",
     "模块": "患者信息",
     "试验药物首次用药日期": "<试验药物首次用药日期>",
     "SAE发生日期": "<SAE发生日期>",
     "严重不良事件（SAE）": ["<症状/疾病>", "<开始日期>", "<结束日期>"],
     "不良事件（AE）": ["<症状/疾病>", "<开始日期>", "<结束日期>"],
     "正文": "<正文>"
   }
   ```
   **请不要输出"模板格式"以外的内容。**
9. **根据"试验方案编号"和以下对应关系，生成对应的"试验方案和研究药物受理号"：**
   ```
   <试验方案编号>    <试验方案和研究药物受理号>
   ${study_id_and_study_num}
   ```

---

#### 模板格式 ####

**正文需要分为三段输出，段落之间需要换行**

**第一段：试验方案基本信息**
```
这是一份来自"<试验方案名称>"（方案编号：<试验方案编号>）的严重不良事件（SAE）报告。
```
如果有研究药物具有受理号，则添加：
```
该研究药物<研究药物名称>的受理号包括：<研究药物受理号>。
```

**第二段：第二个研究药物受理号信息（仅当第二个研究药物有受理号时输出）**
如果有第二个研究药物并且该药物具有受理号（不为"无"），则添加：
```
<研究药物2名称>的受理号包括：<研究药物2受理号>。
```

**第三段：受试者信息**
```
一名<年龄>岁<民族><性别>受试者（受试者编号：<受试者编号>；身高：<身高>cm；体重：<体重>kg）于<签署知情同意书日期>签署知情同意书。受试者经筛选符合入选标准，不符合排除标准。<首次用药日期>，受试者首次接受<试验用药1名称>（规格：<试验用药1规格>）（<首次用药1剂量数值><首次用药1剂量单位>，剂量水平<试验用药1剂量组>，<首次用药1给药途径>，<首次用药1用药频率>，批号：<试验用药1批号>）和<试验用药2名称>（剂型：<试验用药2剂型>，规格：<试验用药2规格>）（<首次用药2剂量数值><首次用药2剂量单位>，剂量水平 <试验用药2剂量组>，<首次用药2给药途径>，<首次用药2用药频率>，批号：<试验用药2批号>）第1周期第1天（C1D1）给药。SAE发生前最近一次<试验用药1名称>和<试验用药2名称>给药日期均为<最近一次用药日期>。
```

**说明：**

- 请根据原始数据提取相应的变量填充到模板中。
- **年龄的计算方法：**
	- 如果出生日期和事件发生日期均有具体的年、月、日，则年龄 = 发生日期 - 出生日期，结果精确到年，年龄格式为"X"；
	- 如果只有年、月，则计算年龄精确到月，年龄 = (发生年份 - 出生年份) 年 + (发生月份 - 出生月份) 个月，年龄格式为"X"；
	- 如果只有年份，则年龄 = 发生年份 - 出生年份，年龄格式为"X"。
- 日期格式需符合要求，确保月份和日为两位数字。
- **每段内容之间必须使用空行分隔，确保三段内容按照要求清晰地展示。**
- **只有当研究药物明确有受理号且受理号不为"无"时，才在报告中显示该药物的受理号。**