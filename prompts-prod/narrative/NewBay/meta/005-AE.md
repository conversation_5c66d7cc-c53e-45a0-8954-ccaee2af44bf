**不良反应事件报告内容：**
```
{PDF报告识别的结果}
```

我是临床试验研究员，负责记录和总结临床试验受试者的不良事件（AE）、严重不良事件（SAE）、临床关注事件（ECI）和特别关注不良事件（AESI）。

现在需要将此事件报告概括为临床个例报告描述（Clinical ICSRs），请遵循以下规则：

---

#### 基本规范 ####

1. **输出必须严格按照提供的模板格式生成，不可增加或改变结构**
2. 输出为JSON格式：
   ```json
   {
     "模块": "不良事件（AE）",
     "正文": "<不良事件（AE）合并文本>"
   }
   ```

---

#### 数据提取规则 ####

- 从报告中"报告模块": "不良事件（AE）"的部分提取数据
- 提取每条记录的"疾病名称"、"开始日期"、"结束日期"和"是否继续"字段
- 如果没有找到任何疾病记录，则使用"受试者无不良事件（AE）"模板

---

#### 日期处理规则 ####

**1. 日期标准格式**
- 所有日期输出格式为："YYYY年MM月DD日"
- 月份和日期如为个位数必须补零，如"01"而非"1"
- 示例：2024-7-5 应转换为 2024年07月05日

**2. 部分日期处理**
- 当日期中包含"?"或日期不完整时：
    - 只保留已知部分，缺失的部分用"未知日期"替代
    - 示例："2025-??-??" 转换为 "2025年未知日期"
    - 示例："2025-02-??" 转换为 "2025年02月未知日期"

**3. 时间描述生成**
根据可用的日期信息，生成以下格式的时间描述：
- **单一日期**：开始和结束日期相同且都已知
  格式：`YYYY年MM月DD日`
- **固定区间**：开始和结束日期都已知且不同
  格式：`YYYY年MM月DD日至YYYY年MM月DD日`
- **持续事件(开始日期已知)**：有开始日期，无结束日期，"是否继续"为"是"或"继续"
  格式：`自YYYY年MM月DD日起`
- **持续事件(开始日期未知)**：开始日期为空或包含"?"，无结束日期或结束日期为空，且"是否继续"为"是"或"继续"
  格式：`自未知日期起`
  示例：当开始日期为空白、"??"或部分缺失，且是继续时，使用"自未知日期起"
- **未知开始有结束**：开始日期未知但有结束日期
  格式：`至YYYY年MM月DD日`
- **完全未知且非持续**：开始和结束日期均未知，且"是否继续"不为"是"或"继续"
  格式：`时间未报告`

---

#### 疾病分组规则 ####

- 根据疾病的开始日期进行分组
- 具有相同开始日期的疾病归为同一组
- 按原始报告顺序（不重新排序）处理各时间组

---

#### 疾病持续状态处理 ####

- 判断疾病是否持续的规则：
    * 如果"结束日期"为空或未知，并且"是否继续"为"是"，则该疾病为"持续中"
    * 否则，疾病为非持续状态
- 对于同一日期组内的疾病：
    * 如果该组内**所有**疾病都是持续中，则在整个疾病列表后添加"，均持续中"
    * 如果只有部分疾病持续中，则仅在持续中的疾病名称后添加"，持续中"
    * 如果该组内没有持续中的疾病，则不添加持续状态说明

---

#### 输出格式规则 ####

**1. 整体结构**
- 有疾病记录：`受试者的不良事件（AE）包括：<日期组>。`
- 无疾病记录：`受试者无不良事件（AE）。`
- 疾病数据不明确：`受试者不良事件（AE）未报告。`

**2. 日期组格式**
- 基本格式：`<开始日期>，受试者发生AE<疾病列表>[，均持续中]`
- 疾病列表格式化规则：
    * 单个疾病，持续中：`<疾病名称>，持续中`
    * 单个疾病，非持续中：`<疾病名称>`
    * 多个疾病，全部持续中：`<疾病1>，<疾病2>，...，<疾病n-1>和<疾病n>，均持续中`
    * 多个疾病，部分持续中：仅在持续中的疾病名称后添加"，持续中"，如`<疾病1>，<疾病2>，持续中，...，<疾病n-1>和<疾病n>`
    * 多个疾病，全部非持续中：`<疾病1>，<疾病2>，...，<疾病n-1>和<疾病n>`
- 不同日期组之间用句号"。"分隔
- 示例：
  ```
  受试者的不良事件（AE）包括：2025年02月17日，受试者发生AE高血压，持续中。2025年02月20日，受试者发生AE便秘，低白蛋白血症和γ-谷氨酰基转移酶增高，均持续中。
  ```

---

#### 处理流程 ####

1. **数据提取**：
    - 提取所有"不良事件（AE）"相关信息

2. **日期处理**：
    - 处理每条疾病记录的开始日期，生成标准格式
    - 确定每个疾病是否持续中

3. **分组和格式化**：
    - 按照开始日期对疾病进行分组
    - 对每个时间组：
        * 检查该组内所有疾病是否都是持续中
        * 如果全部持续中：在整个疾病列表后添加"，均持续中"
        * 如果部分持续中：只为持续中的疾病单独添加"，持续中"
    - 应用疾病列表格式化规则（最后两个用"和"连接）
    - 组织格式为`<开始日期>，受试者发生AE<经处理的疾病列表>[，均持续中]`

4. **文本生成**：
    - 连接所有时间组描述，使用句号分隔

5. **输出结果**：
    - 按指定JSON格式输出最终结果