不良反应事件报告内容：
"""
{PDF报告识别的结果}
"""
我是一个临床试验的研究员，日常工作是记录和总结临床试验中受试者发生的不良事件（AE）、严重不良事件（SAE）、临床关注事件（ECI）和特别关注不良事件（AESI），包括但不限于受试者的病史、合并用药、研究药物使用情况、不良事件的临床过程描述、接受的治疗以及事件的转归。
现在需要将本次事件报告概括为完整的临床个例报告描述（Clinical ICSRs），每个模块都需要体现在描述中，要求：
1、输出结果中的“正文”必须严格按照“模板格式”生成，不能增加“模板格式”中不存在的内容；
2、模板格式中的“<>”代表是一个参数，需要在事件报告中提取相应的值进行替换；
3、模板格式中的“[例如：.......]”是对生产内容的举例说明，请参考其中的示例样式进行生成，输出正文时需删除；
4、模板格式中的“如果.......”是条件判断，当满足条件时，才能参考其中的模板格式进行生成；
5、模板格式中的“（说明：.......）”是说明性文字，对前面内容的说明和补充，输出正文时需删除；
6、输出结果的日期中的月份和日，如月份和日都是个位数，须在数字前面加“0”，如“2024年07月05日”；
7、如模板格式中“<>”代表的参数在报告内容中未提及，则该参数置空；
8、输出结果为json格式，要求输出：
	{
		"试验方案编号":"<试验方案编号>", 
		"模块":"患者信息", 
		"试验药物首次用药日期":"<试验药物首次用药日期>", 
		"SAE/ECI/AESI发生日期":"<SAE/ECI/AESI发生日期>", 
		"严重不良事件（SAE）":["<症状/疾病>"、"<开始日期>"、"<结束日期>"], 
		"不良事件（AE）":["<症状/疾病>"、"<开始日期>"、"<结束日期>"], 
		"正文":"<正文>"（请不要输出“模板格式”以外的内容）
	}；
9、<试验方案编号>和<试验方案和研究药物受理号>的对应关系如下，需根据<试验方案编号>生成对应的<试验方案和研究药物受理号>：
<试验方案编号>	<试验方案和研究药物受理号>
${study_id_and_study_num}

#### 模板格式 ####
如果受试者仅出现了严重不良事件（SAE），则描述为：受试者（筛选号：<筛选号/ID>）为一名<年龄>岁的<民族/种族><男性/女性>，于YYYY年MM月DD日被诊断为<试验药物的适应症>。于YYYY年MM月DD日自愿签署知情同意书“<试验方案名称>”（方案编号：<试验方案编号>）。
[例如：受试者（筛选号：28006）为一名55岁汉族男性，于2023年11月21日被诊断为肺癌。于2023年11月29日自愿签署知情同意书“评估TY-9591片对比奥希替尼一线治疗EGFR敏感突变的局部晚期或转移性非小细胞肺癌受试者的疗效和安全性的随机、双盲、多中心III期临床研究”（方案编号：TYKM1601301）。] 
如果受试者出现了严重不良事件（SAE），又出现了不良事件（AE），则描述为：受试者（筛选号：<筛选号/ID>）为一名<年龄>岁的<民族/种族><男性/女性>，于YYYY年MM月DD日被诊断为<试验药物的适应症>。于YYYY年MM月DD日自愿签署知情同意书“<试验方案名称>”（方案编号：<试验方案编号>）。
[例如：受试者（筛选号：28006）为一名55岁汉族男性，于2023年11月21日被诊断为肺癌。于2023年11月29日自愿签署知情同意书“评估TY-9591片对比奥希替尼一线治疗EGFR敏感突变的局部晚期或转移性非小细胞肺癌受试者的疗效和安全性的随机、双盲、多中心III期临床研究”（方案编号：TYKM1601301）。] 
