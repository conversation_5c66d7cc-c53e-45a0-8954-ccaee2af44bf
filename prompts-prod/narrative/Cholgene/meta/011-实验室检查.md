不良反应事件报告内容：
"""
{PDF报告识别的结果}
"""

我是一个临床试验的研究员，日常工作是记录和总结临床试验中受试者发生的不良事件（AE）、严重不良事件（SAE）、临床关注事件（ECI）和特别关注不良事件（AESI），包括但不限于受试者的病史、合并用药、研究药物使用情况、不良事件的临床过程描述、接受的治疗以及事件的转归。

现在需要将本次事件报告概括为完整的临床个例报告描述（Clinical ICSRs），每个模块都需要体现在描述中，要求：

1. **输出结果中的"正文"必须严格按照"模板格式"生成，不能增加"模板格式"中不存在的内容。**

2. **模板格式中的"<>"代表参数，需要在事件报告中提取相应的值进行替换。**

3. **模板格式中的"[例如：...]"是示例，请参考其中的示例样式进行生成，输出正文时需删除示例部分。**

4. **模板格式中的"如果...，则..."是条件判断，当满足条件时，参考其中的模板格式进行生成。**

5. **模板格式中的"（说明：...）"是说明性文字，对前面内容的说明和补充，输出正文时需删除。**

6. **输出结果的日期中的月份和日，如为个位数，须在数字前面加"0"，如"2024年07月05日"。**

7. **如模板格式中"<>"代表的参数在报告内容中未提及，则该参数置空。**

8. **"实验室检查项目"需分别从"报告模块"是"与事件相关的实验室检查"和"SAE/ECI/AESI的描述"中提取。**

9. **"实验室检查分类"包括但不限于：血常规、尿常规、粪常规、计算机断层扫描(CT)、心电图(ECG)、脑电图(EEG)、磁共振成像(MRI)、肝功能、肾功能、甲状腺功能、血脂、血糖等。**

10. **"实验室检查项目"是"实验室检查分类"下的具体检查项目，如血常规包括但不限于：红细胞计数、白细胞计数、血红蛋白等。**

11. **所有实验室检查信息必须按照检查日期升序排列，并合并为一条"正文"。**

12. **如"正常值范围"字段为空，则不需要在"正文"中生成"（<结果是否正常>）（正常值范围：<正常值下限>-<正常值上限>）"。**

13. **如果存在"合并检查"，表示在同一"检查日期"有多个不同时间点的检查结果，需要按照时间顺序连续输出每个时间点的检查结果。**

14. **相同日期的检查信息必须合并，将日期放在开头，随后列出该日期下的所有检查结果。**

15. **输出结果为JSON格式，格式如下：**

```json
{
  "模块": "与事件相关的实验室检查",
  "正文": "<所有实验室检查信息合并的单条正文>"
}
```

16. **当替换`<结果是否正常>`参数时，应仅提取具体的结果值，如"正常"或"异常"，不包含"结果是否正常："等前缀或其他附加信息。**

17. **检查项目名称与检查结果之间不应有空格，检查结果和检查单位应紧密连接，无空格。**

18. **当检查结果为数字，且检查单位中也包含数字时，必须使用*号连接检查结果和单位，以避免混淆。** 例如，检查结果为"47"，单位为"10^9/L"，则输出应为"47*10^9/L"，而非"47 10^9/L"或"4710^9/L"。

19. **如果检查项目名称为英文缩写，按照药物警戒领域的标准处理：首次出现时使用"中文标准名称（英文缩写）"格式，后续再次出现相同缩写时仅使用英文缩写。** 常见生命体征与实验室检查缩写对照：
- T - 体温
- P - 心率
- R - 呼吸
- BP - 血压
- HR - 心率
- SpO2 - 血氧饱和度
- RBC - 红细胞计数
- WBC - 白细胞计数
- PLT - 血小板计数
- Hb/HGB - 血红蛋白
- ALT - 丙氨酸氨基转移酶
- AST - 天门冬氨酸氨基转移酶
- BUN - 尿素氮
- Cr - 肌酐
- Glu - 血糖
- K - 钾
- Na - 钠
- Cl - 氯
- Ca - 钙
  如果遇到未列出的英文缩写，需基于药物警戒领域的专业知识正确转换为对应的标准中文名称。

#### 模板格式

- **所有实验室检查信息应按日期分组，合并描述为一条正文：**

  ```
  受试者的实验室检查结果包括：<检查日期1>，<实验室检查项目1><检查结果1><检查结果单位1>（<结果是否正常1>）（正常值范围：<正常值下限1>-<正常值上限1>），<实验室检查项目2><检查结果2><检查结果单位2>（<结果是否正常2>）（正常值范围：<正常值下限2>-<正常值上限2>）。<检查日期2>，<实验室检查项目3><检查结果3><检查结果单位3>（<结果是否正常3>）（正常值范围：<正常值下限3>-<正常值上限3>），...。
  ```

  [例如：受试者的实验室检查结果包括：2024年04月04日，体温(T)38.5℃，脉搏(P)96次/分，血压(BP)120/80mmHg，呼吸(R)20次/分，白细胞计数(WBC)2.98*10^9/L（偏低）（正常值范围：3.5-9.5），血红蛋白(Hb)98g/L（偏低）（正常值范围：110-150）。2024年04月05日，T37.5℃，P90次/分，BP118/78mmHg，R18次/分，WBC3.20*10^9/L（偏低）（正常值范围：3.5-9.5），Hb102g/L（偏低）（正常值范围：110-150）。]

- **如果检查结果是文字描述，则使用"示："连接：**

  ```
  ...<实验室检查项目>示：<检查结果>（<结果是否正常>）（正常值范围：<正常值下限>-<正常值上限>），...
  ```

  [例如：受试者的实验室检查结果包括：2024年04月08日，计算机断层扫描(CT)示：1.左下肺癌术后改变，左侧少量胸腔积液较前减少；2.脂肪肝同前，肝内多个结节，考虑转移瘤可能性大（异常）。2024年04月09日，粪便颜色示：红色（异常）（正常值范围：棕黄色）。]

- **如果存在"合并检查"，按照时间点依次列出：**

  ```
  ...<检查日期>，<时间点1>，<检查项目1><检查结果1>（<结果是否正常1>）（正常值范围：<正常值下限1>-<正常值上限1>），<检查项目2><检查结果2>（<结果是否正常2>）（正常值范围：<正常值下限2>-<正常值上限2>）。<时间点2>，<检查项目1><检查结果1>（<结果是否正常1>）（正常值范围：<正常值下限1>-<正常值上限1>），<检查项目2><检查结果2>（<结果是否正常2>）（正常值范围：<正常值下限2>-<正常值上限2>）...。
  ```

  [例如：受试者的实验室检查结果包括：2024年12月31日，15:02，体温(T)38.5℃，脉搏(P)94次/分，血压(BP)178/104mmHg，呼吸(R)21次/分，血氧饱和度(SpO2)96%。15:20，T37.8℃，P94次/分，BP177/93mmHg，R16次/分，SpO299%。15:39，T37.0℃，P97次/分，BP167/82mmHg，R16次/分，SpO2100%，憋气缓解，颈部锁区红色凸起皮疹消退。]

- **如果报告中未提及实验室检查，描述为：**

  ```
  未报告与事件相关的实验室检查。
  ```

- **如果受试者未进行实验室检查，描述为：**

  ```
  受试者未进行与事件相关的实验室检查。
  ```

**检查日期的规则：**

**1. 日期标准格式**
- 所有日期输出格式为："YYYY年MM月DD日"
- 月份和日期如为个位数必须补零，如"01"而非"1"
- 示例：2024-7-5 应转换为 2024年07月05日

**2. 部分日期处理**
- 当日期中包含"?"（如"2025-02-??"）或部分信息缺失时：
  * 日部分为"??"或缺失：只使用年月部分，如"2025年02月"
  * 月部分为"??"或缺失：只使用年部分，如"2025年"
  * 年部分包含"?"或缺失：视为日期完全未知，使用"时间未报告"

**3. 时间描述生成**
根据可用的日期信息，生成相应的时间描述：
- **完整日期**：年月日信息完整
  格式：`YYYY年MM月DD日`
- **部分日期**：根据部分日期处理规则显示可用部分
- **日期带时间**：当检查记录包含具体时间点时
  格式：`YYYY年MM月DD日 HH:MM`
- **未知日期**：时间信息完全缺失时
  格式：`时间未报告`
- **同一日期多时间点**：当同一日期有多个检查时间点时
  格式：`YYYY年MM月DD日，HH:MM，[检查结果]。HH:MM，[检查结果]...`

**注意：**

- **所有实验室检查信息需合并在一条"正文"中，"受试者的实验室检查结果包括："前缀只出现一次。**

- **"正文"中的描述应按照"模板格式"生成，整个"正文"以句号"。"结束。**

- **具有相同检查日期的检查结果必须合并，并将日期放在检查结果之前。**

- **不同检查日期的检查结果之间用句号"。"分隔。**

- **同一检查日期内的不同检查项目之间用逗号"，"分隔。**

- **如"正常值范围"未提及，不需要在括号中添加该信息，但如有"结果是否正常"信息，仍应在单独的括号中显示。**

- **如检查结果为文字描述，使用"示："连接检查项目与结果，否则直接连接，不加空格。**

- **对于英文缩写的检查项目，首次出现时使用"中文标准名称(英文缩写)"的格式，后续相同的缩写只使用缩写本身，遵循药物警戒领域的专业标准。**