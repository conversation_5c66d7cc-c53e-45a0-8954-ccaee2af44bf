不良反应事件报告内容：
"""
{PDF报告识别的结果}
"""
我是一个临床试验的研究员，日常工作是记录和总结临床试验中受试者发生的不良事件（AE）、严重不良事件（SAE）、临床关注事件（ECI）和特别关注不良事件（AESI），包括但不限于受试者的病史、合并用药、研究药物使用情况、不良事件的临床过程描述、接受的治疗以及事件的转归。

现在需要将本次事件报告概括为完整的临床个例报告描述（Clinical ICSRs），**请确保报告中所有相关信息都被完整地提取和包括在内，每个模块都需要体现在描述中**，要求：

1. 输出结果中的“正文”必须严格按照“模板格式”生成，不能增加“模板格式”中不存在的内容，也不能遗漏报告内容中的任何相关信息；
2. 模板格式中的“<>”代表是一个参数，需要在事件报告中提取相应的值进行替换；
3. 模板格式中的“[例如：.......]”是对生成内容的举例说明，请参考其中的示例样式进行生成，输出正文时需删除这些举例内容；
4. 模板格式中的“如果.......”是条件判断，当满足条件时，才能参考其中的模板格式进行生成；
5. 模板格式中的“（说明：.......）”是说明性文字，对前面内容的说明和补充，输出正文时需删除这些说明文字；
6. 输出结果的日期中的月份和日，如月份和日都是个位数，须在数字前面加“0”，如“2024年07月05日”；
7. 如模板格式中“<>”代表的参数在报告内容中未提及，则该参数置空；但如果报告内容中有相关信息，请务必提取并填写，不要遗漏任何信息；
8. **报告中的“SAE/ECI/AESI的描述”就是“事件临床过程”，必须完整地保留原文内容，不得进行删减或改写**；
9. “正文”中的所有标点符号和括号都要用中文的标点符号和括号生成；
10. “正文”中的所有“患者”字样都要替换为“受试者”；
11. “正文”中的所有日期都要按照“YYYY年MM月DD日”的格式生成，如“2024年07月05日”；
12. “正文”中的所有日期中出现的“UK”或“UN”需要删除，如“2024年07月UK”替换为“2024年07月”；
13. 输出结果为json格式，要求输出：
	{
	"试验方案编号":"<试验方案编号>",
	"模块":"SAE/ECI/AESI的描述",
	"正文":"<正文>"（请不要输出“模板格式”以外的内容）
	}；
14. 正文中的中文字符与紧邻的英文字符（包括英文字母、数字和符号）之间不应有空格，请统一格式。
15. 如应用特瑞普利单抗注射液，则需撰写其全部受理号；如没有应用特瑞普利单抗注射液，则不用撰写。

#### 模板格式 ####

SAE/ECI/AESI的描述：'''
<SAE/ECI/AESI的描述>
'''