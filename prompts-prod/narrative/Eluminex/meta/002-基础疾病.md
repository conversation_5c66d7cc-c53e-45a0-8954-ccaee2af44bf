不良反应事件报告内容：
"""
{PDF报告识别的结果}
"""
我是一个临床试验的研究员，日常工作是记录和总结临床试验中受试者发生的不良事件（AE）、严重不良事件（SAE）、临床关注事件（ECI）和特别关注不良事件（AESI），包括但不限于受试者的病史、合并用药、研究药物使用情况、不良事件的临床过程描述、接受的治疗以及事件的转归。
现在需要将本次事件报告概括为完整的临床个例报告描述（Clinical ICSRs），每个模块都需要体现在描述中，要求：
1、输出结果中的"正文"必须严格按照"模板格式"生成，不能增加"模板格式"中不存在的内容；
2、模板格式中的"<>"代表是一个参数，需要在事件报告中提取相应的值进行替换；
3、模板格式中的"[例如：.......]"是对生产内容的举例说明，请参考其中的示例样式进行生成，输出正文时需删除；
4、模板格式中的"如果......."是条件判断，当满足条件时，才能参考其中的模板格式进行生成；
5、模板格式中的"（说明：.......）"是说明性文字，对前面内容的说明和补充，输出正文时需删除；
6、输出结果的日期中的月份和日，如月份和日都是个位数，须在数字前面加"0"，如"2024年07月05日"；
7、如模板格式中"<>"代表的参数在报告内容中未提及，则该参数置空；
8、模板格式中的"基础疾病"必须严格按照以下优先级提取：
a. 首先从"试验用药基础信息"中的"试验药物的适应症"字段提取明确标注的适应症
b. 如果a未找到，则从"SAE/ECI/AESI的描述"文本中寻找明确描述受试者"因为某疾病参加临床试验"或类似表述的内容
c. 如果b未找到，则为方案设计的适应症"${studyIndication}"
d. 如果上述三个来源都未找到明确的基础疾病，则"基础疾病"字段输出"无法确定"，正文则为"无法从报告中确定受试者参加临床试验的基础疾病。"
e. 重要：绝对不要将"合并疾病"、"现病史"、"既往病史"中列出的疾病自动视为基础疾病，除非它们在报告中被明确描述为受试者参加试验的原因

例子说明：
- 如果报告中写"患者因肺癌参加XX临床试验"，则基础疾病为"肺癌"
- 如果报告中只列出"患者有高血压、糖尿病、肺癌病史"，但没有指明哪个是参加试验的原因，则输出"无法确定"
- 如果"试验药物的适应症"字段填写为"晚期实体瘤"，则基础疾病为"晚期实体瘤"

9、输出结果为json格式，要求输出：
	{
		"试验方案编号":"<试验方案编号>", 
		"模块":"基础疾病", 
		"基础疾病":"<基础疾病>", 
		"正文":"<正文>"（请不要输出“模板格式”以外的内容）
	}；

#### 模板格式 ####
如果能确定基础疾病：
受试者基础疾病为<基础疾病>。
[例如：受试者基础疾病为晚期实体瘤。]
如果受试者的基础疾病有多种，且这些疾病都明确是受试者参加临床试验的原因，则描述为：受试者基础疾病包括：<基础疾病>……
[例如：受试者基础疾病包括：晚期实体瘤、乳腺癌。]

如果无法确定基础疾病：
无法从报告中确定受试者参加临床试验的基础疾病。