不良反应事件报告内容：
"""
{PDF报告识别的结果}
"""

我是一个临床试验的研究员，日常工作是记录和总结临床试验中受试者发生的不良事件（AE）、严重不良事件（SAE）、临床关注事件（ECI）和特别关注不良事件（AESI），包括但不限于受试者的病史、合并用药、研究药物使用情况、不良事件的临床过程描述、接受的治疗以及事件的转归。

现在需要将本次事件报告概括为完整的临床个例报告描述（Clinical ICSRs），每个模块都需要体现在描述中，要求：

1. **输出结果中的"正文"必须严格按照"模板格式"生成，不能增加"模板格式"中不存在的内容。**

2. **模板格式中的"<>"代表参数，需要在事件报告中提取相应的值进行替换。**

3. **模板格式中的"[例如：...]"是示例，请参考其中的示例样式进行生成，输出正文时需删除示例部分。**

4. **模板格式中的"如果...，则..."是条件判断，当满足条件时，参考其中的模板格式进行生成。**

5. **模板格式中的"（说明：...）"是说明性文字，对前面内容的说明和补充，输出正文时需删除。**

6. **输出结果的日期中的月份和日，如为个位数，须在数字前面加"0"，例如"2024年07月05日"。**

7. **如模板格式中"<>"代表的参数在报告内容中未提及，则该参数置空。**

8. **"实验室检查项目"需分别从"报告模块"是"与事件相关的实验室检查"和"SAE/ECI/AESI的描述"中提取。**

9. **"实验室检查分类"包括但不限于：血常规、尿常规、粪常规、计算机断层扫描(CT)、心电图(ECG)、脑电图(EEG)、磁共振成像(MRI)、肝功能、肾功能、甲状腺功能、血脂、血糖等。**

10. **"实验室检查项目"是"实验室检查分类"下的具体检查项目，如血常规包括但不限于：红细胞计数、白细胞计数、血红蛋白等。**

11. **所有实验室检查信息必须按照检查日期升序排列，并合并为一条"正文"；但每条检查结果须独立换行，格式详见下文“模板格式”。**

12. **如"正常值范围"字段为空，则不需要在"正文"中生成"正常值范围：<正常值下限>-<正常值上限>"相关内容；若不为空，则必须完整输出上下限及原始单位。**

13. **如果存在"合并检查"，表示在同一"检查日期"有多个不同时间点的检查结果，需要按照时间顺序连续输出每个时间点的检查结果。**

14. **不同检查日期不再合并于同一句；每条检查结果单独成行，统一以“<实验室检查项目>（YYYY-MM-DD）：<检查结果><单位>，正常值范围：<正常值下限><单位> -<正常值上限><单位>。”描述，但仍须保证整体日期顺序升序。**

15. **输出结果为 JSON 格式，格式如下：**

```json
{
  "模块": "与事件相关的实验室检查",
  "正文": "<所有实验室检查信息合并的单条正文>"
}
```

16. **原始模板中的 `<结果是否正常>` 字段在本次重定义的模板中不再使用，可忽略。**

17. **当检查结果为数字，且检查单位中也包含数字时，必须使用“*”号连接检查结果和单位，且“<实验室检查项目>”与“<检查结果>”之间留一个半角空格。**  
    例如，检查结果为"47"，单位为"10^9/L"，则输出应为"血小板 47*10^9 /L"，而非"血小板 47 10^9/L"或"血小板47*10^9/L"。

18. **如果检查项目名称为英文缩写，按照药物警戒领域的标准处理：首次出现时使用"中文标准名称（英文缩写）"格式，后续再次出现相同缩写时仅使用英文缩写。**  
    常见生命体征与实验室检查缩写对照：
- T - 体温
- P - 心率
- R - 呼吸
- BP - 血压
- HR - 心率
- SpO2 - 血氧饱和度
- RBC - 红细胞计数
- WBC - 白细胞计数
- PLT - 血小板计数
- Hb/HGB - 血红蛋白
- ALT - 丙氨酸氨基转移酶
- AST - 天门冬氨酸氨基转移酶
- BUN - 尿素氮
- Cr - 肌酐
- Glu - 血糖
- K - 钾
- Na - 钠
- Cl - 氯
- Ca - 钙  
  如果遇到未列出的英文缩写，需基于药物警戒领域的专业知识正确转换为对应的标准中文名称。

19. **当原始单位为"*109/L"、"*10^9/L"或其他指数形式时，输出统一写作"*10^9 /L"（在“10^9”与“/L”之间保留一个半角空格）。**

20. **如检查结果存在无意义的尾随"0"（例如"64.00"），在不影响精度的前提下删除无意义的"0"，输出为"64"。**

#### 模板格式

```
实验室检查包括 ：
<实验室检查项目1>（<检查日期1>）：<检查结果1><单位1>，正常值范围：<正常值下限1><单位1> -<正常值上限1><单位1>。
<实验室检查项目2>（<检查日期2>）：<检查结果2><单位2>，正常值范围：<正常值下限2><单位2> -<正常值上限2><单位2>。
<实验室检查项目3>（<检查日期3>）：<检查结果3><单位3>，正常值范围：<正常值下限3><单位3> -<正常值上限3><单位3>。
...
```

[例如：  
实验室检查包括 ：  
血小板（2024-09-26）：65*10^9 /L，正常值范围：100*10^9 /L -300*10^9 /L。  
血小板（2024-09-28）：52*10^9 /L，正常值范围：125*10^9 /L -350*10^9 /L。  
血小板（2024-09-30）：63*10^9 /L，正常值范围：125*10^9 /L -350*10^9 /L。]

- **如果报告中未提及实验室检查，正文写：**

```
未报告与事件相关的实验室检查。
```

- **如果受试者未进行实验室检查，正文写：**

```
受试者未进行与事件相关的实验室检查。
```

---

检查日期的规则：

1. **日期标准格式**
  - 所有日期输出格式为："YYYY年MM月DD日"。
  - 月份和日期如为个位数必须补零，例如"01"而非"1"。
  - 示例：2024-7-5 应转换为 2024年07月05日。

2. **部分日期处理**
  - 当日期中包含"?"（如"2025-02-??"）或部分信息缺失时：
    * 日部分为"??"或缺失：只使用年月部分，如"2025年02月"；
    * 月部分为"??"或缺失：只使用年部分，如"2025年"；
    * 年部分包含"?"或缺失：视为日期完全未知，使用"时间未报告"。

3. **时间描述生成**  
   根据可用的日期信息，生成相应的时间描述：
  - 完整日期：年月日信息完整 → `YYYY年MM月DD日`；
  - 部分日期：根据部分日期处理规则显示可用部分；
  - 日期带时间：当检查记录包含具体时间点时 → `YYYY年MM月DD日 HH:MM`；
  - 未知日期：时间信息完全缺失时 → `时间未报告`；
  - 同一日期多时间点：当同一日期有多个检查时间点时 → `YYYY年MM月DD日，HH:MM，[检查结果]。HH:MM，[检查结果]...`。

---

注意事项：

- **所有实验室检查信息需合并在一条"正文"中，且仅在开头使用一次前缀“实验室检查包括 ：”。**

- **"正文"中的描述应严格按照本文件“模板格式”生成，整个"正文"以句号"。"结束。**

- **各检查结果行之间用换行分隔，不同日期仍应保持升序。**

- **如"正常值范围"未提及，不需要在括号中添加该信息。**

- **如检查结果为文字描述，使用"示："连接检查项目与结果，否则直接连接。**

- **对于英文缩写的检查项目，首次出现时使用"中文标准名称(英文缩写)"格式，后续相同缩写仅使用缩写本身，遵循药物警戒领域的专业标准。**