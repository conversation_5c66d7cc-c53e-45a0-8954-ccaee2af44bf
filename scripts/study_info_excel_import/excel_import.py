#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Excel导入工具 - 业务逻辑说明

1. 数据来源与格式：
   - 从Excel文件读取研究方案数据
   - Excel文件包含：租户编号、方案编号、方案描述(中文)、研究药物编号、研究药物名称（中文/英文）、受理号等字段
   - 方案编号和方案描述为必填字段

2. 数据处理流程：
   2.1 study_info数据处理：
       - 将数据按照是否有受理号分为两组处理
       - 有受理号的数据：按"方案编号+受理号"组合分组
       - 无受理号的数据：按方案编号分组
   
   2.2 方案描述处理：
       - 保持方案描述中已有的引号
       - 对没有引号的描述添加引号
       - 对于有受理号的数据，在描述后添加受理号信息
       - 最终格式：
         有受理号："方案描述"（研究药物受理号包括：xxx）
         无受理号："方案描述"
         
   2.3 pat_exposure字典数据处理：
       - 按方案编号分组，收集每个方案下的研究药物信息
       - 每行数据格式: | 方案编号 | 研究药物名称(中文) | 研究药物编号 |
       - 构建dictionary_mapping表所需格式数据，作为pat_exposure字典

3. 数据比对逻辑：
   3.1 study_info数据比对：
       - 新增记录：数据库中不存在的方案编号
       - 更新记录：数据库中存在但内容有变化的记录
       - 无变化记录：数据库中存在且内容相同的记录
   
   3.2 pat_exposure字典数据比对：
       - 以研究药物编号(最后一列)为主键进行精确比对
       - 新增药物：现有数据中不存在的研究药物
       - 删除药物：Excel中不存在但数据库中存在的研究药物
       - 变更药物：同一研究药物编号但内容有变化的记录
   
   3.3 比对结果展示：
       - study_info比对结果直接在控制台显示
       - pat_exposure比对结果生成HTML增强的markdown文件，使用左右对比方式
       - 使用不同颜色区分不同类型的记录：
         - 新增记录/内容：绿色
         - 删除记录/内容：红色
         - 更新/变更记录：黄色
         - 无变化记录：蓝色

4. 用户交互流程：
   4.1 操作选项：
       - 查看数据比对结果
       - 导入数据到MySQL
       - 取消操作
   
   4.2 数据确认：
       - 预览处理后的数据
       - 查看详细的比对结果
       - 确认是否执行导入操作

5. 数据导入逻辑：
   5.1 study_info数据导入：
       - 先删除租户下所有现有数据
       - 导入所有Excel处理后的数据（包括新增和更新）
       - 保留原有记录的ID和创建时间
   
   5.2 pat_exposure字典数据导入：
       - 先删除租户下指定模块和占位符的所有现有数据
       - 导入所有处理后的pat_exposure字典数据
       - 记录导入过程的详细日志

6. 异常处理：
   - Excel文件读取异常处理
   - 数据处理异常处理
   - 数据库操作异常处理
   - 事务管理确保数据一致性

7. 多受理号检测：
   - 检测同一方案编号下是否存在多个不同的受理号
   - 展示具有多个受理号的方案的详细信息
   - 提示用户确认数据正确性

本脚本用于将Excel文件中的研究方案数据导入到MySQL数据库中，包括study_info表和dictionary_mapping表（pat_exposure字典）。
按照方案编号和受理号进行分组，处理描述文本和受理号信息，构建不同格式的数据，
并与数据库中现有数据进行比对，支持新增和更新操作。

用法:
    python -m scripts.study_info_excel_import.excel_import

Excel文件格式要求:
    - 租户编号
    - 方案编号（必填，用于分组）
    - 方案描述(中文)（必填）
    - 研究药物编号
    - 研究药物名称（中文）
    - 研究药物名称（英文）
    - 受理号
"""

import os
import sys
from datetime import datetime

import nanoid
import pandas as pd
from sqlalchemy import text
from sqlalchemy.orm import Session

# 将项目根目录添加到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.database.db_mysql import db_session
from src.common.logger import logger

# Excel列名配置，可根据不同Excel文件格式进行调整
EXCEL_COLUMN_CONFIG = {  # 映射关系: 程序中使用的列名 -> Excel中实际的列名
    "租户编号": "租户编号",
    "方案编号": "方案编号",
    "方案描述": "方案描述(中文)",
    "研究药物编号": "研究药物编号",
    "研究药物名称中文": "研究药物名称（中文）",
    "研究药物名称英文": "研究药物名称（英文）",
    "受理号": "受理号",
    "其他列": "其他列"
}

# 必填字段列表
REQUIRED_COLUMNS = ["方案编号", "方案描述"]

# pat_exposure字典配置
PAT_EXPOSURE_CONFIG = {
    "module": "drugInfo_1",
    "placeholder": "{pat_exposure}",
    # project_code将动态设置为方案编号
}

# 租户配置定义
# 格式模板说明：
# - {description}: 方案描述
# - {acceptance}: 受理号
# - {study_num}: 方案编号
# - {drug_acceptance_info}: 按药物分组的受理号信息块
# - {drug_name}: 药物名称（用于药物受理号模板）
# - {acceptance_list}: 某药物的受理号列表（用于药物受理号模板）
TENANT_CONFIG = {
    # 默认配置（包括kelun租户）
    "default": {
        "有受理号模板": '"{description}"（研究药物受理号包括：{acceptance}）',  # 默认格式
        "无受理号模板": '"{description}"',                           # 默认无受理号格式
        "多受理号分隔符": "、",                                       # 多个受理号之间的分隔符
        "受理号前缀": "研究药物受理号包括：",                          # 受理号信息前的文本
        "受理号后缀": "",                                           # 受理号信息后的文本
        "使用括号包围受理号": True,                                   # 是否使用括号包围受理号信息
        "括号类型": "（）",                                          # 使用的括号类型，默认中文括号
    },
    
    # LaNova租户配置 - 简化版
    "LaNova": {
        "有受理号模板": '"{description}"（方案编号：{study_num}）\n\n{drug_acceptance_info}', 
        "无受理号模板": '"{description}"（方案编号：{study_num}）',
        "受理号分隔符": "，",                                         # 受理号列表中使用中文逗号
        "多受理号分隔符": "、",                                        # 多个受理号之间用顿号
        "分组显示受理号": True,                                       # 按药物名称分组显示受理号
        "药物受理号模板": "{drug_name}的受理号包括：{acceptance_list}"  # 每种药物受理号的显示模板
    },
    
    # hspharm租户配置
    "hspharm": {
        "有受理号模板": '"{description}"（研究药物受理号包括：{acceptance}，方案编号：{study_num}）',  
        "无受理号模板": '"{description}"（方案编号：{study_num}）',                           
        "多受理号分隔符": "、",                                       
        "受理号前缀": "研究药物受理号包括：",                          
        "受理号后缀": "，方案编号：{study_num}",                                           
        "使用括号包围受理号": True,                                   
        "括号类型": "（）",                                          
    },
    
    # inxmed租户配置
    "inxmed": {
        "有受理号模板": '"{description}"（{acceptance}）',
        "无受理号模板": '"{description}"',                         
        "多受理号分隔符": "，",                                    
        "受理号前缀": "",                               
        "受理号后缀": "",                                         
        "使用括号包围受理号": True,                                 
        "括号类型": "（）",                                        
        "使用研究药物名称": True,
    },
    
    # miracogen租户配置
    "miracogen": {
        "有受理号模板": '"{description}"（研究药物受理号包括：{acceptance}，方案编号：{study_num}）',
        "无受理号模板": '"{description}"（方案编号：{study_num}）',
        "多受理号分隔符": "、",                                       
        "受理号前缀": "研究药物受理号包括：",                          
        "受理号后缀": "，方案编号：{study_num}",                                           
        "使用括号包围受理号": True,                                   
        "括号类型": "（）",                                          
    },
}

def load_excel_data(excel_path):
    """
    读取Excel文件数据
    
    Args:
        excel_path: Excel文件路径
    
    Returns:
        DataFrame: 读取的数据
    """
    try:
        logger.info("开始读取Excel文件: {}", excel_path)
        # 指定研究药物编号列为字符串类型
        df = pd.read_excel(excel_path, dtype={"研究药物编号": str})
        logger.info("成功读取Excel文件，共 {} 行数据", len(df))

        # 重命名列以便处理
        column_mapping = {v: k for k, v in EXCEL_COLUMN_CONFIG.items()}
        # 创建列名列表，只处理存在的列
        existing_columns = [col for col in column_mapping.keys() if col in df.columns]
        # 应用重命名
        df = df.rename(columns={col: column_mapping[col] for col in existing_columns})

        # 过滤掉可能的空行，使用配置的必填字段
        df = df.dropna(subset=REQUIRED_COLUMNS)

        # 将受理号列的NaN值替换为空字符串，方便后续处理
        if "受理号" in df.columns:
            df["受理号"] = df["受理号"].fillna("")

        # 确保研究药物编号为字符串类型
        if "研究药物编号" in df.columns:
            df["研究药物编号"] = df["研究药物编号"].astype(str)

        logger.info("有效数据共 {} 行", len(df))
        return df

    except Exception as e:
        logger.error("读取Excel文件失败: {}", str(e))
        sys.exit(1)


def normalize_quotes(text):
    """
    标准化文本中的引号，确保只有一对外层引号，并清理多余的引号
    
    Args:
        text: 输入文本
        
    Returns:
        str: 标准化后的文本，只包含一对最外层引号
    """
    if not text:
        return '""'

    # 去除字符串两端的空白字符
    text = text.strip()

    # 去除字符串中所有的引号
    text = text.replace('"', '')

    # 添加一对新的引号
    return f'"{text}"'


def get_tenant_config(tenant_id):
    """
    获取指定租户的配置，如果不存在则使用默认配置
    
    Args:
        tenant_id: 租户ID
        
    Returns:
        dict: 租户配置
    """
    # 如果租户ID存在于配置中，返回该租户配置，否则返回默认配置
    return TENANT_CONFIG.get(tenant_id, TENANT_CONFIG["default"])


def format_description_with_acceptance(description, acceptance, tenant_config, study_num=None, drug_info=None):
    """
    根据租户配置，格式化包含受理号的方案描述
    
    Args:
        description: 方案描述
        acceptance: 受理号或受理号列表
        tenant_config: 租户配置
        study_num: 方案编号，用于特定租户配置
        drug_info: 药物信息字典，用于特定租户按药物分组显示受理号
        
    Returns:
        str: 格式化后的方案描述
    """
    # 特殊处理：LaNova租户按药物分组显示受理号
    if tenant_config.get("分组显示受理号", False) and drug_info:
        # 构建药物受理号信息
        drug_acceptance_blocks = []
        
        for drug_name, drug_acceptances in drug_info.items():
            if drug_acceptances:
                # 使用租户配置的受理号分隔符连接该药物的所有受理号
                acceptance_list = tenant_config.get("受理号分隔符", "，").join(drug_acceptances)
                # 使用药物受理号模板生成该药物的受理号信息块
                drug_block = tenant_config.get("药物受理号模板", "{drug_name}的受理号包括：{acceptance_list}").format(
                    drug_name=drug_name,
                    acceptance_list=acceptance_list
                )
                # 添加药物受理号本身的末尾标点
                if not drug_block.endswith("。"):
                    drug_block += "。"
                    
                drug_acceptance_blocks.append(drug_block)
        
        # 将所有药物受理号信息块连接起来，使用双换行符连接确保在Markdown中是独立段落
        drug_acceptance_info = "\n\n".join(drug_acceptance_blocks)
        
        # 使用tenant_config中的有受理号模板，并传入额外的参数
        formatted = tenant_config["有受理号模板"].format(
            description=description,
            acceptance="",  # 不直接使用acceptance参数
            study_num=study_num or "",
            drug_acceptance_info=drug_acceptance_info
        )
        
        # 返回格式化后的结果
        return formatted
    
    # 处理多个受理号的情况
    if isinstance(acceptance, list):
        acceptance_str = tenant_config["多受理号分隔符"].join([x.strip() for x in acceptance if x.strip()])
    else:
        acceptance_str = str(acceptance).strip()
    
    # 如果没有有效受理号，使用无受理号模板
    if not acceptance_str:
        return format_description_without_acceptance(description, tenant_config, study_num)
    
    # 特殊处理：如果配置了使用研究药物名称
    if tenant_config.get("使用研究药物名称", False) and drug_info:
        # 获取第一个药物名称
        drug_names = list(drug_info.keys())
        if drug_names:
            # 使用第一个药物名称作为前缀
            drug_name = drug_names[0]
            # 构建"研究药物名称受理号："格式
            acceptance_info = f"{drug_name}受理号：{acceptance_str}"
        else:
            # 如果没有药物名称信息，仍使用配置的前缀
            acceptance_info = f"{tenant_config['受理号前缀']}{acceptance_str}{tenant_config['受理号后缀']}"
    else:
        # 构建受理号信息部分
        acceptance_info = f"{tenant_config['受理号前缀']}{acceptance_str}{tenant_config['受理号后缀']}"
    
    # 如果配置使用括号包围受理号信息
    if tenant_config["使用括号包围受理号"]:
        brackets = tenant_config["括号类型"]
        if len(brackets) >= 2:
            left_bracket, right_bracket = brackets[0], brackets[1]
            acceptance_info = f"{left_bracket}{acceptance_info}{right_bracket}"
    
    # 使用模板格式化，支持传入方案编号
    if "{study_num}" in tenant_config["有受理号模板"]:
        return tenant_config["有受理号模板"].format(
            description=description,
            acceptance=acceptance_str,
            study_num=study_num or ""
        )
    else:
        return tenant_config["有受理号模板"].format(
            description=description,
            acceptance=acceptance_str
        )


def format_description_without_acceptance(description, tenant_config, study_num=None):
    """
    根据租户配置，格式化不包含受理号的方案描述
    
    Args:
        description: 方案描述
        tenant_config: 租户配置
        study_num: 方案编号，用于特定租户配置
        
    Returns:
        str: 格式化后的方案描述
    """
    # 支持传入方案编号的模板
    if "{study_num}" in tenant_config["无受理号模板"]:
        return tenant_config["无受理号模板"].format(
            description=description,
            study_num=study_num or ""
        )
    else:
        return tenant_config["无受理号模板"].format(description=description)


def process_data(df, tenant_id=None):
    """
    处理Excel数据，根据方案编号和受理号进行分组
    
    Args:
        df: DataFrame数据
        tenant_id: 租户ID，如果为None则使用Excel中的租户编号
    
    Returns:
        list: 处理后的数据列表
        str: 使用的租户ID
    """
    logger.info("开始处理数据...")

    result_data = []

    # 如果未提供租户ID，则从Excel中获取第一个有数据的租户编号
    if tenant_id is None and "租户编号" in df.columns:
        # 获取第一个非空的租户编号
        valid_tenant_ids = df['租户编号'].dropna()
        if not valid_tenant_ids.empty:
            tenant_id = str(valid_tenant_ids.iloc[0]).strip()
            logger.info("从Excel中获取租户ID: {}", tenant_id)
        else:
            # 如果没有有效的租户编号，使用默认值
            tenant_id = "kelun"
            logger.warning("Excel中没有有效的租户编号，使用默认值: {}", tenant_id)
    elif tenant_id is None:
        # 如果Excel中没有租户编号列，使用默认值
        tenant_id = "kelun"
        logger.warning("Excel中未找到租户编号列，使用默认值: {}", tenant_id)
    
    # 获取租户配置
    tenant_config = get_tenant_config(tenant_id)
    logger.info("使用租户 {} 的配置进行数据处理", tenant_id)

    def process_description(desc, tenant_config):
        """
        处理描述文本，按租户配置处理引号格式
        """
        # 提取租户配置中的无受理号模板
        template = tenant_config["无受理号模板"]
        
        # 如果模板中已有引号包裹描述，直接使用模板
        if '"{description}"' in template:
            # 确保描述中没有多余的引号
            clean_desc = desc.replace('"', '')
            return template.format(description=clean_desc)
        
        # 如果模板中没有引号，但描述已有引号
        if (desc.startswith('"') and desc.endswith('"')) or (desc.startswith('"') and desc.endswith('"')):
            # 如果模板要求没有引号，去掉描述中的引号
            if '"{description}"' not in template:
                clean_desc = desc[1:-1]  # 去掉首尾引号
                return template.format(description=clean_desc)
            return template.format(description=desc)
        
        # 按照模板格式化
        return template.format(description=desc)

    # 将受理号为空的替换为空字符串
    if "受理号" in df.columns:
        df["受理号"] = df["受理号"].fillna("")

    # 首先，找出哪些方案编号同时存在有受理号和无受理号的情况
    # 创建标记列，标识记录是否有受理号
    df['有受理号'] = df['受理号'].str.strip() != ""

    # 按方案编号分组，检查每组内是否同时存在有受理号和无受理号的记录
    study_num_groups = df.groupby('方案编号')['有受理号'].agg(['any', 'all'])

    # 筛选出同时有受理号和无受理号的方案编号（any=True 且 all=False）
    mixed_study_nums = study_num_groups[
        (study_num_groups['any'] == True) & (study_num_groups['all'] == False)].index.tolist()

    logger.info("检测到 {} 个方案编号同时存在有受理号和无受理号的记录", len(mixed_study_nums))

    # 只保留有受理号的记录或者方案编号不在混合列表中的记录
    filtered_df = df[df['有受理号'] | (~df['方案编号'].isin(mixed_study_nums))]

    logger.info("过滤前数据量: {} 行", len(df))
    logger.info("过滤后数据量: {} 行", len(filtered_df))
    logger.info("过滤掉 {} 行冗余的无受理号记录", len(df) - len(filtered_df))

    # 清理辅助列
    if '有受理号' in filtered_df.columns:
        filtered_df = filtered_df.drop('有受理号', axis=1)

    # 将数据分为有受理号和无受理号两组，使用copy()避免SettingWithCopyWarning
    df_with_acceptance = filtered_df[filtered_df['受理号'].str.strip() != ""].copy()
    df_without_acceptance = filtered_df[filtered_df['受理号'].str.strip() == ""].copy()

    logger.info("有受理号的数据: {} 行", len(df_with_acceptance))
    logger.info("无受理号的数据: {} 行", len(df_without_acceptance))

    # 处理有受理号的数据
    if not df_with_acceptance.empty:
        # 创建组合键
        df_with_acceptance['组合键'] = df_with_acceptance['方案编号'] + '|' + df_with_acceptance['受理号']

        # 按组合键分组
        grouped = df_with_acceptance.groupby('组合键')

        # 收集每个方案编号下的所有研究药物和受理号
        study_nums_drugs = {}

        for group_key, group in grouped:
            # 获取该组的方案编号和方案描述
            study_num = group['方案编号'].iloc[0]
            study_desc = str(group['方案描述'].iloc[0])
            acceptance = group['受理号'].iloc[0]

            # 检查该组合键是否有多条记录（不同的研究药物）
            drug_list = []
            
            # 收集药物信息，用于按药物分组显示受理号
            drug_info = {}
            
            for _, record in group.iterrows():
                drug_id = record.get('研究药物编号', '')
                drug_name = record.get('研究药物名称中文', '')
                drug_acceptance = record.get('受理号', '')
                
                if not pd.isna(drug_id) and not pd.isna(drug_name):
                    drug_list.append(f"{drug_name}({drug_id})")
                
                # 如果存在有效的药物名称和受理号，收集该药物的受理号
                if not pd.isna(drug_name) and drug_name.strip() and not pd.isna(drug_acceptance) and drug_acceptance.strip():
                    if drug_name not in drug_info:
                        drug_info[drug_name] = []
                    
                    # 确保不重复添加相同的受理号
                    if drug_acceptance not in drug_info[drug_name]:
                        drug_info[drug_name].append(drug_acceptance)

            # 收集每个方案编号下的所有药物
            if study_num not in study_nums_drugs:
                study_nums_drugs[study_num] = {
                    'desc': study_desc, 
                    'drugs': drug_list, 
                    'acceptance': acceptance, 
                    'drug_info': drug_info
                }
            else:
                study_nums_drugs[study_num]['drugs'].extend(drug_list)
                # 如果已有受理号，附加新的受理号（用逗号分隔）
                if acceptance not in study_nums_drugs[study_num]['acceptance']:
                    study_nums_drugs[study_num]['acceptance'] += f",{acceptance}"
                
                # 合并药物信息
                for drug_name, acceptances in drug_info.items():
                    if drug_name not in study_nums_drugs[study_num]['drug_info']:
                        study_nums_drugs[study_num]['drug_info'][drug_name] = []
                    
                    for acc in acceptances:
                        if acc not in study_nums_drugs[study_num]['drug_info'][drug_name]:
                            study_nums_drugs[study_num]['drug_info'][drug_name].append(acc)

        # 创建有受理号数据的最终结果
        for study_num, info in study_nums_drugs.items():
            study_desc = info['desc']

            # 添加受理号信息
            acceptances = str(info['acceptance']).split(",")
            
            # 使用租户配置格式化方案描述
            if acceptances:
                # 过滤空字符串
                filtered_acceptances = [x.strip() for x in acceptances if x.strip()]
                
                # 使用带受理号的格式化方法，同时传入方案编号和药物信息
                study_data = format_description_with_acceptance(
                    study_desc, 
                    filtered_acceptances, 
                    tenant_config,
                    study_num=study_num,
                    drug_info=info.get('drug_info', {})
                )
            else:
                # 使用不带受理号的格式化方法，同时传入方案编号
                study_data = format_description_without_acceptance(
                    study_desc, 
                    tenant_config,
                    study_num=study_num
                )

            # 构建结果数据
            result_data.append(
                {"id": nanoid.generate(size=21), "tenant_id": tenant_id, "study_num": study_num,
                    "study_data": study_data, "created_at": datetime.now(), "updated_at": datetime.now()})

    # 处理无受理号的数据
    if not df_without_acceptance.empty:
        # 按方案编号分组
        grouped_no_acceptance = df_without_acceptance.groupby('方案编号')

        for study_num, group in grouped_no_acceptance:
            # 获取该组的方案描述
            study_desc = str(group['方案描述'].iloc[0])

            # 使用租户配置处理描述文本，传入方案编号
            study_data = format_description_without_acceptance(
                study_desc, 
                tenant_config,
                study_num=study_num
            )

            # 构建结果数据
            result_data.append(
                {"id": nanoid.generate(size=21), "tenant_id": tenant_id, "study_num": study_num,
                    "study_data": study_data, "created_at": datetime.now(), "updated_at": datetime.now()})

    logger.info("数据处理完成，共 {} 条有效数据", len(result_data))

    return result_data, tenant_id


def import_to_mysql(data, session: Session):
    """
    将数据导入MySQL数据库
    
    Args:
        data: 要导入的数据列表
        session: 数据库会话
    """
    if not data:
        logger.warning("没有数据可导入")
        return

    logger.info("开始导入数据到MySQL...")

    try:
        # 准备插入语句
        insert_sql = text("""
        INSERT INTO study_info 
        (id, tenant_id, study_num, study_data, created_at, updated_at)
        VALUES 
        (:id, :tenant_id, :study_num, :study_data, :created_at, :updated_at)
        """)

        # 执行批量插入
        for item in data:
            session.execute(insert_sql, item)

        # 提交事务
        session.commit()
        logger.info("成功导入 {} 条数据到MySQL", len(data))

    except Exception as e:
        session.rollback()
        logger.error("导入数据到MySQL失败: {}", str(e))
        raise


def preview_data(data):
    """
    记录处理后的数据信息，但不在控制台展示
    
    Args:
        data: 处理后的数据列表
    """
    if not data:
        logger.warning("没有数据可预览")
        return

    logger.info("处理后的数据共 {} 条", len(data))
    # 不再打印实际数据到控制台


def fetch_existing_data(session: Session, tenant_id=None):
    """
    从数据库中获取现有数据，可按租户ID过滤
    
    Args:
        session: 数据库会话
        tenant_id: 租户ID，如果提供则按租户ID过滤
    
    Returns:
        dict: 以方案编号为键的现有数据字典
    """
    logger.info("正在查询数据库中的现有数据...")

    try:
        # 准备查询语句，根据是否提供租户ID决定是否添加过滤条件
        if tenant_id:
            logger.info("按租户ID过滤: {}", tenant_id)
            query = text(
                "SELECT id, tenant_id, study_num, study_data, created_at, updated_at FROM study_info WHERE tenant_id = :tenant_id")
            result = session.execute(query, {"tenant_id": tenant_id})
        else:
            logger.info("查询所有租户的数据")
            query = text("SELECT id, tenant_id, study_num, study_data, created_at, updated_at FROM study_info")
            result = session.execute(query)

        # 转换为字典，以方案编号为键
        existing_data = {}
        for row in result:
            existing_data[row.study_num] = {"id": row.id, "tenant_id": row.tenant_id, "study_num": row.study_num,
                                            "study_data": row.study_data, "created_at": row.created_at,
                                            "updated_at": row.updated_at}

        logger.info("成功获取现有数据，共 {} 条记录", len(existing_data))
        return existing_data

    except Exception as e:
        logger.error("查询现有数据失败: {}", str(e))
        return {}


def compare_data(new_data, existing_data):
    """
    比对新数据和现有数据，分析差异
    
    Args:
        new_data: 处理后的新数据列表
        existing_data: 现有数据字典
    
    Returns:
        tuple: (新增数据, 更新数据, 无变化数据, 数据库存在但Excel不存在的数据)
    """
    if not existing_data:
        # 如果没有现有数据，则所有数据都是新增的
        return new_data, [], [], []

    new_records = []
    updated_records = []
    unchanged_records = []

    # 创建一个Excel数据中所有方案编号的集合，用于检测数据库中存在但Excel中不存在的记录
    excel_study_nums = {item["study_num"] for item in new_data}

    # 初始化数据库存在但Excel不存在的记录列表
    missing_in_excel_records = []

    for item in new_data:
        study_num = item["study_num"]

        if study_num not in existing_data:
            # 新的方案编号，是新增数据
            new_records.append(item)
        else:
            # 已存在的方案编号，检查内容是否有变化
            existing = existing_data[study_num]
            if item["study_data"] != existing["study_data"]:
                # 内容有变化，需要更新
                item["id"] = existing["id"]  # 使用现有记录的ID
                item["created_at"] = existing["created_at"]  # 保留创建时间
                updated_records.append({"new": item, "old": existing})
            else:
                # 内容无变化
                unchanged_records.append(item)

    # 检测数据库中存在但Excel中不存在的记录
    for study_num, record in existing_data.items():
        if study_num not in excel_study_nums:
            missing_in_excel_records.append(record)

    return new_records, updated_records, unchanged_records, missing_in_excel_records


def generate_study_info_diff_markdown(new_records, updated_records, unchanged_records, missing_in_excel_records=None,
                                      output_path=None):
    """
    生成study_info数据的差异对比的markdown文件
    
    Args:
        new_records: 新增数据列表
        updated_records: 更新数据列表
        unchanged_records: 无变化数据列表
        missing_in_excel_records: 数据库中存在但Excel中不存在的记录列表
        output_path: 输出文件路径，如果为None，则使用默认路径
    
    Returns:
        str: 生成的文件路径
    """
    if not output_path:
        # 创建输出目录
        output_dir = os.path.join(os.path.dirname(__file__), "study_info_diffs")
        os.makedirs(output_dir, exist_ok=True)

        # 使用当前时间作为文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = os.path.join(output_dir, f"study_info_diff_{timestamp}.md")

    logger.info("开始生成study_info数据的差异对比markdown文件...")

    # 如果missing_in_excel_records为None，设置为空列表
    if missing_in_excel_records is None:
        missing_in_excel_records = []

    total = len(new_records) + len(updated_records) + len(unchanged_records) + len(missing_in_excel_records)

    # 构建markdown内容
    md_content = []

    # 添加标题和统计信息
    md_content.append("# study_info数据差异对比\n")
    md_content.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    md_content.append(f"## 统计信息\n")
    md_content.append("<div style='display: flex; flex-direction: column; gap: 5px;'>")
    md_content.append(f"<div><strong>总记录数:</strong> {total} 条</div>")
    md_content.append(f"<div style='color: #2cbe4e;'><strong>新增记录:</strong> {len(new_records)} 条</div>")
    md_content.append(f"<div style='color: #dbab09;'><strong>更新记录:</strong> {len(updated_records)} 条</div>")
    md_content.append(f"<div style='color: #4078c0;'><strong>无变化记录:</strong> {len(unchanged_records)} 条</div>")
    md_content.append(
        f"<div style='color: #cb2431;'><strong>数据库存在但Excel不存在:</strong> {len(missing_in_excel_records)} 条</div>")
    md_content.append("</div>\n")

    # 添加新增记录
    if new_records:
        md_content.append("<h2 style='color: #2cbe4e;'>新增记录</h2>\n")

        for i, item in enumerate(new_records, 1):
            md_content.append(f"<h3 style='color: #2cbe4e;'>{i}. 方案编号: {item['study_num']}</h3>\n")

            # 使用表格格式展示，更加直观
            md_content.append("<table>")
            md_content.append("<tr>")
            md_content.append("<th>状态</th>")
            md_content.append("<th>方案描述</th>")
            md_content.append("</tr>")

            md_content.append("<tr>")
            md_content.append("<td style='color: #2cbe4e; font-weight: bold;'>新增</td>")
            md_content.append(f"<td style='background-color: #e6ffec;'>{item['study_data']}</td>")
            md_content.append("</tr>")

            md_content.append("</table>\n")

    # 添加更新记录
    if updated_records:
        md_content.append("<h2 style='color: #dbab09;'>更新记录</h2>\n")

        for i, item in enumerate(updated_records, 1):
            md_content.append(f"<h3 style='color: #dbab09;'>{i}. 方案编号: {item['new']['study_num']}</h3>\n")

            # 使用左右对比布局
            md_content.append("<table width='100%' border='1' style='border-collapse: collapse;'>")
            md_content.append("<tr style='background-color: #f6f8fa;'>")
            md_content.append("<th width='50%'>旧内容</th>")
            md_content.append("<th width='50%'>新内容</th>")
            md_content.append("</tr>")

            md_content.append("<tr>")
            # 旧内容
            md_content.append(f"<td style='background-color: #fff8e7;'>{item['old']['study_data']}</td>")
            # 新内容
            md_content.append(f"<td style='background-color: #e6ffec;'>{item['new']['study_data']}</td>")
            md_content.append("</tr>")

            md_content.append("</table>\n")

            # 尝试找出具体变化的部分
            old_data = item['old']['study_data']
            new_data = item['new']['study_data']

            # 简单比对，找出第一个不同的字符位置
            diff_pos = 0
            for i in range(min(len(old_data), len(new_data))):
                if old_data[i] != new_data[i]:
                    diff_pos = i
                    break

            if diff_pos > 0:
                start_pos = max(0, diff_pos - 20)
                md_content.append("<div style='margin-top: 10px;'>")
                md_content.append(f"<p><strong>差异位置:</strong> 第 {diff_pos} 个字符处</p>")

                # 高亮显示差异部分
                md_content.append("<table width='100%' border='1' style='border-collapse: collapse;'>")
                md_content.append("<tr style='background-color: #f6f8fa;'>")
                md_content.append("<th width='50%'>旧内容(部分)</th>")
                md_content.append("<th width='50%'>新内容(部分)</th>")
                md_content.append("</tr>")

                md_content.append("<tr>")
                md_content.append(
                    f"<td style='background-color: #ffeef0;'>...{old_data[start_pos:start_pos + 50]}...</td>")
                md_content.append(
                    f"<td style='background-color: #e6ffec;'>...{new_data[start_pos:start_pos + 50]}...</td>")
                md_content.append("</tr>")

                md_content.append("</table>")
                md_content.append("</div>\n")

    # 添加数据库存在但Excel不存在的记录
    if missing_in_excel_records:
        md_content.append("<h2 style='color: #cb2431;'>数据库存在但Excel不存在的记录</h2>\n")

        for i, item in enumerate(missing_in_excel_records, 1):
            md_content.append(f"<h3 style='color: #cb2431;'>{i}. 方案编号: {item['study_num']}</h3>\n")

            # 使用表格格式展示
            md_content.append("<table>")
            md_content.append("<tr>")
            md_content.append("<th>状态</th>")
            md_content.append("<th>方案描述</th>")
            md_content.append("</tr>")

            md_content.append("<tr>")
            md_content.append("<td style='color: #cb2431; font-weight: bold;'>删除</td>")
            md_content.append(f"<td style='background-color: #ffeef0;'>{item['study_data']}</td>")
            md_content.append("</tr>")

            md_content.append("</table>\n")

    # 添加无变化记录
    if unchanged_records:
        md_content.append("<h2 style='color: #4078c0;'>无变化记录</h2>\n")

        # 显示所有方案编号列表
        study_nums = [record['study_num'] for record in unchanged_records]
        md_content.append("<div style='color: #4078c0; margin-left: 20px;'>" + ", ".join(study_nums) + "</div>")

    # 添加注释说明
    md_content.append("\n<hr>\n")
    md_content.append("<div style='font-size: 0.9em; color: #6a737d;'>")
    md_content.append("<p><strong>注释说明:</strong></p>")
    md_content.append("<ul>")
    md_content.append("<li><span style='color: #2cbe4e;'>🔼 绿色</span>: 新增内容</li>")
    md_content.append("<li><span style='color: #cb2431;'>🔻 红色</span>: 删除内容</li>")
    md_content.append("<li><span style='color: #dbab09;'>⚠️ 黄色</span>: 变更内容</li>")
    md_content.append("<li><span style='color: #4078c0;'>蓝色</span>: 无变化记录</li>")
    md_content.append("</ul>")
    md_content.append("<p><strong>比对方式:</strong> 按照方案编号作为主键进行对比，内容变化时显示左右对比</p>")
    md_content.append("</div>")

    # 将内容写入文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("\n".join(md_content))

    logger.info("study_info数据的差异对比markdown文件已生成: {}", output_path)

    return output_path


def preview_comparison(new_records, updated_records, unchanged_records, missing_in_excel_records=None):
    """
    记录比对结果，不在控制台展示，但仍生成差异对比的markdown文件
    
    Args:
        new_records: 新增数据列表
        updated_records: 更新数据列表
        unchanged_records: 无变化数据列表
        missing_in_excel_records: 数据库中存在但Excel中不存在的记录列表
        
    Returns:
        str: 生成的差异对比markdown文件路径
    """
    # 如果missing_in_excel_records为None，设置为空列表
    if missing_in_excel_records is None:
        missing_in_excel_records = []

    total = len(new_records) + len(updated_records) + len(unchanged_records) + len(missing_in_excel_records)
    logger.info("数据比对结果（总计 {} 条记录）:", total)
    logger.info("  新增记录: {} 条", len(new_records))
    logger.info("  更新记录: {} 条", len(updated_records))
    logger.info("  无变化记录: {} 条", len(unchanged_records))
    logger.info("  数据库存在但Excel中不存在的记录: {} 条", len(missing_in_excel_records))

    # 生成差异对比的markdown文件
    diff_file = generate_study_info_diff_markdown(new_records, updated_records, unchanged_records,
                                                  missing_in_excel_records)
    logger.info("详细差异已输出到文件: {}", diff_file)

    return diff_file

def import_data_with_updates(data, session: Session, tenant_id=None):
    """
    将数据导入MySQL数据库，包括新增和更新
    
    Args:
        data: 包含新增和更新的数据字典
        session: 数据库会话
        tenant_id: 租户ID，如果未提供则尝试从记录中获取
    """
    if not data:
        logger.warning("没有数据可导入")
        return

    new_records = data.get("new", [])
    updated_records = data.get("updated", [])
    
    # 如果未提供租户ID，尝试从数据中获取
    if not tenant_id:
        # 从新增记录中获取
        if new_records and "tenant_id" in new_records[0] and new_records[0]["tenant_id"]:
            tenant_id = new_records[0]["tenant_id"]
        # 从更新记录中获取
        elif updated_records and "new" in updated_records[0] and "tenant_id" in updated_records[0]["new"] and updated_records[0]["new"]["tenant_id"]:
            tenant_id = updated_records[0]["new"]["tenant_id"]
    
    # 如果仍然无法获取租户ID，记录错误并返回
    if not tenant_id:
        logger.warning("无法获取租户ID，无法删除原有数据")
        return

    logger.info("开始导入数据到MySQL，租户ID: {}", tenant_id)

    # 开始事务
    transaction = session.begin_nested()
    try:
        logger.info("开始事务处理...")
        
        # 先查询要删除的数据
        query_sql = text("""
        SELECT id, tenant_id, study_num, study_data 
        FROM study_info 
        WHERE tenant_id = :tenant_id
        """)
        
        delete_records = session.execute(query_sql, {"tenant_id": tenant_id}).fetchall()
        
        # 记录要删除的数据
        if delete_records:
            logger.info("将删除租户 {} 的 {} 条数据:", tenant_id, len(delete_records))
            for idx, record in enumerate(delete_records, 1):
                if idx <= 10:  # 只显示前10条，避免日志过长
                    logger.info("  {}. 方案编号: {}, 描述: {}", idx, record.study_num, record.study_data[:50] + "..." if len(record.study_data) > 50 else record.study_data)
                elif idx == 11:
                    logger.info("  ... 还有 {} 条记录未显示", len(delete_records) - 10)
        else:
            logger.info("租户 {} 当前没有数据需要删除", tenant_id)
        
        # 执行删除操作
        delete_sql = text("""
        DELETE FROM study_info 
        WHERE tenant_id = :tenant_id
        """)
        
        result = session.execute(delete_sql, {"tenant_id": tenant_id})
        deleted_rows = result.rowcount
        logger.info("已删除租户 {} 的 {} 条现有数据", tenant_id, deleted_rows)

        # 记录插入总数
        inserted_count = 0

        # 处理新增记录
        if new_records:
            insert_sql = text("""
            INSERT INTO study_info 
            (id, tenant_id, study_num, study_data, created_at, updated_at)
            VALUES 
            (:id, :tenant_id, :study_num, :study_data, :created_at, :updated_at)
            """)

            for item in new_records:
                try:
                    # 确保所有记录使用相同的租户ID
                    item["tenant_id"] = tenant_id
                    session.execute(insert_sql, item)
                    inserted_count += 1
                except Exception as e:
                    logger.error("插入记录失败 [方案编号: {}]: {}", item.get("study_num", "未知"), str(e))
                    raise

            logger.info("已新增 {} 条记录", len(new_records))

        # 处理更新记录
        if updated_records:
            # 由于已经删除了所有数据，所以更新记录也需要作为新增处理
            insert_sql = text("""
            INSERT INTO study_info 
            (id, tenant_id, study_num, study_data, created_at, updated_at)
            VALUES 
            (:id, :tenant_id, :study_num, :study_data, :created_at, :updated_at)
            """)
            
            for item in updated_records:
                try:
                    # 确保所有记录使用相同的租户ID
                    item["new"]["tenant_id"] = tenant_id
                    session.execute(insert_sql, item["new"])
                    inserted_count += 1
                except Exception as e:
                    logger.error("插入更新记录失败 [方案编号: {}]: {}", item["new"].get("study_num", "未知"), str(e))
                    raise

            logger.info("已插入更新的 {} 条记录", len(updated_records))

        # 提交事务
        transaction.commit()
        session.commit()
        logger.info("事务已提交，成功导入数据到MySQL，共删除 {} 条旧数据，插入 {} 条新数据", 
                   deleted_rows, inserted_count)
        
        # 打印最终的操作摘要
        logger.info("=== 数据导入摘要 ===")
        logger.info("租户ID: {}", tenant_id)
        logger.info("删除记录: {} 条", deleted_rows)
        logger.info("新增记录: {} 条", len(new_records))
        logger.info("更新记录: {} 条", len(updated_records))
        logger.info("实际插入: {} 条", inserted_count)
        logger.info("==================")

    except Exception as e:
        # 回滚事务
        try:
            transaction.rollback()
            session.rollback()
            logger.error("事务已回滚，导入数据到MySQL失败: {}", str(e))
        except Exception as rollback_error:
            logger.error("事务回滚失败: {}", str(rollback_error))
        
        # 将异常向上传递
        raise Exception(f"导入数据失败: {str(e)}")
    
    finally:
        logger.info("事务处理结束")


def confirm_import():
    """
    请求用户确认操作选项
    
    Returns:
        str: 用户选择的操作
    """
    print("\n" + "=" * 80)
    print("               请选择要执行的操作               ")
    print("=" * 80)
    print("1. 导入数据到MySQL数据库 (将在一个事务中完成所有操作)")
    print("2. 取消操作 (将删除已生成的差异文件)")
    print("-" * 80)
    print("注意：如果选择导入但操作失败，所有数据变更将回滚且差异文件将被删除")
    print("=" * 80)

    while True:
        response = input("\n请输入选项编号 (1/2): ").strip()

        if response == "1":
            return "import"
        elif response == "2":
            return "cancel"
        else:
            print("无效的输入，请输入 1 或 2")


def check_multiple_acceptance_numbers(df):
    """
    检查具有多个不同受理号的方案编号，并显示相关的完整信息
    
    Args:
        df: DataFrame数据
    """
    # 必须的列是否存在
    if "方案编号" not in df.columns or "受理号" not in df.columns or "方案描述" not in df.columns:
        logger.warning("Excel中缺少必要的列，无法检查多受理号情况")
        return

    # 过滤掉空的受理号
    df_with_acceptance = df[df["受理号"].str.strip() != ""]

    # 按方案编号分组，收集每个方案编号下的所有信息
    study_num_groups = {}
    for _, row in df_with_acceptance.iterrows():
        study_num = row["方案编号"]
        acceptance = str(row["受理号"]).strip()
        desc = str(row["方案描述"]).strip()

        if study_num not in study_num_groups:
            study_num_groups[study_num] = {'acceptances': set(), 'desc': desc, 'details': []}
        study_num_groups[study_num]['acceptances'].add(acceptance)
        study_num_groups[study_num]['details'].append({'acceptance': acceptance, 'desc': desc})

    # 找出具有多个受理号的方案编号
    multiple_acceptance_nums = {study_num: info for study_num, info in study_num_groups.items() if
                                len(info['acceptances']) > 1}

    if multiple_acceptance_nums:
        print("\n" + "=" * 120)
        print("【警告】以下方案编号存在多个不同的受理号：")
        print("=" * 120)

        for study_num, info in multiple_acceptance_nums.items():
            print(f"\n方案编号: {study_num}")
            print("-" * 120)
            for i, detail in enumerate(info['details'], 1):
                print(f"受理号 {i}: {detail['acceptance']}")
                print(f"描述: {detail['desc']}")
                print("-" * 120)

        print("\n请检查以上方案编号的数据是否正确。")
        input("\n按回车键继续...")
    else:
        print("\n所有方案编号的受理号都是唯一的。")


def sort_content_by_last_column(content):
    """
    按照每行内容的最后一列(研究药物编号)排序content内容
    
    Args:
        content: 原始content内容，以换行符分隔的多行文本
    
    Returns:
        str: 排序后的content内容
    """
    if not content:
        return content

    lines = content.split('\n')

    # 定义排序键函数，提取每行的最后一列（去掉结尾的 | 字符）
    def sort_key(line):
        # 拆分行，去除空字符串
        parts = [part.strip() for part in line.split('|') if part.strip()]
        # 如果行格式不符或没有足够列，返回空字符串作为键
        if not parts:
            return ""
        # 返回最后一列作为排序键
        return parts[-1]

    # 按最后一列排序
    sorted_lines = sorted(lines, key=sort_key)
    
    # 处理每一行，去除列数据值之间的空格
    processed_lines = []
    for line in sorted_lines:
        if line.strip():
            # 分割每一行并去除前后空格
            parts = [part.strip() for part in line.split('|')]
            # 重新拼接行，不在分隔符之间添加多余空格
            processed_line = '|' + '|'.join(parts) + '|'
            processed_lines.append(processed_line)
        else:
            processed_lines.append(line)  # 保留空行

    # 返回重新组合的内容
    return '\n'.join(processed_lines)


def extract_drug_id(line):
    """
    从表格行中提取药物编号(最后一列)
    
    Args:
        line: 表格行内容
    
    Returns:
        str: 药物编号
    """
    parts = [part.strip() for part in line.split('|') if part.strip()]
    if not parts:
        return ""
    return parts[-1]


def extract_compare_key(line):
    """
    从表格行中提取比对键（方案编号+药物名称+药物编号的组合）
    
    Args:
        line: 表格行内容
    
    Returns:
        str: 比对键
        tuple: (方案编号, 药物名称, 药物编号)
    """
    parts = [part.strip() for part in line.split('|') if part.strip()]
    if len(parts) < 3:
        return "", ("", "", "")
    
    project_code = parts[0]
    drug_name = parts[1]
    drug_id = parts[2]
    
    # 返回比对键和原始三个部分
    return f"{project_code}_{drug_name}_{drug_id}", (project_code, drug_name, drug_id)


def compare_pat_exposure_data(new_data_list, existing_data):
    """
    比对新旧pat_exposure字典数据，包括处理GLOBAL记录
    使用方案编号+药物名称+药物编号的组合作为比对依据
    
    Args:
        new_data_list: 新的pat_exposure字典数据列表
        existing_data: 现有的pat_exposure字典数据字典
    
    Returns:
        tuple: (新增数据列表, 更新数据列表, 无变化数据列表, 数据库存在但Excel中不存在的数据列表)
    """
    if not existing_data:
        logger.info("没有现有的pat_exposure字典数据，所有数据视为新增")
        return new_data_list, [], [], []

    new_records = []
    updated_records = []
    unchanged_records = []

    # 创建Excel数据中所有方案编号的集合，用于检测数据库中存在但Excel中不存在的记录
    excel_project_codes = {item["project_code"] for item in new_data_list}

    # 初始化数据库存在但Excel不存在的记录列表
    missing_in_excel_records = []

    for new_item in new_data_list:
        project_code = new_item["project_code"]

        if project_code not in existing_data:
            # 新增记录
            new_records.append(new_item)
            logger.info("发现新增记录: {}", project_code)
        else:
            # 现有记录，比较内容
            existing_item = existing_data[project_code]

            # 将内容拆分为行
            new_lines = [line for line in new_item["content"].split('\n') if line.strip()]
            old_lines = [line for line in existing_item["content"].split('\n') if line.strip()]

            # 创建比对键到行内容的映射
            new_key_map = {}
            old_key_map = {}
            
            for line in new_lines:
                key, parts = extract_compare_key(line)
                if key:
                    new_key_map[key] = (line, parts)
                    
            for line in old_lines:
                key, parts = extract_compare_key(line)
                if key:
                    old_key_map[key] = (line, parts)

            # 获取所有不重复的比对键
            all_keys = set(new_key_map.keys()) | set(old_key_map.keys())

            # 检查是否有差异
            has_diff = False
            exact_same = True

            # 存储每种类型的差异
            added_keys = []    # 新增的比对键
            removed_keys = []  # 删除的比对键
            changed_keys = []  # 内容变化的比对键（虽然使用完整比对键后不应该有变化）

            for key in all_keys:
                if key not in old_key_map:
                    # 新数据中有，旧数据中没有
                    added_keys.append(key)
                    has_diff = True
                    exact_same = False
                elif key not in new_key_map:
                    # 旧数据中有，新数据中没有
                    removed_keys.append(key)
                    has_diff = True
                    exact_same = False
                elif new_key_map[key][0] != old_key_map[key][0]:
                    # 新旧数据都有，但内容不同（理论上不会出现）
                    changed_keys.append(key)
                    has_diff = True
                    exact_same = False

            if has_diff:
                # 创建新旧数据的副本
                new_item_copy = new_item.copy()
                existing_item_copy = existing_item.copy()

                # 创建差异详情
                diff_details = {
                    "added": [(key, new_key_map[key][0], new_key_map[key][1]) for key in added_keys],
                    "removed": [(key, old_key_map[key][0], old_key_map[key][1]) for key in removed_keys],
                    "changed": [(key, old_key_map[key][0], new_key_map[key][0]) for key in changed_keys],
                    "all_keys": sorted(list(all_keys)),
                    # 保存完整的旧数据行集合和新数据行集合，便于完整显示
                    "all_old_lines": old_lines,
                    "all_new_lines": new_lines,
                    # 保存新旧映射，便于显示
                    "new_key_map": new_key_map,
                    "old_key_map": old_key_map
                }

                # 保存差异信息
                updated_records.append({
                    "new": new_item_copy,
                    "old": existing_item_copy,
                    "diff_details": diff_details
                })
                
                if project_code == "GLOBAL":
                    logger.info("发现GLOBAL记录有变化")
                    logger.info("GLOBAL记录变化详情: {} 个新增, {} 个删除, {} 个变更", 
                              len(added_keys), len(removed_keys), len(changed_keys))
            else:
                # 内容完全相同
                unchanged_records.append(new_item)
                
                if project_code == "GLOBAL":
                    logger.info("GLOBAL记录内容无变化")

    # 检测数据库中存在但Excel中不存在的记录
    for project_code, record in existing_data.items():
        if project_code not in excel_project_codes:
            missing_in_excel_records.append(record)
            
            if project_code == "GLOBAL":
                logger.info("发现GLOBAL记录在数据库中存在但Excel中不存在")

    logger.info("pat_exposure字典数据比对结果：新增 {} 条，更新 {} 条，无变化 {} 条，数据库存在但Excel中不存在 {} 条",
                len(new_records), len(updated_records), len(unchanged_records), len(missing_in_excel_records))

    return new_records, updated_records, unchanged_records, missing_in_excel_records


def generate_pat_exposure_diff_markdown(new_records, updated_records, unchanged_records, missing_in_excel_records=None,
                                       output_path=None):
    """
    生成pat_exposure字典数据的差异对比的markdown文件
    特殊处理：添加对GLOBAL记录的特殊说明
    
    Args:
        new_records: 新增数据列表
        updated_records: 更新数据列表
        unchanged_records: 无变化数据列表
        missing_in_excel_records: 数据库中存在但Excel中不存在的记录列表
        output_path: 输出文件路径，如果为None，则使用默认路径
    
    Returns:
        str: 生成的文件路径
    """
    if not output_path:
        # 创建输出目录
        output_dir = os.path.join(os.path.dirname(__file__), "pat_exposure_diffs")
        os.makedirs(output_dir, exist_ok=True)

        # 使用当前时间作为文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = os.path.join(output_dir, f"pat_exposure_diff_{timestamp}.md")

    logger.info("开始生成pat_exposure字典数据的差异对比markdown文件...")

    # 如果missing_in_excel_records为None，设置为空列表
    if missing_in_excel_records is None:
        missing_in_excel_records = []

    # 统计删除记录的数量
    total_removed = 0
    for item in updated_records:
        diff_details = item.get('diff_details', {})
        removed = diff_details.get('removed', [])
        total_removed += len(removed)
    
    # 正确计算总记录数（所有分类记录数之和）
    total_records = len(new_records) + len(updated_records) + total_removed + len(unchanged_records) + len(missing_in_excel_records)

    # 检查是否有GLOBAL记录
    has_global_new = any(item['project_code'] == 'GLOBAL' for item in new_records)
    has_global_updated = any(item['new']['project_code'] == 'GLOBAL' for item in updated_records)
    has_global_unchanged = any(item['project_code'] == 'GLOBAL' for item in unchanged_records)
    has_global_missing = any(item['project_code'] == 'GLOBAL' for item in missing_in_excel_records)

    # 构建markdown内容
    md_content = []

    # 添加标题和统计信息
    md_content.append("# pat_exposure字典数据差异对比\n")
    md_content.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

    # 添加GLOBAL记录特殊说明
    md_content.append(
        "<div style='background-color: #f0f7fb; padding: 10px; margin: 10px 0; border-left: 5px solid #3498db;'>")
    md_content.append("<strong>GLOBAL记录特别说明:</strong>")
    md_content.append("<p>project_code为\"GLOBAL\"的记录包含所有项目的pat_exposure数据合成信息，按方案编号和研究药物编号排序。</p>")
    
    if has_global_new:
        md_content.append("<p>本次导入将新增GLOBAL综合数据。</p>")
    elif has_global_updated:
        md_content.append("<p>本次导入将更新GLOBAL综合数据。</p>")
    elif has_global_unchanged:
        md_content.append("<p>本次导入的GLOBAL综合数据无变化。</p>")
    elif has_global_missing:
        md_content.append("<p>Excel中不包含GLOBAL综合数据，但数据库中的GLOBAL记录将被保留。</p>")
    
    md_content.append("</div>\n")

    md_content.append(f"## 统计信息\n")
    md_content.append("<div style='display: flex; flex-direction: column; gap: 5px;'>")
    md_content.append(f"<div><strong>涉及记录总数:</strong> {total_records} 条</div>")
    md_content.append(f"<div style='color: #2cbe4e;'><strong>新增记录:</strong> {len(new_records)} 条</div>")
    md_content.append(f"<div style='color: #dbab09;'><strong>更新记录:</strong> {len(updated_records)} 条</div>")
    md_content.append(f"<div style='color: #cb2431;'><strong>删除记录:</strong> {total_removed} 条</div>")
    md_content.append(f"<div style='color: #4078c0;'><strong>无变化记录:</strong> {len(unchanged_records)} 条</div>")
    md_content.append(
        f"<div style='color: #cb2431;'><strong>数据库存在但Excel不存在:</strong> {len(missing_in_excel_records)} 条</div>")
    md_content.append("</div>\n")

    # 添加新增记录
    if new_records:
        md_content.append("<h2 style='color: #2cbe4e;'>新增记录</h2>\n")

        for i, item in enumerate(new_records, 1):
            md_content.append(f"<h3 style='color: #2cbe4e;'>{i}. 方案编号: {item['project_code']}</h3>\n")

            # 确保内容按最后一列排序
            content_lines = sort_content_by_last_column(item['content']).split('\n')

            # 使用表格格式展示，更加直观
            md_content.append("<table>")
            md_content.append("<tr>")
            md_content.append("<th>状态</th>")
            md_content.append("<th>内容</th>")
            md_content.append("</tr>")

            for line in content_lines:
                if line.strip():  # 只处理非空行
                    md_content.append("<tr>")
                    md_content.append("<td style='color: #2cbe4e; font-weight: bold;'>新增</td>")
                    md_content.append(f"<td style='background-color: #e6ffec;'>{line}</td>")
                    md_content.append("</tr>")

            md_content.append("</table>\n")

    # 添加更新记录
    if updated_records:
        md_content.append("<h2 style='color: #dbab09;'>更新记录</h2>\n")

        for i, item in enumerate(updated_records, 1):
            md_content.append(f"<h3 style='color: #dbab09;'>{i}. 方案编号: {item['new']['project_code']}</h3>\n")

            diff_details = item.get('diff_details', {})
            added = diff_details.get('added', [])
            removed = diff_details.get('removed', [])
            changed = diff_details.get('changed', [])
            all_old_lines = diff_details.get('all_old_lines', [])
            all_new_lines = diff_details.get('all_new_lines', [])
            old_key_map = diff_details.get('old_key_map', {})
            new_key_map = diff_details.get('new_key_map', {})

            # 添加汇总信息
            md_content.append("<div style='display: flex; gap: 10px; margin-bottom: 10px;'>")
            if added:
                md_content.append(f"<div style='color: #2cbe4e;'><strong>新增:</strong> {len(added)} 条</div>")
            if removed:
                md_content.append(f"<div style='color: #cb2431;'><strong>删除:</strong> {len(removed)} 条</div>")
            if changed:
                md_content.append(f"<div style='color: #dbab09;'><strong>变更:</strong> {len(changed)} 条</div>")
            md_content.append("</div>\n")

            # 特别处理GLOBAL记录，提供完整的数据库旧内容与新内容对比
            if item['new']['project_code'] == 'GLOBAL':
                md_content.append("<div style='margin-bottom: 20px;'>")
                md_content.append("<h4>完整内容对比</h4>")
                
                # 提供一个下载链接或者嵌入更多信息的折叠面板
                md_content.append("<details>")
                md_content.append("<summary>点击展开查看旧内容与新内容的完整对比</summary>\n")
                
                md_content.append("<div style='display:flex; margin-top: 10px;'>")
                
                # 旧内容完整展示（左侧）
                md_content.append("<div style='flex: 1; margin-right: 10px;'>")
                md_content.append("<h5 style='margin: 0;'>数据库中的旧内容</h5>")
                md_content.append("<pre style='background-color: #f6f8fa; padding: 10px; max-height: 500px; overflow: auto;'>")
                old_lines_sorted = sort_content_by_last_column('\n'.join([line for line in all_old_lines if line.strip()])).split('\n')
                for line in old_lines_sorted:
                    if line.strip():
                        md_content.append(line)
                md_content.append("</pre>")
                md_content.append("</div>")
                
                # 新内容完整展示（右侧）
                md_content.append("<div style='flex: 1;'>")
                md_content.append("<h5 style='margin: 0;'>导入后的新内容</h5>")
                md_content.append("<pre style='background-color: #f6f8fa; padding: 10px; max-height: 500px; overflow: auto;'>")
                new_lines_sorted = sort_content_by_last_column('\n'.join([line for line in all_new_lines if line.strip()])).split('\n')
                for line in new_lines_sorted:
                    if line.strip():
                        md_content.append(line)
                md_content.append("</pre>")
                md_content.append("</div>")
                
                md_content.append("</div>")
                md_content.append("</details>\n")
                
                # 特别强调被删除的记录
                if removed:
                    md_content.append("<h4 style='color: #cb2431;'>被删除的记录</h4>")
                    md_content.append("<table>")
                    md_content.append("<tr>")
                    md_content.append("<th>状态</th>")
                    md_content.append("<th>方案编号</th>")
                    md_content.append("<th>药物名称</th>")
                    md_content.append("<th>药物编号</th>")
                    md_content.append("<th>完整内容</th>")
                    md_content.append("</tr>")
                    
                    for key, line, parts in removed:
                        project_code, drug_name, drug_id = parts
                        md_content.append("<tr>")
                        md_content.append("<td style='color: #cb2431; font-weight: bold;'>删除</td>")
                        md_content.append(f"<td>{project_code}</td>")
                        md_content.append(f"<td>{drug_name}</td>")
                        md_content.append(f"<td>{drug_id}</td>")
                        md_content.append(f"<td style='background-color: #ffeef0;'>{line}</td>")
                        md_content.append("</tr>")
                    
                    md_content.append("</table>\n")

                # 特别强调被新增的记录
                if added:
                    md_content.append("<h4 style='color: #2cbe4e;'>新增的记录</h4>")
                    md_content.append("<table>")
                    md_content.append("<tr>")
                    md_content.append("<th>状态</th>")
                    md_content.append("<th>方案编号</th>")
                    md_content.append("<th>药物名称</th>")
                    md_content.append("<th>药物编号</th>")
                    md_content.append("<th>完整内容</th>")
                    md_content.append("</tr>")
                    
                    for key, line, parts in added:
                        project_code, drug_name, drug_id = parts
                        md_content.append("<tr>")
                        md_content.append("<td style='color: #2cbe4e; font-weight: bold;'>新增</td>")
                        md_content.append(f"<td>{project_code}</td>")
                        md_content.append(f"<td>{drug_name}</td>")
                        md_content.append(f"<td>{drug_id}</td>")
                        md_content.append(f"<td style='background-color: #e6ffec;'>{line}</td>")
                        md_content.append("</tr>")
                    
                    md_content.append("</table>\n")

            # 使用左右比对方式展示变更
            if added or removed or changed:
                md_content.append("<div style='margin-bottom: 20px;'>")
                md_content.append("<h4>变更汇总</h4>")

                # 分类显示不同类型的变更
                # 1. 显示新增的记录
                if added:
                    md_content.append("<h5 style='color: #2cbe4e;'>新增记录</h5>")
                    md_content.append("<table width='100%' border='1' style='border-collapse: collapse;'>")
                    md_content.append("<tr style='background-color: #f6f8fa;'>")
                    md_content.append("<th>方案编号</th>")
                    md_content.append("<th>药物名称</th>")
                    md_content.append("<th>药物编号</th>")
                    md_content.append("<th>完整内容</th>")
                    md_content.append("</tr>")
                    
                    for key, line, parts in added:
                        project_code, drug_name, drug_id = parts
                        md_content.append("<tr>")
                        md_content.append(f"<td>{project_code}</td>")
                        md_content.append(f"<td>{drug_name}</td>")
                        md_content.append(f"<td>{drug_id}</td>")
                        md_content.append(f"<td style='background-color: #e6ffec;'>{line}</td>")
                        md_content.append("</tr>")
                    
                    md_content.append("</table>\n")
                
                # 2. 显示删除的记录
                if removed:
                    md_content.append("<h5 style='color: #cb2431;'>删除记录</h5>")
                    md_content.append("<table width='100%' border='1' style='border-collapse: collapse;'>")
                    md_content.append("<tr style='background-color: #f6f8fa;'>")
                    md_content.append("<th>方案编号</th>")
                    md_content.append("<th>药物名称</th>")
                    md_content.append("<th>药物编号</th>")
                    md_content.append("<th>完整内容</th>")
                    md_content.append("</tr>")
                    
                    for key, line, parts in removed:
                        project_code, drug_name, drug_id = parts
                        md_content.append("<tr>")
                        md_content.append(f"<td>{project_code}</td>")
                        md_content.append(f"<td>{drug_name}</td>")
                        md_content.append(f"<td>{drug_id}</td>")
                        md_content.append(f"<td style='background-color: #ffeef0;'>{line}</td>")
                        md_content.append("</tr>")
                    
                    md_content.append("</table>\n")
                
                # 3. 显示变更的记录（理论上应很少）
                if changed:
                    md_content.append("<h5 style='color: #dbab09;'>变更记录</h5>")
                    md_content.append("<table width='100%' border='1' style='border-collapse: collapse;'>")
                    md_content.append("<tr style='background-color: #f6f8fa;'>")
                    md_content.append("<th>旧内容</th>")
                    md_content.append("<th>新内容</th>")
                    md_content.append("</tr>")
                    
                    for key, old_line, new_line in changed:
                        md_content.append("<tr>")
                        md_content.append(f"<td style='background-color: #fff8e7;'>{old_line}</td>")
                        md_content.append(f"<td style='background-color: #e6ffec;'>{new_line}</td>")
                        md_content.append("</tr>")
                    
                    md_content.append("</table>")
                
                md_content.append("</div>\n")

    # 添加数据库存在但Excel不存在的记录
    if missing_in_excel_records:
        md_content.append("<h2 style='color: #cb2431;'>数据库存在但Excel不存在的记录</h2>\n")

        # 添加说明：GLOBAL记录处理方式
        md_content.append(
            "<div style='background-color: #ffeef0; padding: 10px; margin: 10px 0; border-left: 5px solid #cb2431;'>")
        md_content.append(
            "<p><strong>注意:</strong> 以下记录因在Excel中不存在而标记为缺失，如果包含project_code为\"GLOBAL\"的记录，将按照情况处理。</p>")
        md_content.append("</div>\n")

        for i, item in enumerate(missing_in_excel_records, 1):
            # 特殊标记GLOBAL记录
            if item['project_code'] == "GLOBAL":
                md_content.append(
                    f"<h3 style='color: #3498db;'>{i}. 方案编号: {item['project_code']} (GLOBAL综合数据)</h3>\n")
            else:
                md_content.append(f"<h3 style='color: #cb2431;'>{i}. 方案编号: {item['project_code']}</h3>\n")

            # 确保内容按最后一列排序
            content_lines = sort_content_by_last_column(item['content']).split('\n')

            # 使用表格格式展示，更加直观
            md_content.append("<table>")
            md_content.append("<tr>")
            md_content.append("<th>状态</th>")
            md_content.append("<th>内容</th>")
            md_content.append("</tr>")

            for line in content_lines:
                if line.strip():
                    md_content.append("<tr>")
                    # 特殊标记GLOBAL记录
                    if item['project_code'] == "GLOBAL":
                        md_content.append("<td style='color: #3498db; font-weight: bold;'>GLOBAL</td>")
                        md_content.append(f"<td style='background-color: #f0f7fb;'>{line}</td>")
                    else:
                        md_content.append("<td style='color: #cb2431; font-weight: bold;'>删除</td>")
                        md_content.append(f"<td style='background-color: #ffeef0;'>{line}</td>")
                    md_content.append("</tr>")

            md_content.append("</table>\n")

    # 添加无变化记录
    if unchanged_records:
        md_content.append("<h2 style='color: #4078c0;'>无变化记录</h2>\n")

        # 显示所有方案编号列表
        study_nums = [record['project_code'] for record in unchanged_records]
        md_content.append("<div style='color: #4078c0; margin-left: 20px;'>" + ", ".join(study_nums) + "</div>")

    # 添加注释说明
    md_content.append("\n<hr>\n")
    md_content.append("<div style='font-size: 0.9em; color: #6a737d;'>")
    md_content.append("<p><strong>注释说明:</strong></p>")
    md_content.append("<ul>")
    md_content.append("<li><span style='color: #2cbe4e;'>🔼 绿色</span>: 新增内容</li>")
    md_content.append("<li><span style='color: #cb2431;'>🔻 红色</span>: 删除内容</li>")
    md_content.append("<li><span style='color: #dbab09;'>⚠️ 黄色</span>: 变更内容</li>")
    md_content.append("<li><span style='color: #4078c0;'>蓝色</span>: 无变化记录</li>")
    md_content.append("<li><span style='color: #3498db;'>📌 蓝色</span>: GLOBAL综合数据记录</li>")
    md_content.append("<li><span style='color: #6a737d;'>☑ 灰色</span>: 未变化内容</li>")
    md_content.append("</ul>")
    md_content.append(
        "<p><strong>比对方式:</strong> 使用\"方案编号+药物名称+药物编号\"的组合作为比对依据，能够准确识别新增、删除和变更的记录</p>")
    md_content.append("<p><strong>特殊处理:</strong> project_code为\"GLOBAL\"的记录包含所有项目的pat_exposure数据合成信息，按方案编号和研究药物编号排序</p>")
    md_content.append("<p><strong>完整对比:</strong> 对于GLOBAL记录，提供完整的旧内容和新内容对比，并专门突出显示被删除和新增的记录</p>")
    md_content.append("</div>")

    # 将内容写入文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("\n".join(md_content))

    logger.info("pat_exposure字典数据的差异对比markdown文件已生成: {}", output_path)

    return output_path


def process_pat_exposure_data(df, tenant_id=None):
    """
    处理Excel数据，构建pat_exposure字典数据
    
    Args:
        df: DataFrame数据
        tenant_id: 租户ID，如果为None则使用Excel中的租户编号
    
    Returns:
        list: 处理后的pat_exposure字典数据列表
        str: 使用的租户ID
    """
    logger.info("开始处理pat_exposure字典数据...")

    # 如果未提供租户ID，则从Excel中获取第一个有数据的租户编号
    if tenant_id is None and "租户编号" in df.columns:
        # 获取第一个非空的租户编号
        valid_tenant_ids = df['租户编号'].dropna()
        if not valid_tenant_ids.empty:
            tenant_id = str(valid_tenant_ids.iloc[0]).strip()
            logger.info("从Excel中获取租户ID: {}", tenant_id)
        else:
            # 如果没有有效的租户编号，使用默认值
            tenant_id = "kelun"
            logger.warning("Excel中没有有效的租户编号，使用默认值: {}", tenant_id)
    elif tenant_id is None:
        # 如果Excel中没有租户编号列，使用默认值
        tenant_id = "kelun"
        logger.warning("Excel中未找到租户编号列，使用默认值: {}", tenant_id)

    # 确保研究药物编号列是字符串类型
    if "研究药物编号" in df.columns:
        df["研究药物编号"] = df["研究药物编号"].astype(str)

    # 按方案编号分组
    grouped = df.groupby('方案编号')

    # 存储结果数据列表
    result_data = []

    for study_num, group in grouped:
        # 收集该方案下的所有药物信息
        drugs_info = []

        for _, row in group.iterrows():
            # 确保获取的研究药物编号是字符串类型
            drug_id = str(row.get('研究药物编号', '')).strip()
            drug_name = str(row.get('研究药物名称中文', '')).strip()

            # 只有当药物编号和名称都不为空时才添加
            if drug_id and drug_id != 'nan' and drug_name and drug_name != 'nan':
                drugs_info.append({
                    'drug_id': drug_id,
                    'drug_name': drug_name
                })

        # 如果该方案有药物信息，创建一个字典记录
        if drugs_info:
            # 对药物信息按研究药物编号进行排序
            drugs_info.sort(key=lambda x: x['drug_id'])

            # 构建content内容
            content_lines = []

            # 对于每个有效的药物信息，添加一行
            for drug in drugs_info:
                content_line = f"|{study_num}|{drug['drug_name']}|{drug['drug_id']}|"
                content_lines.append(content_line)

            # 生成最终content字符串，使用换行符连接
            content = "\n".join(content_lines)

            # 构建pat_exposure字典数据，使用方案编号作为project_code
            result_data.append({
                "id": nanoid.generate(size=21),
                "company_code": tenant_id,
                "project_code": study_num,  # 使用方案编号作为project_code
                "module": PAT_EXPOSURE_CONFIG["module"],
                "placeholder": PAT_EXPOSURE_CONFIG["placeholder"],
                "content": content,
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "tenant_id": tenant_id
            })

    logger.info("pat_exposure字典数据处理完成，共 {} 条记录", len(result_data))
    
    # 处理GLOBAL数据
    global_data = process_global_pat_exposure_data(df, tenant_id)
    if global_data:
        result_data.append(global_data)
        logger.info("已添加GLOBAL综合数据记录")

    return result_data, tenant_id


def process_global_pat_exposure_data(df, tenant_id):
    """
    处理所有项目的pat_exposure数据，生成一条GLOBAL综合数据
    
    Args:
        df: DataFrame数据
        tenant_id: 租户ID
    
    Returns:
        dict: GLOBAL记录的字典数据
    """
    logger.info("开始处理GLOBAL综合数据...")
    
    # 收集所有项目的所有药物信息
    all_drugs_info = []
    
    # 确保研究药物编号列是字符串类型
    if "研究药物编号" in df.columns:
        df["研究药物编号"] = df["研究药物编号"].astype(str)
    
    # 遍历所有行
    for _, row in df.iterrows():
        study_num = str(row.get('方案编号', '')).strip()
        drug_id = str(row.get('研究药物编号', '')).strip()
        drug_name = str(row.get('研究药物名称中文', '')).strip()
        
        # 只有当方案编号、药物编号和名称都不为空时才添加
        if study_num and study_num != 'nan' and drug_id and drug_id != 'nan' and drug_name and drug_name != 'nan':
            # 检查是否已存在相同的组合，避免重复
            existing = next((item for item in all_drugs_info if 
                            item['study_num'] == study_num and 
                            item['drug_id'] == drug_id), None)
            
            if not existing:
                all_drugs_info.append({
                    'study_num': study_num,
                    'drug_name': drug_name,
                    'drug_id': drug_id
                })
    
    # 如果有药物信息，创建GLOBAL记录
    if all_drugs_info:
        # 首先按方案编号排序，然后按研究药物编号排序
        all_drugs_info.sort(key=lambda x: (x['study_num'], x['drug_id']))
        
        # 构建content内容
        content_lines = []
        
        # 对于每个药物信息，添加一行
        for drug in all_drugs_info:
            content_line = f"|{drug['study_num']}|{drug['drug_name']}|{drug['drug_id']}|"
            content_lines.append(content_line)
        
        # 生成最终content字符串，使用换行符连接
        content = "\n".join(content_lines)
        
        # 构建GLOBAL记录
        global_data = {
            "id": nanoid.generate(size=21),
            "company_code": tenant_id,
            "project_code": "GLOBAL",  # 使用GLOBAL作为project_code
            "module": PAT_EXPOSURE_CONFIG["module"],
            "placeholder": PAT_EXPOSURE_CONFIG["placeholder"],
            "content": content,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "tenant_id": tenant_id
        }
        
        logger.info("GLOBAL综合数据处理完成，包含 {} 条药物信息", len(all_drugs_info))
        return global_data
    
    logger.warning("没有有效的药物信息，不生成GLOBAL记录")
    return None


def fetch_existing_pat_exposure_data(session: Session, tenant_id=None):
    """
    从数据库中获取现有的pat_exposure字典数据
    
    Args:
        session: 数据库会话
        tenant_id: 租户ID，如果提供则按租户ID过滤
    
    Returns:
        dict: 以project_code为键的现有pat_exposure字典数据字典
    """
    logger.info("正在查询数据库中的现有pat_exposure字典数据...")

    try:
        # 准备查询语句，必须带上租户ID
        if not tenant_id:
            logger.warning("未提供租户ID，无法查询pat_exposure字典数据")
            return {}

        query = text("""
        SELECT id, company_code, project_code, module, placeholder, content, created_at, updated_at, tenant_id
        FROM dictionary_mapping
        WHERE company_code = :company_code 
        AND module = :module 
        AND placeholder = :placeholder
        AND tenant_id = :tenant_id
        """)

        result = session.execute(query, {
            "company_code": tenant_id,
            "module": PAT_EXPOSURE_CONFIG["module"],
            "placeholder": PAT_EXPOSURE_CONFIG["placeholder"],
            "tenant_id": tenant_id
        })

        # 获取结果，以project_code为键构建字典
        existing_data = {}
        for row in result:
            existing_data[row.project_code] = {
                "id": row.id,
                "company_code": row.company_code,
                "project_code": row.project_code,
                "module": row.module,
                "placeholder": row.placeholder,
                "content": row.content,
                "created_at": row.created_at,
                "updated_at": row.updated_at,
                "tenant_id": row.tenant_id
            }

        logger.info("找到 {} 条现有的pat_exposure字典数据", len(existing_data))
        return existing_data

    except Exception as e:
        logger.error("查询pat_exposure字典数据失败: {}", str(e))
        return {}


def preview_pat_exposure_data(data_list):
    """
    记录处理后的pat_exposure字典数据信息，但不在控制台展示
    
    Args:
        data_list: 处理后的pat_exposure字典数据列表
    """
    if not data_list:
        logger.warning("没有pat_exposure字典数据可预览")
        return

    total_records = len(data_list)
    total_lines = sum(len(data.get("content", "").split("\n")) for data in data_list)

    logger.info("处理后的pat_exposure字典数据共 {} 条记录，{} 行内容", total_records, total_lines)
    # 不再打印实际数据到控制台


def preview_pat_exposure_comparison(new_records, updated_records, unchanged_records, missing_in_excel_records=None):
    """
    记录pat_exposure字典数据的比对结果，不在控制台展示，但仍生成差异对比的markdown文件
    
    Args:
        new_records: 新增数据列表
        updated_records: 更新数据列表
        unchanged_records: 无变化数据列表
        missing_in_excel_records: 数据库中存在但Excel中不存在的记录列表
        
    Returns:
        str: 生成的差异对比markdown文件路径
    """
    # 如果missing_in_excel_records为None，设置为空列表
    if missing_in_excel_records is None:
        missing_in_excel_records = []

    # 检查各类记录中是否有GLOBAL记录
    global_new = [record for record in new_records if record.get('project_code') == 'GLOBAL']
    global_updated = [record for record in updated_records if record['new'].get('project_code') == 'GLOBAL']
    global_unchanged = [record for record in unchanged_records if record.get('project_code') == 'GLOBAL']
    global_missing = [record for record in missing_in_excel_records if record.get('project_code') == 'GLOBAL']

    # 统计总数
    total = len(new_records) + len(updated_records) + len(unchanged_records) + len(missing_in_excel_records)

    logger.info("pat_exposure字典数据比对结果（总计 {} 条记录）:", total)
    logger.info("  新增记录: {} 条", len(new_records))
    logger.info("  更新记录: {} 条", len(updated_records))
    logger.info("  无变化记录: {} 条", len(unchanged_records))
    logger.info("  数据库存在但Excel中不存在的记录: {} 条", len(missing_in_excel_records))

    # 添加GLOBAL记录特殊说明
    if global_new:
        logger.info("  其中GLOBAL记录: {} 条 (新增)", len(global_new))
    elif global_updated:
        logger.info("  其中GLOBAL记录: {} 条 (更新)", len(global_updated))
    elif global_unchanged:
        logger.info("  其中GLOBAL记录: {} 条 (无变化)", len(global_unchanged))
    elif global_missing:
        logger.info("  其中GLOBAL记录: {} 条 (在Excel中不存在，但会保留)", len(global_missing))

    logger.info("特别说明: project_code为GLOBAL的记录包含所有项目的pat_exposure数据合成信息，按方案编号和研究药物编号排序")

    # 生成差异对比的markdown文件
    diff_file = generate_pat_exposure_diff_markdown(new_records, updated_records, unchanged_records,
                                                    missing_in_excel_records)
    logger.info("详细差异已输出到文件: {}", diff_file)

    return diff_file


def import_pat_exposure_data(new_records, updated_records, session: Session, tenant_id=None,
                             missing_in_excel_records=None):
    """
    将pat_exposure字典数据导入MySQL数据库，先清空原有的租户记录再重新导入
    特殊处理：保留或更新project_code为"GLOBAL"的记录
    
    Args:
        new_records: 新增数据列表
        updated_records: 更新数据列表，其中包含变更的新旧数据
        session: 数据库会话
        tenant_id: 租户ID，如果未提供则尝试从记录中获取
        missing_in_excel_records: 数据库中存在但Excel中不存在的记录列表
    
    Returns:
        bool: 操作是否成功
    """
    # 如果新增和更新记录都为空，无需导入
    if not new_records and not updated_records:
        logger.warning("没有pat_exposure字典数据需要导入")
        return False

    # 如果missing_in_excel_records为None，设置为空列表
    if missing_in_excel_records is None:
        missing_in_excel_records = []

    # 如果未提供租户ID，尝试从数据中获取
    if not tenant_id:
        # 从新增记录中获取
        if new_records and "tenant_id" in new_records[0] and new_records[0]["tenant_id"]:
            tenant_id = new_records[0]["tenant_id"]
        # 从更新记录中获取
        elif updated_records and "new" in updated_records[0] and "tenant_id" in updated_records[0]["new"] and updated_records[0]["new"]["tenant_id"]:
            tenant_id = updated_records[0]["new"]["tenant_id"]

    # 如果仍然无法获取租户ID，记录错误并返回
    if not tenant_id:
        logger.error("无法获取租户ID，无法导入pat_exposure字典数据")
        return False

    # 获取要导入的module和placeholder
    module = PAT_EXPOSURE_CONFIG["module"]
    placeholder = PAT_EXPOSURE_CONFIG["placeholder"]

    logger.info("开始导入pat_exposure字典数据到MySQL，租户ID: {}", tenant_id)

    # 开始事务
    transaction = session.begin_nested()
    try:
        logger.info("开始pat_exposure字典数据事务处理...")

        # 找出新增或更新记录中的GLOBAL记录
        global_record = None
        for idx, record in enumerate(new_records):
            if record.get("project_code") == "GLOBAL":
                global_record = record
                # 从新增记录中移除GLOBAL记录，后面单独处理
                new_records.pop(idx)
                break
                
        if not global_record:
            for idx, record in enumerate(updated_records):
                if record["new"].get("project_code") == "GLOBAL":
                    global_record = record["new"]
                    # 从更新记录中移除GLOBAL记录，后面单独处理
                    updated_records.pop(idx)
                    break

        # 先查询要删除的pat_exposure数据，排除GLOBAL记录
        query_sql = text("""
        SELECT id, company_code, project_code, module, placeholder
        FROM dictionary_mapping
        WHERE tenant_id = :tenant_id
        AND module = :module
        AND placeholder = :placeholder
        AND project_code != 'GLOBAL'
        """)

        delete_records = session.execute(query_sql, {
            "tenant_id": tenant_id,
            "module": module,
            "placeholder": placeholder
        }).fetchall()

        # 查询GLOBAL记录
        global_query_sql = text("""
        SELECT id, company_code, project_code, module, placeholder, content
        FROM dictionary_mapping
        WHERE tenant_id = :tenant_id
        AND module = :module
        AND placeholder = :placeholder
        AND project_code = 'GLOBAL'
        """)

        existing_global_records = session.execute(global_query_sql, {
            "tenant_id": tenant_id,
            "module": module,
            "placeholder": placeholder
        }).fetchall()

        existing_global_record = existing_global_records[0] if existing_global_records else None

        if existing_global_record:
            logger.info("检测到现有GLOBAL记录，将进行更新")
        else:
            logger.info("未检测到现有GLOBAL记录，将创建新记录")

        # 记录要删除的数据
        if delete_records:
            logger.info("将删除租户 {} 的 {} 条pat_exposure字典数据:", tenant_id, len(delete_records))
            for idx, record in enumerate(delete_records, 1):
                if idx <= 10:  # 只显示前10条，避免日志过长
                    logger.info("  {}. 方案编号: {}, 模块: {}, 占位符: {}",
                                idx, record.project_code, record.module, record.placeholder)
                elif idx == 11:
                    logger.info("  ... 还有 {} 条记录未显示", len(delete_records) - 10)
        else:
            logger.info("租户 {} 当前没有pat_exposure字典数据需要删除", tenant_id)

        # 执行删除操作，排除GLOBAL记录
        delete_sql = text("""
        DELETE FROM dictionary_mapping
        WHERE tenant_id = :tenant_id
        AND module = :module
        AND placeholder = :placeholder
        AND project_code != 'GLOBAL'
        """)

        result = session.execute(delete_sql, {
            "tenant_id": tenant_id,
            "module": module,
            "placeholder": placeholder
        })
        deleted_rows = result.rowcount
        logger.info("已删除租户 {} 的 {} 条现有pat_exposure字典数据，保留GLOBAL记录", tenant_id, deleted_rows)

        # 记录插入总数
        inserted_count = 0

        # 准备插入语句
        insert_sql = text("""
        INSERT INTO dictionary_mapping
        (id, company_code, project_code, module, placeholder, content, created_at, updated_at, tenant_id)
        VALUES
        (:id, :company_code, :project_code, :module, :placeholder, :content, :created_at, :updated_at, :tenant_id)
        """)

        # 准备更新语句
        update_sql = text("""
        UPDATE dictionary_mapping
        SET content = :content, updated_at = :updated_at
        WHERE id = :id
        """)

        # 处理GLOBAL记录
        if global_record:
            if existing_global_record:
                # 更新现有GLOBAL记录
                session.execute(update_sql, {
                    "content": global_record["content"],
                    "updated_at": datetime.now(),
                    "id": existing_global_record.id
                })
                logger.info("已更新现有GLOBAL记录")
            else:
                # 插入新的GLOBAL记录
                session.execute(insert_sql, global_record)
                logger.info("已插入新的GLOBAL记录")
            
            inserted_count += 1

        # 处理新增记录
        for item in new_records:
            try:
                # 确保所有记录使用相同的租户ID
                item["tenant_id"] = tenant_id
                session.execute(insert_sql, item)
                inserted_count += 1
            except Exception as e:
                logger.error("插入pat_exposure记录失败 [方案编号: {}]: {}", item.get("project_code", "未知"), str(e))
                raise

        # 处理更新记录（以新数据为准）
        for item in updated_records:
            try:
                # 获取新数据并确保租户ID一致
                new_item = item["new"]
                new_item["tenant_id"] = tenant_id

                # 插入新的记录数据
                session.execute(insert_sql, new_item)
                inserted_count += 1
            except Exception as e:
                logger.error("插入更新的pat_exposure记录失败 [方案编号: {}]: {}",
                             item["new"].get("project_code", "未知"), str(e))
                raise

        # 提交事务
        transaction.commit()
        session.commit()
        logger.info(
            "pat_exposure字典数据事务已提交，成功导入数据到MySQL，共删除 {} 条旧数据，插入 {} 条新数据，处理GLOBAL记录",
            deleted_rows, inserted_count)

        # 打印最终的操作摘要
        logger.info("=== pat_exposure数据导入摘要 ===")
        logger.info("租户ID: {}", tenant_id)
        logger.info("模块: {}", module)
        logger.info("占位符: {}", placeholder)
        logger.info("删除记录: {} 条", deleted_rows)
        logger.info("GLOBAL记录: {}", "已更新" if existing_global_record and global_record else "已新增" if global_record else "无变化")
        logger.info("新增记录: {} 条", len(new_records))
        logger.info("更新记录: {} 条", len(updated_records))
        logger.info("数据库存在但Excel中不存在的记录: {} 条", len(missing_in_excel_records))
        logger.info("实际插入或更新: {} 条", inserted_count)
        logger.info("==========================")

        return True

    except Exception as e:
        # 回滚事务
        try:
            transaction.rollback()
            session.rollback()
            logger.error("pat_exposure字典数据事务已回滚，导入数据到MySQL失败: {}", str(e))
        except Exception as rollback_error:
            logger.error("pat_exposure字典数据事务回滚失败: {}", str(rollback_error))

        # 将异常向上传递
        raise Exception(f"导入pat_exposure字典数据失败: {str(e)}")

    finally:
        logger.info("pat_exposure字典数据事务处理结束")
        return False


def delete_diff_files(file_paths):
    """
    删除已生成的差异文件
    
    Args:
        file_paths: 要删除的文件路径列表
    
    Returns:
        bool: 是否全部删除成功
    """
    success = True
    for file_path in file_paths:
        if file_path and os.path.exists(file_path):
            try:
                os.remove(file_path)
                logger.info("已删除文件: {}", file_path)
            except Exception as e:
                logger.error("删除文件失败 {}: {}", file_path, str(e))
                success = False

    return success


def main():
    """
    主函数
    """
    # 初始化存储生成文件路径的列表
    generated_files = []
    # 标记是否发生了未处理的异常
    unhandled_exception_occurred = False

    try:
        # 获取Excel文件目录
        excel_dir = os.path.dirname(__file__)
        logger.info("正在查找目录中的Excel文件: {}", excel_dir)
        
        # 搜索目录中的所有Excel文件
        excel_files = []
        for file in os.listdir(excel_dir):
            if file.endswith('.xlsx') or file.endswith('.xls'):
                excel_files.append(file)
        
        if not excel_files:
            logger.error("在目录中未找到Excel文件: {}", excel_dir)
            print(f"错误：在{excel_dir}目录中未找到Excel文件")
            sys.exit(1)
        
        # 显示Excel文件选择菜单
        print("\n" + "=" * 80)
        print("               请选择要导入的Excel文件               ")
        print("=" * 80)
        
        for i, file in enumerate(excel_files, 1):
            print(f"{i}. {file}")
        
        print("-" * 80)
        
        # 获取用户选择
        while True:
            try:
                choice = input("\n请输入文件编号: ").strip()
                choice_idx = int(choice) - 1
                
                if 0 <= choice_idx < len(excel_files):
                    selected_file = excel_files[choice_idx]
                    excel_path = os.path.join(excel_dir, selected_file)
                    logger.info("已选择Excel文件: {}", excel_path)
                    print(f"已选择: {selected_file}")
                    break
                else:
                    print(f"无效的选择，请输入1到{len(excel_files)}之间的数字")
            except ValueError:
                print("请输入有效的数字")
        
        # 检查文件是否存在
        if not os.path.exists(excel_path):
            logger.error("Excel文件不存在: {}", excel_path)
            sys.exit(1)
            
        # 询问是否为kelun租户
        print("\n" + "=" * 80)
        print("               请选择租户类型               ")
        print("=" * 80)
        print("1. kelun租户 (将导入study_info表和pat_exposure字典数据)")
        print("2. 非kelun租户 (仅导入study_info表数据)")
        print("-" * 80)
        
        # 获取用户选择的租户类型
        is_kelun_tenant = False
        while True:
            try:
                tenant_choice = input("\n请输入选项编号 (1/2): ").strip()
                
                if tenant_choice == "1":
                    is_kelun_tenant = True
                    logger.info("已选择kelun租户，将导入两个表的数据")
                    print("已选择: kelun租户 (将导入study_info表和pat_exposure字典数据)")
                    break
                elif tenant_choice == "2":
                    is_kelun_tenant = False
                    logger.info("已选择非kelun租户，将仅导入study_info表数据")
                    print("已选择: 非kelun租户 (仅导入study_info表数据)")
                    break
                else:
                    print("无效的选择，请输入1或2")
            except ValueError:
                print("请输入有效的数字")

        # 读取Excel数据
        df = load_excel_data(excel_path)
        
        # 如果是非kelun租户，检查是否需要从文件名中提取租户ID
        specified_tenant_id = None
        if not is_kelun_tenant:
            # 检查Excel是否包含"租户编号"列
            has_tenant_column = EXCEL_COLUMN_CONFIG["租户编号"] in df.columns
            
            if not has_tenant_column:
                logger.info("Excel中未找到租户编号列，将从文件名中提取租户ID")
                print("\n未在Excel中找到租户编号列，将尝试从文件名中提取租户ID")
                
                # 从文件名中提取租户ID
                filename = os.path.basename(excel_path)
                # 假设文件名格式为"租户ID-方案产品知识库-V版本号-日期.xlsx"
                parts = filename.split('-')
                
                if len(parts) > 0:
                    extracted_tenant_id = parts[0].strip()
                    specified_tenant_id = extracted_tenant_id
                    logger.info("从文件名中提取的租户ID: {}", specified_tenant_id)
                    print(f"已自动提取租户ID: {specified_tenant_id}")
                else:
                    # 文件名不符合预期格式，让用户手动输入
                    logger.warning("无法从文件名中提取租户ID: {}", filename)
                    print("\n无法从文件名中提取租户ID，请手动输入")
                    
                    while True:
                        manual_tenant_id = input("请输入租户ID: ").strip()
                        if manual_tenant_id:
                            specified_tenant_id = manual_tenant_id
                            logger.info("将使用手动输入的租户ID: {}", specified_tenant_id)
                            print(f"已确认使用租户ID: {specified_tenant_id}")
                            break
                        else:
                            print("租户ID不能为空，请重新输入")

        # 检查具有多个受理号的方案编号
        check_multiple_acceptance_numbers(df)

        # 处理study_info数据，如果指定了租户ID则使用它
        data, tenant_id = process_data(df, specified_tenant_id)

        # 如果是kelun租户，才处理pat_exposure字典数据
        pat_exposure_data_list = []
        if is_kelun_tenant:
            # 处理pat_exposure字典数据
            pat_exposure_data_list, _ = process_pat_exposure_data(df, tenant_id)
            logger.info("已处理pat_exposure字典数据，共 {} 条记录", len(pat_exposure_data_list))
        else:
            logger.info("非kelun租户，跳过pat_exposure字典数据处理")

        # 如果数据处理成功
        if data:
            # 记录处理后的数据信息
            preview_data(data)

            # 如果是kelun租户，才显示和比对pat_exposure字典数据
            if is_kelun_tenant and pat_exposure_data_list:
                # 记录处理后的pat_exposure字典数据信息
                preview_pat_exposure_data(pat_exposure_data_list)

            # 创建数据库会话
            Session = db_session
            with Session() as session:
                # 获取现有study_info数据，按租户ID过滤
                existing_data = fetch_existing_data(session, tenant_id)

                # 比对study_info数据差异
                new_records, updated_records, unchanged_records, missing_in_excel_records = compare_data(data,
                                                                                                       existing_data)

                # 生成study_info差异对比文件并记录结果
                study_info_diff_file = preview_comparison(new_records, updated_records, unchanged_records,
                                                        missing_in_excel_records)
                
                # 初始化比对变量
                pat_exposure_new = []
                pat_exposure_updated = []
                pat_exposure_unchanged = []
                pat_exposure_missing = []
                pat_exposure_diff_file = None
                
                # 如果是kelun租户，才比对pat_exposure字典数据
                if is_kelun_tenant and pat_exposure_data_list:
                    # 获取现有pat_exposure字典数据
                    existing_pat_exposure_data = fetch_existing_pat_exposure_data(session, tenant_id)

                    # 比对pat_exposure字典数据差异
                    pat_exposure_new, pat_exposure_updated, pat_exposure_unchanged, pat_exposure_missing = compare_pat_exposure_data(
                        pat_exposure_data_list, existing_pat_exposure_data)

                    # 生成pat_exposure字典数据差异对比文件并记录结果
                    pat_exposure_diff_file = preview_pat_exposure_comparison(pat_exposure_new, pat_exposure_updated,
                                                                         pat_exposure_unchanged, pat_exposure_missing)

                # 保存生成的文件路径，用于后续可能的删除
                generated_files = [study_info_diff_file]
                if pat_exposure_diff_file:
                    generated_files.append(pat_exposure_diff_file)

                # 提示用户查看差异文件
                logger.info("请查看差异文件了解详细变更：")
                logger.info("  study_info数据差异文件: {}", study_info_diff_file)
                if pat_exposure_diff_file:
                    logger.info("  pat_exposure字典数据差异文件: {}", pat_exposure_diff_file)

                # 询问用户是否要导入数据
                while True:
                    action = confirm_import()

                    if action == "import":
                        # 确认导入
                        confirmation = input("\n确认导入数据吗？这将新增和更新记录 (y/n): ").strip().lower()

                        if confirmation in ['y', 'yes']:
                            # 创建一个包含所有Excel数据的字典 - study_info
                            all_excel_records = new_records.copy()

                            # 把更新记录中的新数据添加进来
                            for item in updated_records:
                                all_excel_records.append(item["new"])

                            # 把无变化记录也添加进来
                            all_excel_records.extend(unchanged_records)

                            # 创建导入数据字典，将所有Excel记录作为新增记录
                            import_data = {"new": all_excel_records, "updated": []}

                            # 开始一个嵌套事务，适用于会话可能已经在事务中的情况
                            main_transaction = session.begin_nested()
                            try:
                                logger.info("开始主事务处理...")

                                # 导入study_info数据
                                logger.info("准备导入全部Excel数据，共 {} 条记录", len(all_excel_records))

                                # 先查询要删除的study_info数据
                                query_sql = text("""
                                SELECT id, tenant_id, study_num, study_data 
                                FROM study_info 
                                WHERE tenant_id = :tenant_id
                                """)

                                delete_records = session.execute(query_sql, {"tenant_id": tenant_id}).fetchall()

                                # 记录要删除的数据
                                if delete_records:
                                    logger.info("将删除租户 {} 的 {} 条study_info数据", tenant_id, len(delete_records))

                                # 执行删除操作
                                delete_sql = text("""
                                DELETE FROM study_info 
                                WHERE tenant_id = :tenant_id
                                """)

                                result = session.execute(delete_sql, {"tenant_id": tenant_id})
                                deleted_rows = result.rowcount
                                logger.info("已删除租户 {} 的 {} 条现有study_info数据", tenant_id, deleted_rows)

                                # 插入study_info数据
                                insert_sql = text("""
                                INSERT INTO study_info 
                                (id, tenant_id, study_num, study_data, created_at, updated_at)
                                VALUES 
                                (:id, :tenant_id, :study_num, :study_data, :created_at, :updated_at)
                                """)

                                inserted_count = 0
                                for item in all_excel_records:
                                    try:
                                        # 确保所有记录使用相同的租户ID
                                        item["tenant_id"] = tenant_id
                                        session.execute(insert_sql, item)
                                        inserted_count += 1
                                    except Exception as e:
                                        logger.error("插入study_info记录失败 [方案编号: {}]: {}",
                                                     item.get("study_num", "未知"), str(e))
                                        raise

                                logger.info("已插入 {} 条study_info记录", inserted_count)

                                # 仅对kelun租户处理pat_exposure字典数据
                                pat_inserted_count = 0
                                if is_kelun_tenant and pat_exposure_data_list:
                                    # 获取要导入的module和placeholder
                                    module = PAT_EXPOSURE_CONFIG["module"]
                                    placeholder = PAT_EXPOSURE_CONFIG["placeholder"]

                                    # 先查询要删除的pat_exposure数据，排除GLOBAL记录
                                    query_sql = text("""
                                    SELECT id, company_code, project_code, module, placeholder
                                    FROM dictionary_mapping
                                    WHERE tenant_id = :tenant_id
                                    AND module = :module
                                    AND placeholder = :placeholder
                                    AND project_code != 'GLOBAL'
                                    """)

                                    delete_records = session.execute(query_sql, {
                                        "tenant_id": tenant_id,
                                        "module": module,
                                        "placeholder": placeholder
                                    }).fetchall()

                                    # 查询GLOBAL记录，仅用于记录日志
                                    global_query_sql = text("""
                                    SELECT id, company_code, project_code, module, placeholder, content
                                    FROM dictionary_mapping
                                    WHERE tenant_id = :tenant_id
                                    AND module = :module
                                    AND placeholder = :placeholder
                                    AND project_code = 'GLOBAL'
                                    """)

                                    global_records = session.execute(global_query_sql, {
                                        "tenant_id": tenant_id,
                                        "module": module,
                                        "placeholder": placeholder
                                    }).fetchall()

                                    existing_global_record = global_records[0] if global_records else None

                                    if existing_global_record:
                                        logger.info("检测到现有GLOBAL记录，将进行更新")
                                    else:
                                        logger.info("未检测到现有GLOBAL记录，将创建新记录")

                                    # 记录要删除的数据
                                    if delete_records:
                                        logger.info("将删除租户 {} 的 {} 条pat_exposure字典数据", tenant_id,
                                                    len(delete_records))

                                    # 执行删除操作，排除GLOBAL记录
                                    delete_sql = text("""
                                    DELETE FROM dictionary_mapping
                                    WHERE tenant_id = :tenant_id
                                    AND module = :module
                                    AND placeholder = :placeholder
                                    AND project_code != 'GLOBAL'
                                    """)

                                    result = session.execute(delete_sql, {
                                        "tenant_id": tenant_id,
                                        "module": module,
                                        "placeholder": placeholder
                                    })
                                    deleted_pat_rows = result.rowcount
                                    logger.info("已删除租户 {} 的 {} 条现有pat_exposure字典数据，保留GLOBAL记录", tenant_id,
                                                deleted_pat_rows)

                                    # 插入pat_exposure数据
                                    insert_sql = text("""
                                    INSERT INTO dictionary_mapping
                                    (id, company_code, project_code, module, placeholder, content, created_at, updated_at, tenant_id)
                                    VALUES
                                    (:id, :company_code, :project_code, :module, :placeholder, :content, :created_at, :updated_at, :tenant_id)
                                    """)

                                    # 准备更新语句
                                    update_sql = text("""
                                    UPDATE dictionary_mapping
                                    SET content = :content, updated_at = :updated_at
                                    WHERE id = :id
                                    """)

                                    global_record_processed = False

                                    for item in pat_exposure_data_list:
                                        try:
                                            # 处理GLOBAL记录
                                            if item.get("project_code") == "GLOBAL":
                                                global_record_processed = True
                                                if existing_global_record:
                                                    # 更新现有GLOBAL记录
                                                    session.execute(update_sql, {
                                                        "content": item["content"],
                                                        "updated_at": datetime.now(),
                                                        "id": existing_global_record.id
                                                    })
                                                    logger.info("已更新现有GLOBAL记录")
                                                else:
                                                    # 插入新的GLOBAL记录
                                                    session.execute(insert_sql, item)
                                                    logger.info("已插入新的GLOBAL记录")
                                                
                                                pat_inserted_count += 1
                                                continue

                                            # 确保所有记录使用相同的租户ID
                                            item["tenant_id"] = tenant_id
                                            session.execute(insert_sql, item)
                                            pat_inserted_count += 1
                                        except Exception as e:
                                            logger.error("插入pat_exposure记录失败 [方案编号: {}]: {}",
                                                         item.get("project_code", "未知"), str(e))
                                            raise

                                    logger.info("已插入或更新 {} 条pat_exposure字典记录", pat_inserted_count)

                                # 提交主事务
                                main_transaction.commit()
                                # 提交整个会话
                                session.commit()
                                logger.info("主事务已提交，所有数据导入成功")

                                # 打印最终的操作摘要 - study_info
                                logger.info("=== study_info数据导入摘要 ===")
                                logger.info("租户ID: {}", tenant_id)
                                logger.info("删除记录: {} 条", deleted_rows)
                                logger.info("新增记录: {} 条", len(new_records))
                                logger.info("更新记录: {} 条", len(updated_records))
                                logger.info("实际插入: {} 条", inserted_count)
                                logger.info("==================")

                                # 如果是kelun租户，还打印pat_exposure数据导入摘要
                                if is_kelun_tenant and pat_exposure_data_list:
                                    # 打印最终的操作摘要 - pat_exposure
                                    logger.info("=== pat_exposure数据导入摘要 ===")
                                    logger.info("租户ID: {}", tenant_id)
                                    logger.info("模块: {}", module)
                                    logger.info("占位符: {}", placeholder)
                                    logger.info("删除记录: {} 条", deleted_pat_rows)
                                    logger.info("GLOBAL记录: {}", "已更新" if existing_global_record and global_record_processed else "已新增" if global_record_processed else "无变化")
                                    logger.info("新增记录: {} 条", len(pat_exposure_new))
                                    logger.info("更新记录: {} 条", len(pat_exposure_updated))
                                    logger.info("数据库存在但Excel中不存在的记录: {} 条", len(pat_exposure_missing))
                                    logger.info("实际插入或更新: {} 条", pat_inserted_count)
                                    logger.info("==========================")

                                # 导入成功，提示用户差异文件位置
                                logger.info("导入成功，差异文件已保留供参考:")
                                logger.info("  study_info数据差异文件: {}", study_info_diff_file)
                                if is_kelun_tenant and pat_exposure_diff_file:
                                    logger.info("  pat_exposure字典数据差异文件: {}", pat_exposure_diff_file)
                                print("\n导入操作已成功完成，差异文件已保留供参考")

                            except Exception as e:
                                # 回滚主事务
                                try:
                                    main_transaction.rollback()
                                    # 回滚整个会话
                                    session.rollback()
                                    logger.error("主事务已回滚，数据导入失败: {}", str(e))
                                except Exception as rollback_error:
                                    logger.error("事务回滚失败: {}", str(rollback_error))

                                # 删除生成的差异文件
                                logger.info("由于事务失败，正在删除已生成的差异文件...")
                                delete_diff_files(generated_files)
                                logger.error("导入操作已完全回滚")
                                # 清空文件列表，因为已经删除了
                                generated_files = []

                                # 提示用户操作失败
                                print(f"\n导入操作失败: {str(e)}")
                                print("所有变更已回滚，差异文件已删除")

                            break
                        else:
                            # 用户取消导入，删除差异文件
                            logger.info("用户取消导入，正在删除已生成的差异文件...")
                            delete_diff_files(generated_files)
                            print("已取消导入操作，差异文件已删除")
                            # 清空文件列表，因为已经删除了
                            generated_files = []
                            break
                    else:  # cancel
                        # 用户取消操作，删除差异文件
                        logger.info("用户取消操作，正在删除已生成的差异文件...")
                        delete_diff_files(generated_files)
                        logger.info("用户取消操作，差异文件已删除")
                        # 清空文件列表，因为已经删除了
                        generated_files = []
                        break
        else:
            logger.info("无有效数据可处理，程序已退出")

    except Exception as e:
        # 捕获所有未处理的异常
        logger.error("程序执行过程中发生未处理的异常: {}", str(e))
        print(f"\n程序执行失败: {str(e)}")

        # 打印异常堆栈跟踪，方便调试
        import traceback
        logger.error("异常堆栈跟踪: {}", traceback.format_exc())

        # 标记发生了未处理的异常
        unhandled_exception_occurred = True

    finally:
        # 只有在未处理的异常情况下，才清理剩余的差异文件
        if unhandled_exception_occurred and generated_files:
            logger.info("程序异常退出，正在清理所有生成的差异文件...")
            delete_diff_files(generated_files)
            logger.info("所有差异文件已清理完毕")


if __name__ == "__main__":
    main()
