def generate_sql_statements(data_text: str) -> str:
    # 分割每行数据
    lines = data_text.strip().split('\n')

    # SQL语句模板
    sql_template = """INSERT INTO study_info (id, tenant_id, study_num, study_data, created_at, updated_at) VALUES"""

    values = []
    for line in lines:
        # 分割研究编号和研究数据
        parts = line.split('\t')
        if len(parts) != 2:
            continue

        study_num, study_data = parts

        # 清理数据中的引号
        study_data = study_data.strip('"')

        # 生成单条数据的VALUES部分
        value = f"""(
            REPLACE(UUID(), '-', ''),
            'kelun',
            '{study_num}',
            '{study_data}',
            NOW(),
            NOW()
        )"""
        values.append(value)

    # 每20条数据生成一个INSERT语句
    batch_size = 20
    batches = []

    for i in range(0, len(values), batch_size):
        batch_values = values[i:i + batch_size]
        batch_sql = f"{sql_template}\n" + ",\n".join(batch_values) + ";"
        batches.append(batch_sql)

    return "\n\n".join(batches)


def main():
    # 读取数据文件
    with open('study_data.txt', 'r', encoding='utf-8') as f:
        data = f.read()

    # 生成SQL语句
    sql_statements = generate_sql_statements(data)

    # 将SQL语句写入文件
    with open('insert_study_info.sql', 'w', encoding='utf-8') as f:
        f.write(sql_statements)


if __name__ == "__main__":
    main()
