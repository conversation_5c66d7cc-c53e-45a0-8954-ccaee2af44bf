"""
此脚本用于复制和处理租户目录结构，主要功能包括:
1. 基于科伦目录创建新的租户目录
2. 重命名narrative目录下的Prompt文件
3. 更新meta.json中的租户名称
"""

import json
import os
import shutil


def format_json(json_str):
    """
    格式化JSON字符串，使其具有更好的可读性
    
    Args:
        json_str: 需要格式化的JSON字符串
    
    Returns:
        格式化后的JSON字符串，保持中文编码，使用2空格缩进
    """
    return json.dumps(json.loads(json_str), ensure_ascii=False, indent=2)


def process_files(root_path, new_names):
    """
    处理文件夹复制和重命名操作的主要函数
    
    Args:
        root_path: 项目根目录路径
        new_names: 新租户名称列表
    """
    prompts_path = os.path.join(root_path, 'prompts-test')

    for new_name in new_names:
        # 遍历prompts-test下的所有子目录
        for subdir in os.listdir(prompts_path):
            subdir_path = os.path.join(prompts_path, subdir)
            if os.path.isdir(subdir_path):
                # 查找科伦目录作为模板
                kelun_path = os.path.join(subdir_path, 'kelun')
                if os.path.exists(kelun_path):
                    new_path = os.path.join(subdir_path, new_name)
                    if os.path.exists(new_path):
                        # 如果是narrative目录，需要特殊处理Prompt文件的重命名
                        if subdir == 'narrative':
                            meta_dir = os.path.join(new_path, 'meta')
                            if os.path.exists(meta_dir):
                                for file in os.listdir(meta_dir):
                                    if file.endswith('.md'):
                                        old_file_path = os.path.join(meta_dir, file)
                                        try:
                                            # 提取Prompt序号并生成新文件名
                                            prompt_num = file.split('Prompt_')[1]
                                            new_file_name = f'{new_name}报告描述_Prompt_{prompt_num}'
                                            new_file_path = os.path.join(meta_dir, new_file_name)
                                            os.rename(old_file_path, new_file_path)
                                            print(f'已重命名文件: {file} -> {new_file_name}')
                                        except Exception as e:
                                            print(f"重命名文件 {file} 时出错: {str(e)}")
                        # 删除已存在的目标目录
                        shutil.rmtree(new_path)
                    
                    # 复制科伦目录结构到新租户目录
                    shutil.copytree(kelun_path, new_path)
                    print(f'已复制文件夹: {kelun_path} -> {new_path}')

                    # 更新meta.json中的租户名称
                    meta_json_path = os.path.join(new_path, 'dict', 'meta.json')
                    if os.path.exists(meta_json_path):
                        with open(meta_json_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        # 替换json内容中的"科伦"为新租户名称
                        str_data = json.dumps(data, ensure_ascii=False)
                        str_data = str_data.replace('科伦', new_name)
                        # 格式化并保存更新后的JSON
                        formatted_json = format_json(str_data)
                        with open(meta_json_path, 'w', encoding='utf-8') as f:
                            f.write(formatted_json)
                        print(f'已更新并格式化meta.json: {meta_json_path}')

                    # 处理narrative目录下的Prompt文件重命名
                    if subdir == 'narrative':
                        meta_dir = os.path.join(new_path, 'meta')
                        if os.path.exists(meta_dir):
                            for file in os.listdir(meta_dir):
                                if file.endswith('.md'):
                                    old_file_path = os.path.join(meta_dir, file)
                                    try:
                                        prompt_num = file.split('Prompt_')[1]
                                        new_file_name = f'{new_name}报告描述_Prompt_{prompt_num}'
                                        new_file_path = os.path.join(meta_dir, new_file_name)
                                        os.rename(old_file_path, new_file_path)
                                        print(f'已重命名文件: {file} -> {new_file_name}')
                                    except Exception as e:
                                        print(f"重命名文件 {file} 时出错: {str(e)}")


def main():
    """
    主函数，设置项目路径和需要处理的新租户名称列表
    """
    # 设置项目根目录路径
    project_path = r'C:\Users\<USER>\IdeaProjects\pv-model-service'

    # 配置需要创建的新租户名称列表
    new_folder_names = [
        # 'AST',
        # 'BGB',
        # 'Cali',
        # 'CNBGVG',
        # 'Curon',
        # 'Eluminex',
        # 'HZCTI',
        # 'LG Chem',
        # 'Liushun',
        # 'LYNK',
        # 'Medinno',
        # 'NewBay',
        # 'NFS',
        # 'NucMito',
        # 'Oricell',
        # 'SN',
        # 'Yuejia Pharm',
        # 'ZHB',
        # 'ZMC'
        # 'DEFAULT',
        # 'ALS'
        'LaNova'
    ]

    try:
        process_files(project_path, new_folder_names)
        print("所有文件处理完成！")
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")


if __name__ == "__main__":
    main()
