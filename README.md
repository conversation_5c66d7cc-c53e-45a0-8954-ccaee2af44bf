## python项目依赖更新

## 使用 uv 管理 Python 依赖

[uv](https://github.com/astral-sh/uv) 是一个非常快速的 Python 包安装器和解析器，旨在替代 `pip` 和 `pip-tools`。

### 常用命令

```bash
# 安装 uv (如果尚未安装)
# 参考官方文档: https://astral.sh/uv/install.sh
# 或者使用 pip:
pip install uv

# 根据pyproject.tom、uv.lock自动安装依赖包
uv sync
 
# 添加依赖
uv add package-name

# 更新全部依赖
uv lock --upgrade
uv sync

# 生成 requirements.txt
uv pip compile pyproject.toml -o requirements.txt
```

## 本地运行

### 运行模式

由于插件是跨域请求的后端，服务器上由nginx进行CORS的返回头的处理，本地运行需要添加命令参数

```shell
deploy_mode = local
```

### 热部署

Python支持热更新，需要通过以下命令参数进行开启

```shell
deploy_reload = local
```

