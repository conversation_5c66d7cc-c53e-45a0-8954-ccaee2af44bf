#!/usr/bin/env python3
"""
简化版本的 FastAPI 应用，用于测试基本功能
"""

import os
import sys
from pathlib import Path

# 添加 src 目录到 Python 路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

try:
    from fastapi import FastAPI
    from fastapi.responses import JSONResponse
    import uvicorn
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False

def create_simple_app():
    """创建一个简单的 FastAPI 应用"""
    if not FASTAPI_AVAILABLE:
        print("FastAPI 未安装，请先安装依赖：pip install fastapi uvicorn")
        return None
    
    app = FastAPI(title="PV Model Service", version="0.1.0")
    
    @app.get("/")
    async def root():
        return {"message": "PV Model Service is running!", "status": "ok"}
    
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "service": "pv-model-service"}
    
    @app.get("/api/test")
    async def test_endpoint():
        return {
            "message": "Test endpoint working",
            "environment": {
                "deploy_mode": os.getenv("deploy_mode", "not_set"),
                "api_port": os.getenv("API_PORT", "not_set")
            }
        }
    
    return app

def main():
    """主函数"""
    print("=== PV Model Service 启动器 ===")
    print(f"Python 版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查环境变量
    env_file = Path(".env")
    if env_file.exists():
        print(f"✓ 找到环境配置文件: {env_file}")
        try:
            from dotenv import load_dotenv
            load_dotenv()
            print("✓ 环境变量已加载")
        except ImportError:
            print("⚠ python-dotenv 未安装，手动读取环境变量")
            # 手动读取 .env 文件
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
    else:
        print("⚠ 未找到 .env 文件")
    
    # 检查关键环境变量
    deploy_mode = os.getenv('deploy_mode', 'production')
    api_port = int(os.getenv('API_PORT', 2189))
    
    print(f"部署模式: {deploy_mode}")
    print(f"API 端口: {api_port}")
    
    if FASTAPI_AVAILABLE:
        print("✓ FastAPI 可用，启动完整服务")
        app = create_simple_app()
        if app:
            print(f"🚀 启动服务在 http://localhost:{api_port}")
            if deploy_mode == 'local':
                # 本地开发模式，不使用 reload
                print("本地开发模式启动")
                uvicorn.run(
                    app,
                    host="0.0.0.0",
                    port=api_port,
                    reload=False
                )
            else:
                # 生产模式
                uvicorn.run(
                    app,
                    host="0.0.0.0",
                    port=api_port,
                    reload=False
                )
    else:
        print("❌ FastAPI 不可用")
        print("\n请安装依赖:")
        print("pip install fastapi uvicorn python-dotenv")
        print("\n或者运行:")
        print("pip install -r requirements.txt")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
