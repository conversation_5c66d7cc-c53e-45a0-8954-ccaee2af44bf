import os
from contextlib import asynccontextmanager

import uvicorn
from dotenv import load_dotenv
from fastapi import FastAPI
from starlette.middleware.cors import CORSMiddleware

load_dotenv()

# 尝试导入可选依赖
try:
    import fastapi_cdn_host
    CDN_HOST_AVAILABLE = True
except ImportError:
    CDN_HOST_AVAILABLE = False
    print("⚠ fastapi_cdn_host 未安装，跳过 CDN 配置")

try:
    from src.api.router import router
    ROUTER_AVAILABLE = True
except ImportError:
    ROUTER_AVAILABLE = False
    print("⚠ 路由模块导入失败，使用基础路由")

try:
    from src.cache.init_cache import init_all_caches
    CACHE_AVAILABLE = True
except ImportError:
    CACHE_AVAILABLE = False
    print("⚠ 缓存模块导入失败，跳过缓存初始化")

@asynccontextmanager
async def lifespan(app_param: FastAPI):
    # 应用启动前的初始化操作
    if CACHE_AVAILABLE:
        try:
            init_all_caches()
            print("✓ 缓存初始化完成")
        except Exception as e:
            print(f"⚠ 缓存初始化失败: {e}")

    # 利用 yield 分隔启动和关闭逻辑
    yield

    # 应用关闭前的清理逻辑（如果有）
    pass

app = FastAPI(
    title="PV Model Service",
    version="0.1.0",
    lifespan=lifespan
)

# 配置 CDN Host（如果可用）
if CDN_HOST_AVAILABLE:
    try:
        fastapi_cdn_host.patch_docs(app)
        print("✓ CDN Host 配置完成")
    except Exception as e:
        print(f"⚠ CDN Host 配置失败: {e}")

# 仅当 deploy_mode 环境变量的值为 'local' 时添加中间件
if os.getenv('deploy_mode') == 'local':
    app.add_middleware(
        CORSMiddleware,  # type: ignore
        allow_origins=['*'],
        allow_credentials=True,
        allow_methods=['*'],
        allow_headers=['*'],
    )
    print("✓ CORS 中间件已配置")

# 添加基础路由
@app.get("/")
async def root():
    return {
        "message": "PV Model Service is running!",
        "status": "ok",
        "version": "0.1.0"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "pv-model-service",
        "modules": {
            "cdn_host": CDN_HOST_AVAILABLE,
            "router": ROUTER_AVAILABLE,
            "cache": CACHE_AVAILABLE
        }
    }

# 包含主路由（如果可用）
if ROUTER_AVAILABLE:
    try:
        app.include_router(router)
        print("✓ 主路由已加载")
    except Exception as e:
        print(f"⚠ 主路由加载失败: {e}")

def start():
    """
    启动服务
    """
    print("=== PV Model Service 启动 ===")
    print(f"部署模式: {os.getenv('deploy_mode', 'production')}")
    print(f"API 端口: {os.getenv('API_PORT', 2189)}")
    print(f"热重载: {os.getenv('deploy_reload') == 'local'}")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        reload=False,  # 暂时禁用热重载避免问题
        port=int(os.getenv("API_PORT", 2189)),
    )

if __name__ == '__main__':
    start()
